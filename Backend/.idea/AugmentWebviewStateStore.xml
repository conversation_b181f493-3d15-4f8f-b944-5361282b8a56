<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="eyJjdXJyZW50Q29udmVyc2F0aW9uSWQiOiI3ODIzYmUyZS0zZjE4LTQ5MDctOTc0Yy0wZjIyNTA0NjIxMjQiLCJjb252ZXJzYXRpb25zIjp7IjhmOWM5ZmVlLThkMDYtNDdlNC05MmI4LTY3MzlmNTUzMzc4NyI6eyJpZCI6IjhmOWM5ZmVlLThkMDYtNDdlNC05MmI4LTY3MzlmNTUzMzc4NyIsImNyZWF0ZWRBdElzbyI6IjIwMjUtMDUtMjZUMDY6NDk6NTcuMDMyWiIsImxhc3RJbnRlcmFjdGVkQXRJc28iOiIyMDI1LTA1LTI2VDA2OjQ5OjU3LjAzMloiLCJjaGF0SGlzdG9yeSI6W10sImZlZWRiYWNrU3RhdGVzIjp7fSwidG9vbFVzZVN0YXRlcyI6e30sImRyYWZ0RXhjaGFuZ2UiOnsicmVxdWVzdF9tZXNzYWdlIjoiIiwicmljaF90ZXh0X2pzb25fcmVwciI6eyJ0eXBlIjoiZG9jIiwiY29udGVudCI6W3sidHlwZSI6InBhcmFncmFwaCJ9XX0sIm1lbnRpb25lZF9pdGVtcyI6W10sInN0YXR1cyI6ImRyYWZ0In0sInJlcXVlc3RJZHMiOltdLCJpc1Bpbm5lZCI6ZmFsc2UsImlzU2hhcmVhYmxlIjpmYWxzZSwiZXh0cmFEYXRhIjp7Imhhc0RpcnR5RWRpdHMiOmZhbHNlfSwicGVyc29uYVR5cGUiOjB9LCI4MjU4NGUzNi0xNDJhLTQxMDUtYjUwNi02MGZmNzU3MzUzNDIiOnsiaWQiOiI4MjU4NGUzNi0xNDJhLTQxMDUtYjUwNi02MGZmNzU3MzUzNDIiLCJjcmVhdGVkQXRJc28iOiIyMDI1LTA1LTI2VDA2OjQ5OjU3LjA5MVoiLCJsYXN0SW50ZXJhY3RlZEF0SXNvIjoiMjAyNS0wNS0yNlQwNjo0OTo1Ny4wOTFaIiwiY2hhdEhpc3RvcnkiOltdLCJmZWVkYmFja1N0YXRlcyI6e30sInRvb2xVc2VTdGF0ZXMiOnt9LCJkcmFmdEV4Y2hhbmdlIjp7InJlcXVlc3RfbWVzc2FnZSI6IiIsInJpY2hfdGV4dF9qc29uX3JlcHIiOnsidHlwZSI6ImRvYyIsImNvbnRlbnQiOlt7InR5cGUiOiJwYXJhZ3JhcGgifV19LCJtZW50aW9uZWRfaXRlbXMiOltdLCJzdGF0dXMiOiJkcmFmdCJ9LCJyZXF1ZXN0SWRzIjpbXSwiaXNQaW5uZWQiOmZhbHNlLCJpc1NoYXJlYWJsZSI6ZmFsc2UsImV4dHJhRGF0YSI6eyJoYXNEaXJ0eUVkaXRzIjpmYWxzZX0sInBlcnNvbmFUeXBlIjowLCJyb290VGFza1V1aWQiOiI2MDYzMWFhNC02Yzc4LTRhM2EtOTUzMi1mNmUzYjBhYmZkZmIifSwiMDk3ZDQ4NTAtYzEzYi00Y2E4LWEyZDYtMzJlMTU3MmZmOTM0Ijp7ImlkIjoiMDk3ZDQ4NTAtYzEzYi00Y2E4LWEyZDYtMzJlMTU3MmZmOTM0IiwiY3JlYXRlZEF0SXNvIjoiMjAyNS0wNS0yNlQwNjo0OTo1OS43NjVaIiwibGFzdEludGVyYWN0ZWRBdElzbyI6IjIwMjUtMDUtMjZUMDc6MTg6MTguMjU2WiIsImNoYXRIaXN0b3J5IjpbeyJyZXF1ZXN0X2lkIjoiNTBhZWUwZGQtN2NlYy00ZjVhLTg5NjEtMTRiZjJlZmRkMjJjIiwidXVpZCI6ImRlMTk3ODJjLWFhNjgtNGJlZi1iN2Y2LWEwODI3MGJmNWE1YyIsImNoYXRJdGVtVHlwZSI6ImFnZW50aWMtY2hlY2twb2ludC1kZWxpbWl0ZXIiLCJzdGF0dXMiOiJzdWNjZXNzIiwiZnJvbVRpbWVzdGFtcCI6MCwidG9UaW1lc3RhbXAiOjE3NDgyNDIxOTk3NjYsInNlZW5fc3RhdGUiOiJzZWVuIn0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjBiMGVkNTJhLTgyYTYtNDg1My04MjQ4LWM2Yjc2Y2JmZDdjZiIsInJlcXVlc3RfbWVzc2FnZSI6ImFmdGVyIGluaXRpYWxpemVkIHRoZSBjYWxsLCB0aGUgY2xpZW50IGdldCB0aGUgdG9rZW4gYW5kIHRyeSB0byBqb2luIHRoZSBjYWxsLiBidXQgc2VlbSBpdCdzIGludmFsaWRcbkkvZmx1dHRlciAoMzA2MTMpOiBBZ29yYSBlcnJvcjogRXJyb3JDb2RlVHlwZS5lcnJJbnZhbGlkVG9rZW4sIG1lc3NhZ2U6IFxuSS9mbHV0dGVyICgzMDYxMyk6IENvbm5lY3Rpb24gc3RhdGUgY2hhbmdlZDogQ29ubmVjdGlvblN0YXRlVHlwZS5jb25uZWN0aW9uU3RhdGVGYWlsZWQsIHJlYXNvbjogQ29ubmVjdGlvbkNoYW5nZWRSZWFzb25UeXBlLmNvbm5lY3Rpb25DaGFuZ2VkSW52YWxpZFRva2VuIiwicmljaF90ZXh0X2pzb25fcmVwciI6eyJ0eXBlIjoiZG9jIiwiY29udGVudCI6W3sidHlwZSI6InBhcmFncmFwaCIsImNvbnRlbnQiOlt7InR5cGUiOiJ0ZXh0IiwidGV4dCI6ImFmdGVyIGluaXRpYWxpemVkIHRoZSBjYWxsLCB0aGUgY2xpZW50IGdldCB0aGUgdG9rZW4gYW5kIHRyeSB0byBqb2luIHRoZSBjYWxsLiBidXQgc2VlbSBpdCdzIGludmFsaWQifSx7InR5cGUiOiJoYXJkQnJlYWsifSx7InR5cGUiOiJ0ZXh0IiwidGV4dCI6IkkvZmx1dHRlciAoMzA2MTMpOiBBZ29yYSBlcnJvcjogRXJyb3JDb2RlVHlwZS5lcnJJbnZhbGlkVG9rZW4sIG1lc3NhZ2U6ICJ9LHsidHlwZSI6ImhhcmRCcmVhayJ9LHsidHlwZSI6InRleHQiLCJ0ZXh0IjoiSS9mbHV0dGVyICgzMDYxMyk6IENvbm5lY3Rpb24gc3RhdGUgY2hhbmdlZDogQ29ubmVjdGlvblN0YXRlVHlwZS5jb25uZWN0aW9uU3RhdGVGYWlsZWQsIHJlYXNvbjogQ29ubmVjdGlvbkNoYW5nZWRSZWFzb25UeXBlLmNvbm5lY3Rpb25DaGFuZ2VkSW52YWxpZFRva2VuIn1dfV19LCJtZW50aW9uZWRfaXRlbXMiOltdLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFVNE5rYUE4U0ZZZ0N6a2NrWk04SENHIiwidG9vbF9uYW1lIjoidmlldyIsImlucHV0X2pzb24iOiJ7XCJwYXRoXCI6IFwic2VydmljZXMvYWdvcmFfc2VydmljZS5nb1wifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IkknbGwgaGVscCB5b3UgaW52ZXN0aWdhdGUgdGhlIGludmFsaWQgdG9rZW4gaXNzdWUgd2l0aCB0aGUgQWdvcmEgY2FsbC4gTGV0IG1lIGZpcnN0IGV4YW1pbmUgdGhlIGN1cnJlbnQgQWdvcmEgc2VydmljZSBpbXBsZW1lbnRhdGlvbiBhbmQgdGhlbiBsb29rIGludG8gaG93IHRva2VucyBhcmUgZ2VuZXJhdGVkIGFuZCB1c2VkLlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MCwidHlwZSI6MCwidGV4dF9ub2RlIjp7ImNvbnRlbnQiOiJhZnRlciBpbml0aWFsaXplZCB0aGUgY2FsbCwgdGhlIGNsaWVudCBnZXQgdGhlIHRva2VuIGFuZCB0cnkgdG8gam9pbiB0aGUgY2FsbC4gYnV0IHNlZW0gaXQncyBpbnZhbGlkXG5JL2ZsdXR0ZXIgKDMwNjEzKTogQWdvcmEgZXJyb3I6IEVycm9yQ29kZVR5cGUuZXJySW52YWxpZFRva2VuLCBtZXNzYWdlOiBcbkkvZmx1dHRlciAoMzA2MTMpOiBDb25uZWN0aW9uIHN0YXRlIGNoYW5nZWQ6IENvbm5lY3Rpb25TdGF0ZVR5cGUuY29ubmVjdGlvblN0YXRlRmFpbGVkLCByZWFzb246IENvbm5lY3Rpb25DaGFuZ2VkUmVhc29uVHlwZS5jb25uZWN0aW9uQ2hhbmdlZEludmFsaWRUb2tlbiJ9fSx7ImlkIjoxLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlfX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA2OjUwOjIxLjkyM1oiLCJyZXNwb25zZV90ZXh0IjoiSSdsbCBoZWxwIHlvdSBpbnZlc3RpZ2F0ZSB0aGUgaW52YWxpZCB0b2tlbiBpc3N1ZSB3aXRoIHRoZSBBZ29yYSBjYWxsLiBMZXQgbWUgZmlyc3QgZXhhbWluZSB0aGUgY3VycmVudCBBZ29yYSBzZXJ2aWNlIGltcGxlbWVudGF0aW9uIGFuZCB0aGVuIGxvb2sgaW50byBob3cgdG9rZW5zIGFyZSBnZW5lcmF0ZWQgYW5kIHVzZWQuXG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMTdiNGNlMjQtOTdiNC00NWVjLWE3MmItMWUwN2ZkY2ExM2I1IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxSlBhc3lpbUI2SnU4cUM5TDhnNWRHaCIsInRvb2xfbmFtZSI6ImNvZGViYXNlLXJldHJpZXZhbCIsImlucHV0X2pzb24iOiJ7XCJpbmZvcm1hdGlvbl9yZXF1ZXN0XCI6IFwiQWdvcmEgY29uZmlndXJhdGlvbiBzZXR0aW5ncyBpbmNsdWRpbmcgQXBwSUQsIEFwcENlcnRpZmljYXRlLCBhbmQgaG93IHRoZXkgYXJlIGxvYWRlZCBmcm9tIGVudmlyb25tZW50IHZhcmlhYmxlcyBvciBjb25maWcgZmlsZXNcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiJOb3cgbGV0IG1lIGNoZWNrIHRoZSBjb25maWd1cmF0aW9uIHRvIHNlZSBob3cgdGhlIEFnb3JhIGNyZWRlbnRpYWxzIGFyZSBzZXQgdXA6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJIZXJlJ3MgdGhlIHJlc3VsdCBvZiBydW5uaW5nIGBjYXQgLW5gIG9uIHNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ286XG4gICAgIDFcdHBhY2thZ2Ugc2VydmljZXNcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJCYWNrZW5kL2NvbmZpZ1wiXG4gICAgIDVcdFx0XCJmbXRcIlxuICAgICA2XHRcdFwibG9nXCJcbiAgICAgN1x0XHRcInN0cmNvbnZcIlxuICAgICA4XHRcdFwidGltZVwiXG4gICAgIDlcdFxuICAgIDEwXHRcdFwiZ2l0aHViLmNvbS9BZ29yYUlPL1Rvb2xzL0R5bmFtaWNLZXkvQWdvcmFEeW5hbWljS2V5L2dvL3NyYy9ydGN0b2tlbmJ1aWxkZXIyXCJcbiAgICAxMVx0KVxuICAgIDEyXHRcbiAgICAxM1x0Ly8gQWdvcmFTZXJ2aWNlIGhhbmRsZXMgQWdvcmEgUlRDIG9wZXJhdGlvbnNcbiAgICAxNFx0dHlwZSBBZ29yYVNlcnZpY2Ugc3RydWN0IHtcbiAgICAxNVx0XHRjb25maWcgKmNvbmZpZy5Db25maWdcbiAgICAxNlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNvbnRhaW46XG4gICAgMTdcdFx0Ly8gLSBBZ29yYSBzZXJ2aWNlIGluc3RhbmNlXG4gICAgMThcdFx0Ly8gLSBNZWRpYSBub2RlIGZhY3RvcnlcbiAgICAxOVx0XHQvLyAtIEFjdGl2ZSBjb25uZWN0aW9ucyBtYXBcbiAgICAyMFx0XHQvLyAtIEF1ZGlvL1ZpZGVvIG9ic2VydmVyc1xuICAgIDIxXHR9XG4gICAgMjJcdFxuICAgIDIzXHQvLyBUb2tlblJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgZm9yIEFnb3JhIHRva2VuXG4gICAgMjRcdHR5cGUgVG9rZW5SZXF1ZXN0IHN0cnVjdCB7XG4gICAgMjVcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMjZcdFx0VXNlcklEICAgICAgc3RyaW5nIGBqc29uOlwidXNlcl9pZFwiYFxuICAgIDI3XHRcdFJvbGUgICAgICAgIGludCAgICBganNvbjpcInJvbGVcImAgLy8gMSA9IHB1Ymxpc2hlciwgMiA9IHN1YnNjcmliZXJcbiAgICAyOFx0fVxuICAgIDI5XHRcbiAgICAzMFx0Ly8gVG9rZW5SZXNwb25zZSByZXByZXNlbnRzIEFnb3JhIHRva2VuIHJlc3BvbnNlXG4gICAgMzFcdHR5cGUgVG9rZW5SZXNwb25zZSBzdHJ1Y3Qge1xuICAgIDMyXHRcdFRva2VuICAgICBzdHJpbmcgYGpzb246XCJ0b2tlblwiYFxuICAgIDMzXHRcdEV4cGlyZXNBdCBpbnQ2NCAgYGpzb246XCJleHBpcmVzX2F0XCJgXG4gICAgMzRcdH1cbiAgICAzNVx0XG4gICAgMzZcdC8vIE5ld0Fnb3JhU2VydmljZSBjcmVhdGVzIGEgbmV3IEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAzN1x0ZnVuYyBOZXdBZ29yYVNlcnZpY2UoY2ZnICpjb25maWcuQ29uZmlnKSAqQWdvcmFTZXJ2aWNlIHtcbiAgICAzOFx0XHRzZXJ2aWNlIDo9ICZBZ29yYVNlcnZpY2V7XG4gICAgMzlcdFx0XHRjb25maWc6IGNmZyxcbiAgICA0MFx0XHR9XG4gICAgNDFcdFxuICAgIDQyXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIGluaXRpYWxpemUgQWdvcmEgU0RLIGhlcmU6XG4gICAgNDNcdFx0Ly8gMS4gSW5pdGlhbGl6ZSBBZ29yYSBzZXJ2aWNlIHdpdGggYXBwIElEXG4gICAgNDRcdFx0Ly8gMi4gQ3JlYXRlIG1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDQ1XHRcdC8vIDMuIFNldCB1cCBhdWRpby92aWRlbyBwcm9jZXNzaW5nIGNhbGxiYWNrc1xuICAgIDQ2XHRcbiAgICA0N1x0XHRsb2cuUHJpbnRmKFwi8J+OryBBZ29yYSBzZXJ2aWNlIGluaXRpYWxpemVkIHdpdGggQXBwIElEOiAlc1wiLCBjZmcuQWdvcmEuQXBwSUQpXG4gICAgNDhcdFx0cmV0dXJuIHNlcnZpY2VcbiAgICA0OVx0fVxuICAgIDUwXHRcbiAgICA1MVx0Ly8gR2VuZXJhdGVUb2tlbiBnZW5lcmF0ZXMgYW4gQWdvcmEgUlRDIHRva2VuIGZvciBjaGFubmVsIGFjY2Vzc1xuICAgIDUyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZW5lcmF0ZVRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgKCpUb2tlblJlc3BvbnNlLCBlcnJvcikge1xuICAgIDUzXHRcdGxvZy5QcmludGYoXCLwn5SRIEdlbmVyYXRpbmcgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB1c2VyOiAlcywgcm9sZTogJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA1NFx0XG4gICAgNTVcdFx0Ly8gVG9rZW4gZXhwaXJlcyBpbiAyNCBob3Vyc1xuICAgIDU2XHRcdGV4cGlyYXRpb25UaW1lIDo9IHRpbWUuTm93KCkuQWRkKDI0ICogdGltZS5Ib3VyKS5Vbml4KClcbiAgICA1N1x0XG4gICAgNThcdFx0dmFyIHRva2VuIHN0cmluZ1xuICAgIDU5XHRcdHZhciBlcnIgZXJyb3JcbiAgICA2MFx0XG4gICAgNjFcdFx0aWYgYXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlICE9IFwiXCIge1xuICAgIDYyXHRcdFx0Ly8gR2VuZXJhdGUgcmVhbCB0b2tlbiB3aXRoIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA2M1x0XHRcdHRva2VuLCBlcnIgPSBhcy5nZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB1aW50MzIoZXhwaXJhdGlvblRpbWUpKVxuICAgIDY0XHRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjVcdFx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gZ2VuZXJhdGUgdG9rZW46ICV3XCIsIGVycilcbiAgICA2Nlx0XHRcdH1cbiAgICA2N1x0XHRcdGxvZy5QcmludGYoXCLinIUgR2VuZXJhdGVkIHJlYWwgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgIDY4XHRcdH0gZWxzZSB7XG4gICAgNjlcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgIDcwXHRcdFx0dG9rZW4gPSBhcy5nZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDcxXHRcdFx0bG9nLlByaW50ZihcIuKaoO+4jyBbTU9DS10gVXNpbmcgbW9jayB0b2tlbiAobm8gYXBwIGNlcnRpZmljYXRlIHByb3ZpZGVkKVwiKVxuICAgIDcyXHRcdH1cbiAgICA3M1x0XG4gICAgNzRcdFx0cmVzcG9uc2UgOj0gJlRva2VuUmVzcG9uc2V7XG4gICAgNzVcdFx0XHRUb2tlbjogICAgIHRva2VuLFxuICAgIDc2XHRcdFx0RXhwaXJlc0F0OiBleHBpcmF0aW9uVGltZSxcbiAgICA3N1x0XHR9XG4gICAgNzhcdFxuICAgIDc5XHRcdGxvZy5QcmludGYoXCLinIUgVG9rZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgODBcdFx0cmV0dXJuIHJlc3BvbnNlLCBuaWxcbiAgICA4MVx0fVxuICAgIDgyXHRcbiAgICA4M1x0Ly8gZ2VuZXJhdGVSZWFsVG9rZW4gZ2VuZXJhdGVzIGEgcmVhbCBBZ29yYSB0b2tlbiB1c2luZyB0aGUgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDg0XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQsIGV4cGlyYXRpb24gdWludDMyKSAoc3RyaW5nLCBlcnJvcikge1xuICAgIDg1XHRcdC8vIENvbnZlcnQgdXNlcklEIHRvIHVpbnQzMiAoQWdvcmEgZXhwZWN0cyBudW1lcmljIFVJRClcbiAgICA4Nlx0XHR2YXIgdWlkIHVpbnQzMlxuICAgIDg3XHRcdGlmIHVzZXJJRCAhPSBcIlwiIHtcbiAgICA4OFx0XHRcdGlmIHBhcnNlZFVJRCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VVaW50KHVzZXJJRCwgMTAsIDMyKTsgZXJyID09IG5pbCB7XG4gICAgODlcdFx0XHRcdHVpZCA9IHVpbnQzMihwYXJzZWRVSUQpXG4gICAgOTBcdFx0XHR9IGVsc2Uge1xuICAgIDkxXHRcdFx0XHQvLyBJZiB1c2VySUQgaXMgbm90IG51bWVyaWMsIGdlbmVyYXRlIGEgaGFzaC1iYXNlZCBVSURcbiAgICA5Mlx0XHRcdFx0dWlkID0gdWludDMyKGxlbih1c2VySUQpKSAvLyBTaW1wbGUgZmFsbGJhY2ssIGluIHByb2R1Y3Rpb24gdXNlIGEgcHJvcGVyIGhhc2hcbiAgICA5M1x0XHRcdH1cbiAgICA5NFx0XHR9IGVsc2Uge1xuICAgIDk1XHRcdFx0dWlkID0gMCAvLyBMZXQgQWdvcmEgYXNzaWduIGEgVUlEXG4gICAgOTZcdFx0fVxuICAgIDk3XHRcbiAgICA5OFx0XHQvLyBDb252ZXJ0IHJvbGUgdG8gQWdvcmEgcm9sZSB0eXBlXG4gICAgOTlcdFx0dmFyIGFnb3JhUm9sZSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVcbiAgIDEwMFx0XHRpZiByb2xlID09IDEge1xuICAgMTAxXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlUHVibGlzaGVyXG4gICAxMDJcdFx0fSBlbHNlIHtcbiAgIDEwM1x0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVN1YnNjcmliZXJcbiAgIDEwNFx0XHR9XG4gICAxMDVcdFxuICAgMTA2XHRcdC8vIEdlbmVyYXRlIHRva2VuIHVzaW5nIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgIDEwN1x0XHR0b2tlbiwgZXJyIDo9IHJ0Y3Rva2VuYnVpbGRlcjIuQnVpbGRUb2tlbldpdGhVaWQoXG4gICAxMDhcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwSUQsXG4gICAxMDlcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUsXG4gICAxMTBcdFx0XHRjaGFubmVsTmFtZSxcbiAgIDExMVx0XHRcdHVpZCxcbiAgIDExMlx0XHRcdGFnb3JhUm9sZSxcbiAgIDExM1x0XHRcdGV4cGlyYXRpb24sXG4gICAxMTRcdFx0XHRleHBpcmF0aW9uLCAvLyBQcml2aWxlZ2UgZXhwaXJlZCB0aW1lc3RhbXAgKHNhbWUgYXMgdG9rZW4gZXhwaXJhdGlvbilcbiAgIDExNVx0XHQpXG4gICAxMTZcdFxuICAgMTE3XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTE4XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gYnVpbGQgQWdvcmEgdG9rZW46ICV3XCIsIGVycilcbiAgIDExOVx0XHR9XG4gICAxMjBcdFxuICAgMTIxXHRcdHJldHVybiB0b2tlbiwgbmlsXG4gICAxMjJcdH1cbiAgIDEyM1x0XG4gICAxMjRcdC8vIGdlbmVyYXRlTW9ja1Rva2VuIGdlbmVyYXRlcyBhIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAxMjVcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgc3RyaW5nIHtcbiAgIDEyNlx0XHR0aW1lc3RhbXAgOj0gdGltZS5Ob3coKS5Vbml4KClcbiAgIDEyN1x0XHRyZXR1cm4gZm10LlNwcmludGYoXCJtb2NrX3Rva2VuXyVzXyVzXyVkXyVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHRpbWVzdGFtcClcbiAgIDEyOFx0fVxuICAgMTI5XHRcbiAgIDEzMFx0Ly8gSm9pbkNoYW5uZWwgam9pbnMgYW4gQWdvcmEgY2hhbm5lbCBmb3IgQUkgcHJvY2Vzc2luZ1xuICAgMTMxXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBKb2luQ2hhbm5lbChjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZykgZXJyb3Ige1xuICAgMTMyXHRcdGxvZy5QcmludGYoXCLwn5SXIFtNT0NLXSBBSSBqb2luaW5nIEFnb3JhIGNoYW5uZWw6ICVzIGFzIHVzZXI6ICVzXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQpXG4gICAxMzNcdFxuICAgMTM0XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxMzVcdFx0Ly8gMS4gQ3JlYXRlIFJUQyBjb25uZWN0aW9uXG4gICAxMzZcdFx0Ly8gMi4gU2V0IHVwIGxvY2FsIHVzZXJcbiAgIDEzN1x0XHQvLyAzLiBDcmVhdGUgYXVkaW8gdHJhY2sgZm9yIEFJIHZvaWNlXG4gICAxMzhcdFx0Ly8gNC4gUmVnaXN0ZXIgYXVkaW8gZnJhbWUgb2JzZXJ2ZXJzIGZvciBwcm9jZXNzaW5nIGluY29taW5nIGF1ZGlvXG4gICAxMzlcdFx0Ly8gNS4gSm9pbiB0aGUgY2hhbm5lbFxuICAgMTQwXHRcdC8vIDYuIFN0YXJ0IGF1ZGlvIHByb2Nlc3NpbmcgcGlwZWxpbmVcbiAgIDE0MVx0XG4gICAxNDJcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvblxuICAgMTQzXHRcdHRpbWUuU2xlZXAoNTAwICogdGltZS5NaWxsaXNlY29uZCkgLy8gU2ltdWxhdGUgY29ubmVjdGlvbiB0aW1lXG4gICAxNDRcdFxuICAgMTQ1XHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEFJIHN1Y2Nlc3NmdWxseSBqb2luZWQgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNDZcdFx0cmV0dXJuIG5pbFxuICAgMTQ3XHR9XG4gICAxNDhcdFxuICAgMTQ5XHQvLyBMZWF2ZUNoYW5uZWwgbGVhdmVzIGFuIEFnb3JhIGNoYW5uZWxcbiAgIDE1MFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgTGVhdmVDaGFubmVsKGNhbGxJRCBzdHJpbmcpIGVycm9yIHtcbiAgIDE1MVx0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAxNTJcdFx0bG9nLlByaW50ZihcIvCfkYsgW01PQ0tdIEFJIGxlYXZpbmcgQWdvcmEgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNTNcdFxuICAgMTU0XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxNTVcdFx0Ly8gMS4gU3RvcCBhdWRpbyBwcm9jZXNzaW5nXG4gICAxNTZcdFx0Ly8gMi4gVW5wdWJsaXNoIGF1ZGlvIHRyYWNrc1xuICAgMTU3XHRcdC8vIDMuIERpc2Nvbm5lY3QgZnJvbSBjaGFubmVsXG4gICAxNThcdFx0Ly8gNC4gQ2xlYW4gdXAgcmVzb3VyY2VzXG4gICAxNTlcdFxuICAgMTYwXHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb25cbiAgIDE2MVx0XHR0aW1lLlNsZWVwKDIwMCAqIHRpbWUuTWlsbGlzZWNvbmQpIC8vIFNpbXVsYXRlIGRpc2Nvbm5lY3Rpb24gdGltZVxuICAgMTYyXHRcbiAgIDE2M1x0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgbGVmdCBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE2NFx0XHRyZXR1cm4gbmlsXG4gICAxNjVcdH1cbiAgIDE2Nlx0XG4gICAxNjdcdC8vIFNlbmRBdWRpb1RvQ2hhbm5lbCBzZW5kcyBBSS1nZW5lcmF0ZWQgYXVkaW8gdG8gdGhlIGNoYW5uZWxcbiAgIDE2OFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgU2VuZEF1ZGlvVG9DaGFubmVsKGNhbGxJRCBzdHJpbmcsIGF1ZGlvRGF0YSBbXWJ5dGUpIGVycm9yIHtcbiAgIDE2OVx0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAxNzBcdFx0bG9nLlByaW50ZihcIvCflIogW01PQ0tdIFNlbmRpbmcgJWQgYnl0ZXMgb2YgYXVkaW8gdG8gY2hhbm5lbDogJXNcIiwgbGVuKGF1ZGlvRGF0YSksIGNoYW5uZWxOYW1lKVxuICAgMTcxXHRcbiAgIDE3Mlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTczXHRcdC8vIDEuIENvbnZlcnQgYXVkaW8gZGF0YSB0byByZXF1aXJlZCBmb3JtYXQgKFBDTSlcbiAgIDE3NFx0XHQvLyAyLiBQdXNoIGF1ZGlvIGZyYW1lcyB0byBBZ29yYSBhdWRpbyB0cmFja1xuICAgMTc1XHRcdC8vIDMuIEhhbmRsZSBhdWRpbyBlbmNvZGluZyBhbmQgdHJhbnNtaXNzaW9uXG4gICAxNzZcdFxuICAgMTc3XHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb24gLSBzaW11bGF0ZSBhdWRpbyB0cmFuc21pc3Npb24gdGltZVxuICAgMTc4XHRcdHRyYW5zbWlzc2lvblRpbWUgOj0gdGltZS5EdXJhdGlvbihsZW4oYXVkaW9EYXRhKS8xMDAwKSAqIHRpbWUuTWlsbGlzZWNvbmRcbiAgIDE3OVx0XHRpZiB0cmFuc21pc3Npb25UaW1lID4gMTAwKnRpbWUuTWlsbGlzZWNvbmQge1xuICAgMTgwXHRcdFx0dHJhbnNtaXNzaW9uVGltZSA9IDEwMCAqIHRpbWUuTWlsbGlzZWNvbmRcbiAgIDE4MVx0XHR9XG4gICAxODJcdFx0dGltZS5TbGVlcCh0cmFuc21pc3Npb25UaW1lKVxuICAgMTgzXHRcbiAgIDE4NFx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBdWRpbyBzZW50IHN1Y2Nlc3NmdWxseSB0byBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE4NVx0XHRyZXR1cm4gbmlsXG4gICAxODZcdH1cbiAgIDE4N1x0XG4gICAxODhcdC8vIFNldHVwQXVkaW9Qcm9jZXNzaW5nIHNldHMgdXAgYXVkaW8gcHJvY2Vzc2luZyBjYWxsYmFja3NcbiAgIDE4OVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgU2V0dXBBdWRpb1Byb2Nlc3NpbmcoY2FsbElEIHN0cmluZywgYXVkaW9DYWxsYmFjayBmdW5jKFtdYnl0ZSkgZXJyb3IpIGVycm9yIHtcbiAgIDE5MFx0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAxOTFcdFx0bG9nLlByaW50ZihcIvCfjpnvuI8gW01PQ0tdIFNldHRpbmcgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxOTJcdFxuICAgMTkzXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxOTRcdFx0Ly8gMS4gUmVnaXN0ZXIgYXVkaW8gZnJhbWUgb2JzZXJ2ZXJcbiAgIDE5NVx0XHQvLyAyLiBTZXQgdXAgVkFEIChWb2ljZSBBY3Rpdml0eSBEZXRlY3Rpb24pXG4gICAxOTZcdFx0Ly8gMy4gQ29uZmlndXJlIGF1ZGlvIGZvcm1hdCAoc2FtcGxlIHJhdGUsIGNoYW5uZWxzLCBldGMuKVxuICAgMTk3XHRcdC8vIDQuIFN0YXJ0IHByb2Nlc3NpbmcgaW5jb21pbmcgYXVkaW8gZnJhbWVzXG4gICAxOThcdFxuICAgMTk5XHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb24gLSBzaW11bGF0ZSByZWNlaXZpbmcgYXVkaW8gcGVyaW9kaWNhbGx5XG4gICAyMDBcdFx0Z28gYXMuc2ltdWxhdGVJbmNvbWluZ0F1ZGlvKGNhbGxJRCwgYXVkaW9DYWxsYmFjaylcbiAgIDIwMVx0XG4gICAyMDJcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQXVkaW8gcHJvY2Vzc2luZyBzZXR1cCBjb21wbGV0ZSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAyMDNcdFx0cmV0dXJuIG5pbFxuICAgMjA0XHR9XG4gICAyMDVcdFxuICAgMjA2XHQvLyBzaW11bGF0ZUluY29taW5nQXVkaW8gc2ltdWxhdGVzIHJlY2VpdmluZyBhdWRpbyBmcm9tIHRoZSBjdXN0b21lclxuICAgMjA3XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBzaW11bGF0ZUluY29taW5nQXVkaW8oY2FsbElEIHN0cmluZywgYXVkaW9DYWxsYmFjayBmdW5jKFtdYnl0ZSkgZXJyb3IpIHtcbiAgIDIwOFx0XHRsb2cuUHJpbnRmKFwi8J+OrSBbTU9DS10gU3RhcnRpbmcgYXVkaW8gc2ltdWxhdGlvbiBmb3IgY2FsbDogJXNcIiwgY2FsbElEKVxuICAgMjA5XHRcbiAgIDIxMFx0XHQvLyBTaW11bGF0ZSBjdXN0b21lciBzcGVha2luZyBldmVyeSA1LTEwIHNlY29uZHNcbiAgIDIxMVx0XHRmb3IgaSA6PSAwOyBpIDwgNTsgaSsrIHsgLy8gU2ltdWxhdGUgNSBjdXN0b21lciBtZXNzYWdlc1xuICAgMjEyXHRcdFx0dGltZS5TbGVlcCh0aW1lLkR1cmF0aW9uKDUraSoyKSAqIHRpbWUuU2Vjb25kKVxuICAgMjEzXHRcbiAgIDIxNFx0XHRcdC8vIEdlbmVyYXRlIG1vY2sgYXVkaW8gZGF0YVxuICAgMjE1XHRcdFx0bW9ja0F1ZGlvRGF0YSA6PSBtYWtlKFtdYnl0ZSwgMTYwMCkgLy8gU2ltdWxhdGUgMTAwbXMgb2YgMTZrSHogbW9ubyBhdWRpb1xuICAgMjE2XHRcdFx0Zm9yIGogOj0gcmFuZ2UgbW9ja0F1ZGlvRGF0YSB7XG4gICAyMTdcdFx0XHRcdG1vY2tBdWRpb0RhdGFbal0gPSBieXRlKChpKjEwMCArIGopICUgMjU2KVxuICAgMjE4XHRcdFx0fVxuICAgMjE5XHRcbiAgIDIyMFx0XHRcdGxvZy5QcmludGYoXCLwn46kIFtNT0NLXSBTaW11bGF0aW5nIGluY29taW5nIGF1ZGlvIGZvciBjYWxsICVzIChtZXNzYWdlICVkKVwiLCBjYWxsSUQsIGkrMSlcbiAgIDIyMVx0XG4gICAyMjJcdFx0XHRpZiBlcnIgOj0gYXVkaW9DYWxsYmFjayhtb2NrQXVkaW9EYXRhKTsgZXJyICE9IG5pbCB7XG4gICAyMjNcdFx0XHRcdGxvZy5QcmludGYoXCJFcnJvciBwcm9jZXNzaW5nIHNpbXVsYXRlZCBhdWRpbzogJXZcIiwgZXJyKVxuICAgMjI0XHRcdFx0XHRicmVha1xuICAgMjI1XHRcdFx0fVxuICAgMjI2XHRcdH1cbiAgIDIyN1x0XG4gICAyMjhcdFx0bG9nLlByaW50ZihcIvCfjq0gW01PQ0tdIEF1ZGlvIHNpbXVsYXRpb24gY29tcGxldGVkIGZvciBjYWxsOiAlc1wiLCBjYWxsSUQpXG4gICAyMjlcdH1cbiAgIDIzMFx0XG4gICAyMzFcdC8vIEdldENoYW5uZWxTdGF0cyByZXR1cm5zIGNoYW5uZWwgc3RhdGlzdGljc1xuICAgMjMyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZXRDaGFubmVsU3RhdHMoY2FsbElEIHN0cmluZykgKG1hcFtzdHJpbmddaW50ZXJmYWNle30sIGVycm9yKSB7XG4gICAyMzNcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMjM0XHRcdGxvZy5QcmludGYoXCLwn5OKIFtNT0NLXSBHZXR0aW5nIGNoYW5uZWwgc3RhdHMgZm9yOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDIzNVx0XG4gICAyMzZcdFx0Ly8gTW9jayBzdGF0aXN0aWNzXG4gICAyMzdcdFx0c3RhdHMgOj0gbWFwW3N0cmluZ11pbnRlcmZhY2V7fXtcbiAgIDIzOFx0XHRcdFwiY2hhbm5lbF9uYW1lXCI6ICAgICBjaGFubmVsTmFtZSxcbiAgIDIzOVx0XHRcdFwiY29ubmVjdGVkX3VzZXJzXCI6ICAyLCAvLyBBSSArIEN1c3RvbWVyXG4gICAyNDBcdFx0XHRcImF1ZGlvX2JpdHJhdGVcIjogICAgNjQwMDAsXG4gICAyNDFcdFx0XHRcInBhY2tldF9sb3NzX3JhdGVcIjogMC4wMSxcbiAgIDI0Mlx0XHRcdFwicnR0XCI6ICAgICAgICAgICAgIDQ1LCAvLyBSb3VuZCB0cmlwIHRpbWUgaW4gbXNcbiAgIDI0M1x0XHRcdFwiYXVkaW9fcXVhbGl0eVwiOiAgICBcImdvb2RcIixcbiAgIDI0NFx0XHRcdFwiY29ubmVjdGlvbl9zdGF0ZVwiOiBcImNvbm5lY3RlZFwiLFxuICAgMjQ1XHRcdH1cbiAgIDI0Nlx0XG4gICAyNDdcdFx0cmV0dXJuIHN0YXRzLCBuaWxcbiAgIDI0OFx0fVxuICAgMjQ5XHRcbiAgIDI1MFx0Ly8gZ2V0Q2hhbm5lbE5hbWUgZ2VuZXJhdGVzIGNoYW5uZWwgbmFtZSBmcm9tIGNhbGwgSURcbiAgIDI1MVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2V0Q2hhbm5lbE5hbWUoY2FsbElEIHN0cmluZykgc3RyaW5nIHtcbiAgIDI1Mlx0XHRyZXR1cm4gYXMuY29uZmlnLkFnb3JhLkNoYW5uZWxQcmVmaXggKyBjYWxsSURcbiAgIDI1M1x0fVxuICAgMjU0XHRcbiAgIDI1NVx0Ly8gVmFsaWRhdGVDaGFubmVsIGNoZWNrcyBpZiBhIGNoYW5uZWwgZXhpc3RzIGFuZCBpcyBhY2Nlc3NpYmxlXG4gICAyNTZcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIFZhbGlkYXRlQ2hhbm5lbChjaGFubmVsTmFtZSBzdHJpbmcpIGVycm9yIHtcbiAgIDI1N1x0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBDaGFubmVsIHZhbGlkYXRpb24gcGFzc2VkIGZvcjogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAyNThcdFx0cmV0dXJuIG5pbFxuICAgMjU5XHR9XG4gICAyNjBcdFxuICAgMjYxXHQvLyBHZXRBY3RpdmVDaGFubmVscyByZXR1cm5zIGxpc3Qgb2YgYWN0aXZlIGNoYW5uZWxzXG4gICAyNjJcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEdldEFjdGl2ZUNoYW5uZWxzKCkgKFtdc3RyaW5nLCBlcnJvcikge1xuICAgMjYzXHRcdC8vIE1vY2sgYWN0aXZlIGNoYW5uZWxzXG4gICAyNjRcdFx0Y2hhbm5lbHMgOj0gW11zdHJpbmd7XG4gICAyNjVcdFx0XHRhcy5jb25maWcuQWdvcmEuQ2hhbm5lbFByZWZpeCArIFwiY2FsbF8xXCIsXG4gICAyNjZcdFx0XHRhcy5jb25maWcuQWdvcmEuQ2hhbm5lbFByZWZpeCArIFwiY2FsbF8yXCIsXG4gICAyNjdcdFx0fVxuICAgMjY4XHRcbiAgIDI2OVx0XHRsb2cuUHJpbnRmKFwi8J+TiyBbTU9DS10gQWN0aXZlIGNoYW5uZWxzOiAldlwiLCBjaGFubmVscylcbiAgIDI3MFx0XHRyZXR1cm4gY2hhbm5lbHMsIG5pbFxuICAgMjcxXHR9XG4gICAyNzJcdFxuICAgMjczXHQvLyBDbGVhbnVwIHBlcmZvcm1zIGNsZWFudXAgd2hlbiBzaHV0dGluZyBkb3duXG4gICAyNzRcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIENsZWFudXAoKSBlcnJvciB7XG4gICAyNzVcdFx0bG9nLlByaW50ZihcIvCfp7kgW01PQ0tdIENsZWFuaW5nIHVwIEFnb3JhIHNlcnZpY2VcIilcbiAgIDI3Nlx0XG4gICAyNzdcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDI3OFx0XHQvLyAxLiBMZWF2ZSBhbGwgYWN0aXZlIGNoYW5uZWxzXG4gICAyNzlcdFx0Ly8gMi4gUmVsZWFzZSBtZWRpYSBub2RlIGZhY3RvcnlcbiAgIDI4MFx0XHQvLyAzLiBSZWxlYXNlIEFnb3JhIHNlcnZpY2VcbiAgIDI4MVx0XHQvLyA0LiBDbGVhbiB1cCBhbGwgcmVzb3VyY2VzXG4gICAyODJcdFxuICAgMjgzXHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEFnb3JhIHNlcnZpY2UgY2xlYW51cCBjb21wbGV0ZWRcIilcbiAgIDI4NFx0XHRyZXR1cm4gbmlsXG4gICAyODVcdH1cbiAgIDI4Nlx0XG5Ub3RhbCBsaW5lcyBpbiBmaWxlOiAyODZcbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVU0TmthQThTRllnQ3prY2taTThIQ0cifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNjo1MDozMy45NTJaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgY2hlY2sgdGhlIGNvbmZpZ3VyYXRpb24gdG8gc2VlIGhvdyB0aGUgQWdvcmEgY3JlZGVudGlhbHMgYXJlIHNldCB1cDpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJlYjA1OTFhMy0zOGU0LTRkZTgtODQ0Ni0zZmMwOGI1ZWMwZTEiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFNZkVrOFVTVzE0MmV3TXo1aDNZTEIyIiwidG9vbF9uYW1lIjoiY29kZWJhc2UtcmV0cmlldmFsIiwiaW5wdXRfanNvbiI6IntcImluZm9ybWF0aW9uX3JlcXVlc3RcIjogXCJBUEkgaGFuZGxlcnMgZm9yIHRva2VuIGdlbmVyYXRpb24sIGNhbGwgaW5pdGlhbGl6YXRpb24sIGFuZCBob3cgdG9rZW5zIGFyZSByZXR1cm5lZCB0byBjbGllbnRzXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiTm93IGxldCBtZSBjaGVjayBob3cgdGhlIHRva2VuIGlzIGJlaW5nIHJlcXVlc3RlZCBhbmQgdXNlZCBpbiB0aGUgQVBJIGhhbmRsZXJzOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogY29uZmlnL2NvbmZpZy5nb1xuICAgICAxXHRwYWNrYWdlIGNvbmZpZ1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcImxvZ1wiXG4gICAgIDVcdFx0XCJvc1wiXG4gICAgIDZcdFx0XCJzdHJjb252XCJcbiAgICAgN1x0XG4gICAgIDhcdFx0XCJnaXRodWIuY29tL2pvaG8vZ29kb3RlbnZcIlxuICAgICA5XHQpXG4gICAgMTBcdFxuICAgIDExXHR0eXBlIENvbmZpZyBzdHJ1Y3Qge1xuICAgIDEyXHRcdFNlcnZlciBTZXJ2ZXJDb25maWdcbiAgICAxM1x0XHRBZ29yYSAgQWdvcmFDb25maWdcbiAgICAxNFx0XHRBSSAgICAgQUlDb25maWdcbiAgICAxNVx0fVxuICAgIDE2XHRcbiAgICAxN1x0dHlwZSBTZXJ2ZXJDb25maWcgc3RydWN0IHtcbiAgICAxOFx0XHRQb3J0IHN0cmluZ1xuICAgIDE5XHRcdEhvc3Qgc3RyaW5nXG4gICAgMjBcdH1cbiAgICAyMVx0XG4gICAgMjJcdHR5cGUgQWdvcmFDb25maWcgc3RydWN0IHtcbiAgICAyM1x0XHRBcHBJRCAgICAgICAgICBzdHJpbmdcbiAgICAyNFx0XHRBcHBDZXJ0aWZpY2F0ZSBzdHJpbmdcbiAgICAyNVx0XHRDaGFubmVsUHJlZml4ICBzdHJpbmdcbiAgICAyNlx0fVxuICAgIDI3XHRcbiAgICAyOFx0dHlwZSBBSUNvbmZpZyBzdHJ1Y3Qge1xuICAgIDI5XHRcdE9wZW5BSUFQSUtleSAgICBzdHJpbmdcbiAgICAzMFx0XHRPcGVuQUlNb2RlbCAgICAgc3RyaW5nXG4gICAgMzFcdFx0U1RUUHJvdmlkZXIgICAgIHN0cmluZ1xuICAgIDMyXHRcdFRUU1Byb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzM1x0XHRWb2ljZUlEICAgICAgICAgc3RyaW5nXG4gICAgMzRcdFx0RW5hYmxlVkFEICAgICAgIGJvb2xcbiAgICAzNVx0XHRWQURTZW5zaXRpdml0eSAgZmxvYXQ2NFxuICAgIDM2XHR9XG4gICAgMzdcdFxuICAgIDM4XHR2YXIgQXBwQ29uZmlnICpDb25maWdcbiAgICAzOVx0XG4gICAgNDBcdGZ1bmMgTG9hZENvbmZpZygpIHtcbiAgICA0MVx0XHQvLyBMb2FkIC5lbnYgZmlsZSBpZiBpdCBleGlzdHNcbiAgICA0Mlx0XHRpZiBlcnIgOj0gZ29kb3RlbnYuTG9hZCgpOyBlcnIgIT0gbmlsIHtcbiAgICA0M1x0XHRcdGxvZy5QcmludGxuKFwiTm8gLmVudiBmaWxlIGZvdW5kLCB1c2luZyBlbnZpcm9ubWVudCB2YXJpYWJsZXNcIilcbiAgICA0NFx0XHR9XG4gICAgNDVcdFxuICAgIDQ2XHRcdEFwcENvbmZpZyA9ICZDb25maWd7XG4gICAgNDdcdFx0XHRTZXJ2ZXI6IFNlcnZlckNvbmZpZ3tcbiAgICA0OFx0XHRcdFx0UG9ydDogZ2V0RW52KFwiU0VSVkVSX1BPUlRcIiwgXCI4MDgwXCIpLFxuICAgIDQ5XHRcdFx0XHRIb3N0OiBnZXRFbnYoXCJTRVJWRVJfSE9TVFwiLCBcIjAuMC4wLjBcIiksXG4gICAgNTBcdFx0XHR9LFxuICAgIDUxXHRcdFx0QWdvcmE6IEFnb3JhQ29uZmlne1xuICAgIDUyXHRcdFx0XHRBcHBJRDogICAgICAgICAgZ2V0RW52KFwiQUdPUkFfQVBQX0lEXCIsIFwiXCIpLFxuICAgIDUzXHRcdFx0XHRBcHBDZXJ0aWZpY2F0ZTogZ2V0RW52KFwiQUdPUkFfQVBQX0NFUlRJRklDQVRFXCIsIFwiXCIpLFxuICAgIDU0XHRcdFx0XHRDaGFubmVsUHJlZml4OiAgZ2V0RW52KFwiQUdPUkFfQ0hBTk5FTF9QUkVGSVhcIiwgXCJhaV9zdXBwb3J0X1wiKSxcbiAgICA1NVx0XHRcdH0sXG4gICAgNTZcdFx0XHRBSTogQUlDb25maWd7XG4gICAgNTdcdFx0XHRcdE9wZW5BSUFQSUtleTogICAgZ2V0RW52KFwiT1BFTkFJX0FQSV9LRVlcIiwgXCJcIiksXG4gICAgNThcdFx0XHRcdE9wZW5BSU1vZGVsOiAgICAgZ2V0RW52KFwiT1BFTkFJX01PREVMXCIsIFwiZ3B0LTRcIiksXG4gICAgNTlcdFx0XHRcdFNUVFByb3ZpZGVyOiAgICAgZ2V0RW52KFwiU1RUX1BST1ZJREVSXCIsIFwib3BlbmFpXCIpLFxuICAgIDYwXHRcdFx0XHRUVFNQcm92aWRlcjogICAgIGdldEVudihcIlRUU19QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MVx0XHRcdFx0Vm9pY2VJRDogICAgICAgICBnZXRFbnYoXCJUVFNfVk9JQ0VfSURcIiwgXCJhbGxveVwiKSxcbiAgICA2Mlx0XHRcdFx0RW5hYmxlVkFEOiAgICAgICBnZXRFbnZCb29sKFwiRU5BQkxFX1ZBRFwiLCB0cnVlKSxcbiAgICA2M1x0XHRcdFx0VkFEU2Vuc2l0aXZpdHk6ICBnZXRFbnZGbG9hdChcIlZBRF9TRU5TSVRJVklUWVwiLCAwLjcpLFxuICAgIDY0XHRcdFx0fSxcbiAgICA2NVx0XHR9XG4gICAgNjZcdFxuICAgIDY3XHRcdC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgIDY4XHRcdGlmIEFwcENvbmZpZy5BZ29yYS5BcHBJRCA9PSBcIlwiIHtcbiAgICA2OVx0XHRcdGxvZy5GYXRhbChcIkFHT1JBX0FQUF9JRCBpcyByZXF1aXJlZFwiKVxuICAgIDcwXHRcdH1cbiAgICA3MVx0fVxuICAgIDcyXHRcbiAgICA3M1x0ZnVuYyBnZXRFbnYoa2V5LCBkZWZhdWx0VmFsdWUgc3RyaW5nKSBzdHJpbmcge1xuICAgIDc0XHRcdGlmIHZhbHVlIDo9IG9zLkdldGVudihrZXkpOyB2YWx1ZSAhPSBcIlwiIHtcbiAgICA3NVx0XHRcdHJldHVybiB2YWx1ZVxuICAgIDc2XHRcdH1cbiAgICA3N1x0XHRyZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgNzhcdH1cbiAgICA3OVx0XG4gICAgODBcdGZ1bmMgZ2V0RW52Qm9vbChrZXkgc3RyaW5nLCBkZWZhdWx0VmFsdWUgYm9vbCkgYm9vbCB7XG4gICAgODFcdFx0aWYgdmFsdWUgOj0gb3MuR2V0ZW52KGtleSk7IHZhbHVlICE9IFwiXCIge1xuICAgIDgyXHRcdFx0aWYgcGFyc2VkLCBlcnIgOj0gc3RyY29udi5QYXJzZUJvb2wodmFsdWUpOyBlcnIgPT0gbmlsIHtcbiAgICA4M1x0XHRcdFx0cmV0dXJuIHBhcnNlZFxuICAgIDg0XHRcdFx0fVxuICAgIDg1XHRcdH1cbiAgICA4Nlx0XHRyZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgODdcdH1cbiAgICA4OFx0XG4gICAgODlcdGZ1bmMgZ2V0RW52RmxvYXQoa2V5IHN0cmluZywgZGVmYXVsdFZhbHVlIGZsb2F0NjQpIGZsb2F0NjQge1xuICAgIDkwXHRcdGlmIHZhbHVlIDo9IG9zLkdldGVudihrZXkpOyB2YWx1ZSAhPSBcIlwiIHtcbiAgICA5MVx0XHRcdGlmIHBhcnNlZCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VGbG9hdCh2YWx1ZSwgNjQpOyBlcnIgPT0gbmlsIHtcbiAgICA5Mlx0XHRcdFx0cmV0dXJuIHBhcnNlZFxuICAgIDkzXHRcdFx0fVxuICAgIDk0XHRcdH1cbiAgICA5NVx0XHRyZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgOTZcdH1cbi4uLlxuUGF0aDogLmVudlxuICAgICAxXHQjIFNlcnZlciBDb25maWd1cmF0aW9uXG4gICAgIDJcdFNFUlZFUl9QT1JUPTgwODBcbiAgICAgM1x0U0VSVkVSX0hPU1Q9MC4wLjAuMFxuICAgICA0XHRcbiAgICAgNVx0IyBBZ29yYSBDb25maWd1cmF0aW9uXG4gICAgIDZcdEFHT1JBX0FQUF9JRD0wMTQ1MjM0ODBkOTU0NGJlODAwYTAxNGYzNWJkY2ZhY1xuICAgICA3XHRBR09SQV9BUFBfQ0VSVElGSUNBVEU9ZTdmMzdlNjdkODJiNGFmZjg0NjMxNzc0YzFiNWRlNjFcbiAgICAgOFx0QUdPUkFfQ0hBTk5FTF9QUkVGSVg9YWlfc3VwcG9ydF9cbiAgICAgOVx0XG4gICAgMTBcdCMgQUkgQ29uZmlndXJhdGlvblxuICAgIDExXHRPUEVOQUlfQVBJX0tFWT1rLXByb2otengzVFdYUi1MS2NNUFRtMkx3cTB3MmdvcHV6Vk9mNHR1aWd4UUg1aXBpT3lSQ0s5ekZPT2dFTUJqbXFmMEE3UVZkQzFIRzcxa01UM0JsYmtGSjJMY2dpQkF1cGw1VTliQjVyRVR5UE1SbEVWXzFHWi1rWnNXczFKY1RwQllTLWxoa2FFRERNQXZFcWZudFlRQVVkTEtlblAzNlVBXG4gICAgMTJcdE9QRU5BSV9NT0RFTD1ncHQtNFxuICAgIDEzXHRTVFRfUFJPVklERVI9b3BlbmFpXG4gICAgMTRcdFRUU19QUk9WSURFUj1vcGVuYWlcbiAgICAxNVx0VFRTX1ZPSUNFX0lEPWFsbG95XG4gICAgMTZcdEVOQUJMRV9WQUQ9dHJ1ZVxuICAgIDE3XHRWQURfU0VOU0lUSVZJVFk9MC43XG4uLi5cblBhdGg6IFJFQURNRS5tZFxuLi4uXG4gICAgMzBcdOKUgiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJgg4pSCICAgIOKUgiDilIzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJAg4pSCICAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmFxuICAgIDMxXHTilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJggICAg4pSCIOKUgiBOb3RpZmljYXRpb27ilIIg4pSCICAgIOKUjOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUkFxuICAgIDMyXHQgICAgICAgICAgICAgICAgICAgICAgIOKUgiDilIIgU2VydmljZSAgICAg4pSCIOKUguKXhOKUgOKUgOKWuuKUgiBQdXNoL1NNUy9FbWFpbCAg4pSCXG4gICAgMzNcdCAgICAgICAgICAgICAgICAgICAgICAg4pSCIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmCDilIIgICAg4pSCIFNlcnZpY2VzICAgICAgICDilIJcbiAgICAzNFx0ICAgICAgICAgICAgICAgICAgICAgICDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJggICAg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYXG4gICAgMzVcdGBgYFxuICAgIDM2XHRcbiAgICAzN1x0IyMg8J+TiyBQcmVyZXF1aXNpdGVzXG4gICAgMzhcdFxuICAgIDM5XHQtIEdvIDEuMjIgb3IgaGlnaGVyXG4gICAgNDBcdC0gQWdvcmEuaW8gYWNjb3VudCBhbmQgQXBwIElEXG4gICAgNDFcdC0gT3BlbkFJIEFQSSBrZXkgKG9wdGlvbmFsLCB1c2VzIG1vY2sgYnkgZGVmYXVsdClcbiAgICA0Mlx0XG4gICAgNDNcdCMjIPCfm6DvuI8gU2V0dXBcbiAgICA0NFx0XG4gICAgNDVcdDEuICoqQ2xvbmUgYW5kIHNldHVwKio6XG4gICAgNDZcdCAgIGBgYGJhc2hcbiAgICA0N1x0ICAgZ2l0IGNsb25lIDxyZXBvc2l0b3J5PlxuICAgIDQ4XHQgICBjZCBCYWNrZW5kXG4gICAgNDlcdCAgIGBgYFxuICAgIDUwXHRcbiAgICA1MVx0Mi4gKipJbnN0YWxsIGRlcGVuZGVuY2llcyoqOlxuICAgIDUyXHQgICBgYGBiYXNoXG4gICAgNTNcdCAgIGdvIG1vZCB0aWR5XG4gICAgNTRcdCAgIGBgYFxuICAgIDU1XHRcbiAgICA1Nlx0My4gKipDb25maWd1cmUgZW52aXJvbm1lbnQqKjpcbiAgICA1N1x0ICAgYGBgYmFzaFxuICAgIDU4XHQgICBjcCAuZW52IC5lbnZcbiAgICA1OVx0ICAgIyBFZGl0IC5lbnYgd2l0aCB5b3VyIGNvbmZpZ3VyYXRpb25cbiAgICA2MFx0ICAgYGBgXG4gICAgNjFcdFxuICAgIDYyXHQ0LiAqKlJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlcyoqOlxuICAgIDYzXHQgICBgYGBlbnZcbiAgICA2NFx0ICAgQUdPUkFfQVBQX0lEPXlvdXJfYWdvcmFfYXBwX2lkX2hlcmVcbiAgICA2NVx0ICAgQUdPUkFfQVBQX0NFUlRJRklDQVRFPXlvdXJfYWdvcmFfYXBwX2NlcnRpZmljYXRlX2hlcmVcbiAgICA2Nlx0ICAgT1BFTkFJX0FQSV9LRVk9eW91cl9vcGVuYWlfYXBpX2tleV9oZXJlICAjIE9wdGlvbmFsXG4gICAgNjdcdCAgIGBgYFxuICAgIDY4XHRcbiAgICA2OVx0NS4gKipSdW4gdGhlIHNlcnZlcioqOlxuICAgIDcwXHQgICBgYGBiYXNoXG4gICAgNzFcdCAgIGdvIHJ1biBtYWluLmdvXG4gICAgNzJcdCAgIGBgYFxuICAgIDczXHRcbiAgICA3NFx0IyMg8J+UjCBBUEkgRW5kcG9pbnRzXG4gICAgNzVcdFxuICAgIDc2XHQjIyMgQ29yZSBFbmRwb2ludHNcbiAgICA3N1x0XG4gICAgNzhcdHwgTWV0aG9kIHwgRW5kcG9pbnQgfCBEZXNjcmlwdGlvbiB8XG4gICAgNzlcdHwtLS0tLS0tLXwtLS0tLS0tLS0tfC0tLS0tLS0tLS0tLS18XG4gICAgODBcdHwgYEdFVGAgfCBgL2AgfCBXZWxjb21lIG1lc3NhZ2UgYW5kIEFQSSBvdmVydmlldyB8XG4gICAgODFcdHwgYEdFVGAgfCBgL2hlYWx0aGAgfCBIZWFsdGggY2hlY2sgYW5kIHN5c3RlbSBzdGF0dXMgfFxuICAgIDgyXHRcbiAgICA4M1x0IyMjIENhbGwgTWFuYWdlbWVudFxuLi4uXG4gICAyMzRcdFxuICAgMjM1XHQjIyDwn5SnIENvbmZpZ3VyYXRpb25cbiAgIDIzNlx0XG4gICAyMzdcdCMjIyBTZXJ2ZXIgU2V0dGluZ3NcbiAgIDIzOFx0YGBgZW52XG4gICAyMzlcdFNFUlZFUl9QT1JUPTgwODBcbiAgIDI0MFx0U0VSVkVSX0hPU1Q9MC4wLjAuMFxuICAgMjQxXHRgYGBcbiAgIDI0Mlx0XG4gICAyNDNcdCMjIyBBZ29yYSBTZXR0aW5nc1xuICAgMjQ0XHRgYGBlbnZcbiAgIDI0NVx0QUdPUkFfQVBQX0lEPXlvdXJfYXBwX2lkXG4gICAyNDZcdEFHT1JBX0FQUF9DRVJUSUZJQ0FURT15b3VyX2NlcnRpZmljYXRlXG4gICAyNDdcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAyNDhcdGBgYFxuICAgMjQ5XHRcbiAgIDI1MFx0IyMjIEFJIFNldHRpbmdzXG4gICAyNTFcdGBgYGVudlxuICAgMjUyXHRPUEVOQUlfQVBJX0tFWT15b3VyX2tleVxuICAgMjUzXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgIDI1NFx0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgMjU1XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAyNTZcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgMjU3XHRFTkFCTEVfVkFEPXRydWVcbiAgIDI1OFx0VkFEX1NFTlNJVElWSVRZPTAuN1xuICAgMjU5XHRgYGBcbiAgIDI2MFx0XG4gICAyNjFcdCMjIPCfp6ogRGV2ZWxvcG1lbnRcbiAgIDI2Mlx0XG4gICAyNjNcdCMjIyBNb2NrIFNlcnZpY2VzXG4gICAyNjRcdFxuICAgMjY1XHRUaGUgYXBwbGljYXRpb24gaW5jbHVkZXMgbW9jayBpbXBsZW1lbnRhdGlvbnMgZm9yOlxuICAgMjY2XHQtICoqTm90aWZpY2F0aW9uIFNlcnZpY2UqKjogU2ltdWxhdGVzIHB1c2gvU01TL2VtYWlsIG5vdGlmaWNhdGlvbnNcbiAgIDI2N1x0LSAqKkFJIFNlcnZpY2UqKjogTW9jayBTVFQsIExMTSwgYW5kIFRUUyByZXNwb25zZXNcbiAgIDI2OFx0LSAqKkFnb3JhIFNlcnZpY2UqKjogTW9jayB0b2tlbiBnZW5lcmF0aW9uIGFuZCBjaGFubmVsIG1hbmFnZW1lbnRcbiAgIDI2OVx0XG4gICAyNzBcdCMjIyBUZXN0aW5nXG4gICAyNzFcdFxuICAgMjcyXHRgYGBiYXNoXG4gICAyNzNcdCMgVGVzdCBoZWFsdGggZW5kcG9pbnRcbiAgIDI3NFx0Y3VybCBodHRwOi8vbG9jYWxob3N0OjgwODAvaGVhbHRoXG4uLi5cblBhdGg6IG1haW4uZ29cbi4uLlxuICAgIDE3XHRcbiAgICAxOFx0ZnVuYyBtYWluKCkge1xuICAgIDE5XHRcdC8vIExvYWQgY29uZmlndXJhdGlvblxuICAgIDIwXHRcdGNvbmZpZy5Mb2FkQ29uZmlnKClcbiAgICAyMVx0XHRjZmcgOj0gY29uZmlnLkFwcENvbmZpZ1xuICAgIDIyXHRcbiAgICAyM1x0XHRsb2cuUHJpbnRmKFwi8J+agCBTdGFydGluZyBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIilcbiAgICAyNFx0XHRsb2cuUHJpbnRmKFwi8J+TiyBTZXJ2ZXI6ICVzOiVzXCIsIGNmZy5TZXJ2ZXIuSG9zdCwgY2ZnLlNlcnZlci5Qb3J0KVxuICAgIDI1XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIEFwcCBJRDogJXNcIiwgY2ZnLkFnb3JhLkFwcElEKVxuICAgIDI2XHRcbiAgICAyN1x0XHQvLyBJbml0aWFsaXplIHNlcnZpY2VzXG4gICAgMjhcdFx0bm90aWZpY2F0aW9uU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdOb3RpZmljYXRpb25TZXJ2aWNlKClcbiAgICAyOVx0XHRhZ29yYVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QWdvcmFTZXJ2aWNlKGNmZylcbiAgICAzMFx0XHRhaVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QUlTZXJ2aWNlKGNmZylcbiAgICAzMVx0XHRjYWxsTWFuYWdlciA6PSBzZXJ2aWNlcy5OZXdDYWxsTWFuYWdlcihub3RpZmljYXRpb25TZXJ2aWNlLCBhaVNlcnZpY2UsIGFnb3JhU2VydmljZSlcbiAgICAzMlx0XG4gICAgMzNcdFx0Ly8gSW5pdGlhbGl6ZSBoYW5kbGVyc1xuICAgIDM0XHRcdGNhbGxIYW5kbGVyIDo9IGhhbmRsZXJzLk5ld0NhbGxIYW5kbGVyKGNhbGxNYW5hZ2VyLCBhZ29yYVNlcnZpY2UpXG4gICAgMzVcdFxuICAgIDM2XHRcdC8vIFNldHVwIEdpbiByb3V0ZXJcbiAgICAzN1x0XHRyb3V0ZXIgOj0gc2V0dXBSb3V0ZXIoY2FsbEhhbmRsZXIpXG4gICAgMzhcdFxuICAgIDM5XHRcdC8vIFNldHVwIGdyYWNlZnVsIHNodXRkb3duXG4gICAgNDBcdFx0c2V0dXBHcmFjZWZ1bFNodXRkb3duKGFnb3JhU2VydmljZSlcbi4uLlxuUGF0aDogc2VydmljZXMvYWdvcmFfc2VydmljZS5nb1xuICAgICAxXHRwYWNrYWdlIHNlcnZpY2VzXG4gICAgIDJcdFxuICAgICAzXHRpbXBvcnQgKFxuICAgICA0XHRcdFwiQmFja2VuZC9jb25maWdcIlxuICAgICA1XHRcdFwiZm10XCJcbiAgICAgNlx0XHRcImxvZ1wiXG4gICAgIDdcdFx0XCJzdHJjb252XCJcbiAgICAgOFx0XHRcInRpbWVcIlxuICAgICA5XHRcbiAgICAxMFx0XHRcImdpdGh1Yi5jb20vQWdvcmFJTy9Ub29scy9EeW5hbWljS2V5L0Fnb3JhRHluYW1pY0tleS9nby9zcmMvcnRjdG9rZW5idWlsZGVyMlwiXG4gICAgMTFcdClcbiAgICAxMlx0XG4gICAgMTNcdC8vIEFnb3JhU2VydmljZSBoYW5kbGVzIEFnb3JhIFJUQyBvcGVyYXRpb25zXG4gICAgMTRcdHR5cGUgQWdvcmFTZXJ2aWNlIHN0cnVjdCB7XG4gICAgMTVcdFx0Y29uZmlnICpjb25maWcuQ29uZmlnXG4gICAgMTZcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjb250YWluOlxuICAgIDE3XHRcdC8vIC0gQWdvcmEgc2VydmljZSBpbnN0YW5jZVxuICAgIDE4XHRcdC8vIC0gTWVkaWEgbm9kZSBmYWN0b3J5XG4gICAgMTlcdFx0Ly8gLSBBY3RpdmUgY29ubmVjdGlvbnMgbWFwXG4gICAgMjBcdFx0Ly8gLSBBdWRpby9WaWRlbyBvYnNlcnZlcnNcbiAgICAyMVx0fVxuICAgIDIyXHRcbiAgICAyM1x0Ly8gVG9rZW5SZXF1ZXN0IHJlcHJlc2VudHMgYSByZXF1ZXN0IGZvciBBZ29yYSB0b2tlblxuICAgIDI0XHR0eXBlIFRva2VuUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDI1XHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgIDI2XHRcdFVzZXJJRCAgICAgIHN0cmluZyBganNvbjpcInVzZXJfaWRcImBcbiAgICAyN1x0XHRSb2xlICAgICAgICBpbnQgICAgYGpzb246XCJyb2xlXCJgIC8vIDEgPSBwdWJsaXNoZXIsIDIgPSBzdWJzY3JpYmVyXG4gICAgMjhcdH1cbiAgICAyOVx0XG4gICAgMzBcdC8vIFRva2VuUmVzcG9uc2UgcmVwcmVzZW50cyBBZ29yYSB0b2tlbiByZXNwb25zZVxuICAgIDMxXHR0eXBlIFRva2VuUmVzcG9uc2Ugc3RydWN0IHtcbiAgICAzMlx0XHRUb2tlbiAgICAgc3RyaW5nIGBqc29uOlwidG9rZW5cImBcbiAgICAzM1x0XHRFeHBpcmVzQXQgaW50NjQgIGBqc29uOlwiZXhwaXJlc19hdFwiYFxuICAgIDM0XHR9XG4gICAgMzVcdFxuICAgIDM2XHQvLyBOZXdBZ29yYVNlcnZpY2UgY3JlYXRlcyBhIG5ldyBBZ29yYSBzZXJ2aWNlIGluc3RhbmNlXG4gICAgMzdcdGZ1bmMgTmV3QWdvcmFTZXJ2aWNlKGNmZyAqY29uZmlnLkNvbmZpZykgKkFnb3JhU2VydmljZSB7XG4gICAgMzhcdFx0c2VydmljZSA6PSAmQWdvcmFTZXJ2aWNle1xuICAgIDM5XHRcdFx0Y29uZmlnOiBjZmcsXG4gICAgNDBcdFx0fVxuICAgIDQxXHRcbiAgICA0Mlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCBpbml0aWFsaXplIEFnb3JhIFNESyBoZXJlOlxuICAgIDQzXHRcdC8vIDEuIEluaXRpYWxpemUgQWdvcmEgc2VydmljZSB3aXRoIGFwcCBJRFxuICAgIDQ0XHRcdC8vIDIuIENyZWF0ZSBtZWRpYSBub2RlIGZhY3RvcnlcbiAgICA0NVx0XHQvLyAzLiBTZXQgdXAgYXVkaW8vdmlkZW8gcHJvY2Vzc2luZyBjYWxsYmFja3NcbiAgICA0Nlx0XG4gICAgNDdcdFx0bG9nLlByaW50ZihcIvCfjq8gQWdvcmEgc2VydmljZSBpbml0aWFsaXplZCB3aXRoIEFwcCBJRDogJXNcIiwgY2ZnLkFnb3JhLkFwcElEKVxuICAgIDQ4XHRcdHJldHVybiBzZXJ2aWNlXG4gICAgNDlcdH1cbiAgICA1MFx0XG4gICAgNTFcdC8vIEdlbmVyYXRlVG9rZW4gZ2VuZXJhdGVzIGFuIEFnb3JhIFJUQyB0b2tlbiBmb3IgY2hhbm5lbCBhY2Nlc3NcbiAgICA1Mlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgR2VuZXJhdGVUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQpICgqVG9rZW5SZXNwb25zZSwgZXJyb3IpIHtcbiAgICA1M1x0XHRsb2cuUHJpbnRmKFwi8J+UkSBHZW5lcmF0aW5nIEFnb3JhIHRva2VuIGZvciBjaGFubmVsOiAlcywgdXNlcjogJXMsIHJvbGU6ICVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNTRcdFxuICAgIDU1XHRcdC8vIFRva2VuIGV4cGlyZXMgaW4gMjQgaG91cnNcbiAgICA1Nlx0XHRleHBpcmF0aW9uVGltZSA6PSB0aW1lLk5vdygpLkFkZCgyNCAqIHRpbWUuSG91cikuVW5peCgpXG4gICAgNTdcdFxuICAgIDU4XHRcdHZhciB0b2tlbiBzdHJpbmdcbiAgICA1OVx0XHR2YXIgZXJyIGVycm9yXG4gICAgNjBcdFxuICAgIDYxXHRcdGlmIGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSAhPSBcIlwiIHtcbiAgICA2Mlx0XHRcdC8vIEdlbmVyYXRlIHJlYWwgdG9rZW4gd2l0aCBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAgNjNcdFx0XHR0b2tlbiwgZXJyID0gYXMuZ2VuZXJhdGVSZWFsVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSwgdWludDMyKGV4cGlyYXRpb25UaW1lKSlcbiAgICA2NFx0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgIDY1XHRcdFx0XHRyZXR1cm4gbmlsLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGdlbmVyYXRlIHRva2VuOiAld1wiLCBlcnIpXG4gICAgNjZcdFx0XHR9XG4gICAgNjdcdFx0XHRsb2cuUHJpbnRmKFwi4pyFIEdlbmVyYXRlZCByZWFsIEFnb3JhIHRva2VuIGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgICA2OFx0XHR9IGVsc2Uge1xuICAgIDY5XHRcdFx0Ly8gR2VuZXJhdGUgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgICA3MFx0XHRcdHRva2VuID0gYXMuZ2VuZXJhdGVNb2NrVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA3MVx0XHRcdGxvZy5QcmludGYoXCLimqDvuI8gW01PQ0tdIFVzaW5nIG1vY2sgdG9rZW4gKG5vIGFwcCBjZXJ0aWZpY2F0ZSBwcm92aWRlZClcIilcbiAgICA3Mlx0XHR9XG4gICAgNzNcdFxuICAgIDc0XHRcdHJlc3BvbnNlIDo9ICZUb2tlblJlc3BvbnNle1xuICAgIDc1XHRcdFx0VG9rZW46ICAgICB0b2tlbixcbiAgICA3Nlx0XHRcdEV4cGlyZXNBdDogZXhwaXJhdGlvblRpbWUsXG4gICAgNzdcdFx0fVxuICAgIDc4XHRcbiAgICA3OVx0XHRsb2cuUHJpbnRmKFwi4pyFIFRva2VuIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgIDgwXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAgODFcdH1cbiAgICA4Mlx0XG4gICAgODNcdC8vIGdlbmVyYXRlUmVhbFRva2VuIGdlbmVyYXRlcyBhIHJlYWwgQWdvcmEgdG9rZW4gdXNpbmcgdGhlIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA4NFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2VuZXJhdGVSZWFsVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50LCBleHBpcmF0aW9uIHVpbnQzMikgKHN0cmluZywgZXJyb3IpIHtcbiAgICA4NVx0XHQvLyBDb252ZXJ0IHVzZXJJRCB0byB1aW50MzIgKEFnb3JhIGV4cGVjdHMgbnVtZXJpYyBVSUQpXG4gICAgODZcdFx0dmFyIHVpZCB1aW50MzJcbiAgICA4N1x0XHRpZiB1c2VySUQgIT0gXCJcIiB7XG4gICAgODhcdFx0XHRpZiBwYXJzZWRVSUQsIGVyciA6PSBzdHJjb252LlBhcnNlVWludCh1c2VySUQsIDEwLCAzMik7IGVyciA9PSBuaWwge1xuICAgIDg5XHRcdFx0XHR1aWQgPSB1aW50MzIocGFyc2VkVUlEKVxuICAgIDkwXHRcdFx0fSBlbHNlIHtcbiAgICA5MVx0XHRcdFx0Ly8gSWYgdXNlcklEIGlzIG5vdCBudW1lcmljLCBnZW5lcmF0ZSBhIGhhc2gtYmFzZWQgVUlEXG4gICAgOTJcdFx0XHRcdHVpZCA9IHVpbnQzMihsZW4odXNlcklEKSkgLy8gU2ltcGxlIGZhbGxiYWNrLCBpbiBwcm9kdWN0aW9uIHVzZSBhIHByb3BlciBoYXNoXG4gICAgOTNcdFx0XHR9XG4gICAgOTRcdFx0fSBlbHNlIHtcbiAgICA5NVx0XHRcdHVpZCA9IDAgLy8gTGV0IEFnb3JhIGFzc2lnbiBhIFVJRFxuICAgIDk2XHRcdH1cbiAgICA5N1x0XG4gICAgOThcdFx0Ly8gQ29udmVydCByb2xlIHRvIEFnb3JhIHJvbGUgdHlwZVxuICAgIDk5XHRcdHZhciBhZ29yYVJvbGUgcnRjdG9rZW5idWlsZGVyMi5Sb2xlXG4gICAxMDBcdFx0aWYgcm9sZSA9PSAxIHtcbiAgIDEwMVx0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVB1Ymxpc2hlclxuICAgMTAyXHRcdH0gZWxzZSB7XG4gICAxMDNcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVTdWJzY3JpYmVyXG4gICAxMDRcdFx0fVxuICAgMTA1XHRcbiAgIDEwNlx0XHQvLyBHZW5lcmF0ZSB0b2tlbiB1c2luZyBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAxMDdcdFx0dG9rZW4sIGVyciA6PSBydGN0b2tlbmJ1aWxkZXIyLkJ1aWxkVG9rZW5XaXRoVWlkKFxuICAgMTA4XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcElELFxuICAgMTA5XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlLFxuICAgMTEwXHRcdFx0Y2hhbm5lbE5hbWUsXG4gICAxMTFcdFx0XHR1aWQsXG4gICAxMTJcdFx0XHRhZ29yYVJvbGUsXG4gICAxMTNcdFx0XHRleHBpcmF0aW9uLFxuICAgMTE0XHRcdFx0ZXhwaXJhdGlvbiwgLy8gUHJpdmlsZWdlIGV4cGlyZWQgdGltZXN0YW1wIChzYW1lIGFzIHRva2VuIGV4cGlyYXRpb24pXG4gICAxMTVcdFx0KVxuICAgMTE2XHRcbiAgIDExN1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDExOFx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGJ1aWxkIEFnb3JhIHRva2VuOiAld1wiLCBlcnIpXG4gICAxMTlcdFx0fVxuICAgMTIwXHRcbiAgIDEyMVx0XHRyZXR1cm4gdG9rZW4sIG5pbFxuICAgMTIyXHR9XG4gICAxMjNcdFxuICAgMTI0XHQvLyBnZW5lcmF0ZU1vY2tUb2tlbiBnZW5lcmF0ZXMgYSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgMTI1XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQpIHN0cmluZyB7XG4gICAxMjZcdFx0dGltZXN0YW1wIDo9IHRpbWUuTm93KCkuVW5peCgpXG4gICAxMjdcdFx0cmV0dXJuIGZtdC5TcHJpbnRmKFwibW9ja190b2tlbl8lc18lc18lZF8lZFwiLCBjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB0aW1lc3RhbXApXG4gICAxMjhcdH1cbiAgIDEyOVx0XG4gICAxMzBcdC8vIEpvaW5DaGFubmVsIGpvaW5zIGFuIEFnb3JhIGNoYW5uZWwgZm9yIEFJIHByb2Nlc3NpbmdcbiAgIDEzMVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgSm9pbkNoYW5uZWwoY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcpIGVycm9yIHtcbiAgIDEzMlx0XHRsb2cuUHJpbnRmKFwi8J+UlyBbTU9DS10gQUkgam9pbmluZyBBZ29yYSBjaGFubmVsOiAlcyBhcyB1c2VyOiAlc1wiLCBjaGFubmVsTmFtZSwgdXNlcklEKVxuICAgMTMzXHRcbiAgIDEzNFx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTM1XHRcdC8vIDEuIENyZWF0ZSBSVEMgY29ubmVjdGlvblxuICAgMTM2XHRcdC8vIDIuIFNldCB1cCBsb2NhbCB1c2VyXG4gICAxMzdcdFx0Ly8gMy4gQ3JlYXRlIGF1ZGlvIHRyYWNrIGZvciBBSSB2b2ljZVxuICAgMTM4XHRcdC8vIDQuIFJlZ2lzdGVyIGF1ZGlvIGZyYW1lIG9ic2VydmVycyBmb3IgcHJvY2Vzc2luZyBpbmNvbWluZyBhdWRpb1xuICAgMTM5XHRcdC8vIDUuIEpvaW4gdGhlIGNoYW5uZWxcbiAgIDE0MFx0XHQvLyA2LiBTdGFydCBhdWRpbyBwcm9jZXNzaW5nIHBpcGVsaW5lXG4gICAxNDFcdFxuICAgMTQyXHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb25cbiAgIDE0M1x0XHR0aW1lLlNsZWVwKDUwMCAqIHRpbWUuTWlsbGlzZWNvbmQpIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gdGltZVxuICAgMTQ0XHRcbiAgIDE0NVx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgam9pbmVkIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTQ2XHRcdHJldHVybiBuaWxcbiAgIDE0N1x0fVxuICAgMTQ4XHRcbiAgIDE0OVx0Ly8gTGVhdmVDaGFubmVsIGxlYXZlcyBhbiBBZ29yYSBjaGFubmVsXG4gICAxNTBcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIExlYXZlQ2hhbm5lbChjYWxsSUQgc3RyaW5nKSBlcnJvciB7XG4gICAxNTFcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMTUyXHRcdGxvZy5QcmludGYoXCLwn5GLIFtNT0NLXSBBSSBsZWF2aW5nIEFnb3JhIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTUzXHRcbiAgIDE1NFx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTU1XHRcdC8vIDEuIFN0b3AgYXVkaW8gcHJvY2Vzc2luZ1xuICAgMTU2XHRcdC8vIDIuIFVucHVibGlzaCBhdWRpbyB0cmFja3NcbiAgIDE1N1x0XHQvLyAzLiBEaXNjb25uZWN0IGZyb20gY2hhbm5lbFxuICAgMTU4XHRcdC8vIDQuIENsZWFuIHVwIHJlc291cmNlc1xuICAgMTU5XHRcbiAgIDE2MFx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uXG4gICAxNjFcdFx0dGltZS5TbGVlcCgyMDAgKiB0aW1lLk1pbGxpc2Vjb25kKSAvLyBTaW11bGF0ZSBkaXNjb25uZWN0aW9uIHRpbWVcbiAgIDE2Mlx0XG4gICAxNjNcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQUkgc3VjY2Vzc2Z1bGx5IGxlZnQgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNjRcdFx0cmV0dXJuIG5pbFxuICAgMTY1XHR9XG4gICAxNjZcdFxuICAgMTY3XHQvLyBTZW5kQXVkaW9Ub0NoYW5uZWwgc2VuZHMgQUktZ2VuZXJhdGVkIGF1ZGlvIHRvIHRoZSBjaGFubmVsXG4gICAxNjhcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIFNlbmRBdWRpb1RvQ2hhbm5lbChjYWxsSUQgc3RyaW5nLCBhdWRpb0RhdGEgW11ieXRlKSBlcnJvciB7XG4gICAxNjlcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMTcwXHRcdGxvZy5QcmludGYoXCLwn5SKIFtNT0NLXSBTZW5kaW5nICVkIGJ5dGVzIG9mIGF1ZGlvIHRvIGNoYW5uZWw6ICVzXCIsIGxlbihhdWRpb0RhdGEpLCBjaGFubmVsTmFtZSlcbiAgIDE3MVx0XG4gICAxNzJcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDE3M1x0XHQvLyAxLiBDb252ZXJ0IGF1ZGlvIGRhdGEgdG8gcmVxdWlyZWQgZm9ybWF0IChQQ00pXG4gICAxNzRcdFx0Ly8gMi4gUHVzaCBhdWRpbyBmcmFtZXMgdG8gQWdvcmEgYXVkaW8gdHJhY2tcbiAgIDE3NVx0XHQvLyAzLiBIYW5kbGUgYXVkaW8gZW5jb2RpbmcgYW5kIHRyYW5zbWlzc2lvblxuICAgMTc2XHRcbiAgIDE3N1x0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uIC0gc2ltdWxhdGUgYXVkaW8gdHJhbnNtaXNzaW9uIHRpbWVcbiAgIDE3OFx0XHR0cmFuc21pc3Npb25UaW1lIDo9IHRpbWUuRHVyYXRpb24obGVuKGF1ZGlvRGF0YSkvMTAwMCkgKiB0aW1lLk1pbGxpc2Vjb25kXG4gICAxNzlcdFx0aWYgdHJhbnNtaXNzaW9uVGltZSA+IDEwMCp0aW1lLk1pbGxpc2Vjb25kIHtcbiAgIDE4MFx0XHRcdHRyYW5zbWlzc2lvblRpbWUgPSAxMDAgKiB0aW1lLk1pbGxpc2Vjb25kXG4gICAxODFcdFx0fVxuICAgMTgyXHRcdHRpbWUuU2xlZXAodHJhbnNtaXNzaW9uVGltZSlcbiAgIDE4M1x0XG4gICAxODRcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQXVkaW8gc2VudCBzdWNjZXNzZnVsbHkgdG8gY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxODVcdFx0cmV0dXJuIG5pbFxuICAgMTg2XHR9XG4gICAxODdcdFxuICAgMTg4XHQvLyBTZXR1cEF1ZGlvUHJvY2Vzc2luZyBzZXRzIHVwIGF1ZGlvIHByb2Nlc3NpbmcgY2FsbGJhY2tzXG4gICAxODlcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIFNldHVwQXVkaW9Qcm9jZXNzaW5nKGNhbGxJRCBzdHJpbmcsIGF1ZGlvQ2FsbGJhY2sgZnVuYyhbXWJ5dGUpIGVycm9yKSBlcnJvciB7XG4gICAxOTBcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMTkxXHRcdGxvZy5QcmludGYoXCLwn46Z77iPIFtNT0NLXSBTZXR0aW5nIHVwIGF1ZGlvIHByb2Nlc3NpbmcgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTkyXHRcbiAgIDE5M1x0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTk0XHRcdC8vIDEuIFJlZ2lzdGVyIGF1ZGlvIGZyYW1lIG9ic2VydmVyXG4gICAxOTVcdFx0Ly8gMi4gU2V0IHVwIFZBRCAoVm9pY2UgQWN0aXZpdHkgRGV0ZWN0aW9uKVxuICAgMTk2XHRcdC8vIDMuIENvbmZpZ3VyZSBhdWRpbyBmb3JtYXQgKHNhbXBsZSByYXRlLCBjaGFubmVscywgZXRjLilcbiAgIDE5N1x0XHQvLyA0LiBTdGFydCBwcm9jZXNzaW5nIGluY29taW5nIGF1ZGlvIGZyYW1lc1xuICAgMTk4XHRcbiAgIDE5OVx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uIC0gc2ltdWxhdGUgcmVjZWl2aW5nIGF1ZGlvIHBlcmlvZGljYWxseVxuICAgMjAwXHRcdGdvIGFzLnNpbXVsYXRlSW5jb21pbmdBdWRpbyhjYWxsSUQsIGF1ZGlvQ2FsbGJhY2spXG4gICAyMDFcdFxuICAgMjAyXHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEF1ZGlvIHByb2Nlc3Npbmcgc2V0dXAgY29tcGxldGUgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMjAzXHRcdHJldHVybiBuaWxcbiAgIDIwNFx0fVxuICAgMjA1XHRcbiAgIDIwNlx0Ly8gc2ltdWxhdGVJbmNvbWluZ0F1ZGlvIHNpbXVsYXRlcyByZWNlaXZpbmcgYXVkaW8gZnJvbSB0aGUgY3VzdG9tZXJcbiAgIDIwN1x0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgc2ltdWxhdGVJbmNvbWluZ0F1ZGlvKGNhbGxJRCBzdHJpbmcsIGF1ZGlvQ2FsbGJhY2sgZnVuYyhbXWJ5dGUpIGVycm9yKSB7XG4gICAyMDhcdFx0bG9nLlByaW50ZihcIvCfjq0gW01PQ0tdIFN0YXJ0aW5nIGF1ZGlvIHNpbXVsYXRpb24gZm9yIGNhbGw6ICVzXCIsIGNhbGxJRClcbiAgIDIwOVx0XG4gICAyMTBcdFx0Ly8gU2ltdWxhdGUgY3VzdG9tZXIgc3BlYWtpbmcgZXZlcnkgNS0xMCBzZWNvbmRzXG4gICAyMTFcdFx0Zm9yIGkgOj0gMDsgaSA8IDU7IGkrKyB7IC8vIFNpbXVsYXRlIDUgY3VzdG9tZXIgbWVzc2FnZXNcbiAgIDIxMlx0XHRcdHRpbWUuU2xlZXAodGltZS5EdXJhdGlvbig1K2kqMikgKiB0aW1lLlNlY29uZClcbiAgIDIxM1x0XG4gICAyMTRcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIGF1ZGlvIGRhdGFcbiAgIDIxNVx0XHRcdG1vY2tBdWRpb0RhdGEgOj0gbWFrZShbXWJ5dGUsIDE2MDApIC8vIFNpbXVsYXRlIDEwMG1zIG9mIDE2a0h6IG1vbm8gYXVkaW9cbiAgIDIxNlx0XHRcdGZvciBqIDo9IHJhbmdlIG1vY2tBdWRpb0RhdGEge1xuICAgMjE3XHRcdFx0XHRtb2NrQXVkaW9EYXRhW2pdID0gYnl0ZSgoaSoxMDAgKyBqKSAlIDI1NilcbiAgIDIxOFx0XHRcdH1cbiAgIDIxOVx0XG4gICAyMjBcdFx0XHRsb2cuUHJpbnRmKFwi8J+OpCBbTU9DS10gU2ltdWxhdGluZyBpbmNvbWluZyBhdWRpbyBmb3IgY2FsbCAlcyAobWVzc2FnZSAlZClcIiwgY2FsbElELCBpKzEpXG4gICAyMjFcdFxuICAgMjIyXHRcdFx0aWYgZXJyIDo9IGF1ZGlvQ2FsbGJhY2sobW9ja0F1ZGlvRGF0YSk7IGVyciAhPSBuaWwge1xuICAgMjIzXHRcdFx0XHRsb2cuUHJpbnRmKFwiRXJyb3IgcHJvY2Vzc2luZyBzaW11bGF0ZWQgYXVkaW86ICV2XCIsIGVycilcbiAgIDIyNFx0XHRcdFx0YnJlYWtcbiAgIDIyNVx0XHRcdH1cbiAgIDIyNlx0XHR9XG4gICAyMjdcdFxuICAgMjI4XHRcdGxvZy5QcmludGYoXCLwn46tIFtNT0NLXSBBdWRpbyBzaW11bGF0aW9uIGNvbXBsZXRlZCBmb3IgY2FsbDogJXNcIiwgY2FsbElEKVxuICAgMjI5XHR9XG4gICAyMzBcdFxuICAgMjMxXHQvLyBHZXRDaGFubmVsU3RhdHMgcmV0dXJucyBjaGFubmVsIHN0YXRpc3RpY3NcbiAgIDIzMlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgR2V0Q2hhbm5lbFN0YXRzKGNhbGxJRCBzdHJpbmcpIChtYXBbc3RyaW5nXWludGVyZmFjZXt9LCBlcnJvcikge1xuICAgMjMzXHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDIzNFx0XHRsb2cuUHJpbnRmKFwi8J+TiiBbTU9DS10gR2V0dGluZyBjaGFubmVsIHN0YXRzIGZvcjogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAyMzVcdFxuICAgMjM2XHRcdC8vIE1vY2sgc3RhdGlzdGljc1xuICAgMjM3XHRcdHN0YXRzIDo9IG1hcFtzdHJpbmddaW50ZXJmYWNle317XG4gICAyMzhcdFx0XHRcImNoYW5uZWxfbmFtZVwiOiAgICAgY2hhbm5lbE5hbWUsXG4gICAyMzlcdFx0XHRcImNvbm5lY3RlZF91c2Vyc1wiOiAgMiwgLy8gQUkgKyBDdXN0b21lclxuICAgMjQwXHRcdFx0XCJhdWRpb19iaXRyYXRlXCI6ICAgIDY0MDAwLFxuICAgMjQxXHRcdFx0XCJwYWNrZXRfbG9zc19yYXRlXCI6IDAuMDEsXG4gICAyNDJcdFx0XHRcInJ0dFwiOiAgICAgICAgICAgICA0NSwgLy8gUm91bmQgdHJpcCB0aW1lIGluIG1zXG4gICAyNDNcdFx0XHRcImF1ZGlvX3F1YWxpdHlcIjogICAgXCJnb29kXCIsXG4gICAyNDRcdFx0XHRcImNvbm5lY3Rpb25fc3RhdGVcIjogXCJjb25uZWN0ZWRcIixcbiAgIDI0NVx0XHR9XG4gICAyNDZcdFxuICAgMjQ3XHRcdHJldHVybiBzdGF0cywgbmlsXG4gICAyNDhcdH1cbiAgIDI0OVx0XG4gICAyNTBcdC8vIGdldENoYW5uZWxOYW1lIGdlbmVyYXRlcyBjaGFubmVsIG5hbWUgZnJvbSBjYWxsIElEXG4gICAyNTFcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdldENoYW5uZWxOYW1lKGNhbGxJRCBzdHJpbmcpIHN0cmluZyB7XG4gICAyNTJcdFx0cmV0dXJuIGFzLmNvbmZpZy5BZ29yYS5DaGFubmVsUHJlZml4ICsgY2FsbElEXG4gICAyNTNcdH1cbiAgIDI1NFx0XG4gICAyNTVcdC8vIFZhbGlkYXRlQ2hhbm5lbCBjaGVja3MgaWYgYSBjaGFubmVsIGV4aXN0cyBhbmQgaXMgYWNjZXNzaWJsZVxuICAgMjU2XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBWYWxpZGF0ZUNoYW5uZWwoY2hhbm5lbE5hbWUgc3RyaW5nKSBlcnJvciB7XG4gICAyNTdcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQ2hhbm5lbCB2YWxpZGF0aW9uIHBhc3NlZCBmb3I6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMjU4XHRcdHJldHVybiBuaWxcbiAgIDI1OVx0fVxuICAgMjYwXHRcbiAgIDI2MVx0Ly8gR2V0QWN0aXZlQ2hhbm5lbHMgcmV0dXJucyBsaXN0IG9mIGFjdGl2ZSBjaGFubmVsc1xuICAgMjYyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZXRBY3RpdmVDaGFubmVscygpIChbXXN0cmluZywgZXJyb3IpIHtcbiAgIDI2M1x0XHQvLyBNb2NrIGFjdGl2ZSBjaGFubmVsc1xuICAgMjY0XHRcdGNoYW5uZWxzIDo9IFtdc3RyaW5ne1xuICAgMjY1XHRcdFx0YXMuY29uZmlnLkFnb3JhLkNoYW5uZWxQcmVmaXggKyBcImNhbGxfMVwiLFxuICAgMjY2XHRcdFx0YXMuY29uZmlnLkFnb3JhLkNoYW5uZWxQcmVmaXggKyBcImNhbGxfMlwiLFxuICAgMjY3XHRcdH1cbiAgIDI2OFx0XG4gICAyNjlcdFx0bG9nLlByaW50ZihcIvCfk4sgW01PQ0tdIEFjdGl2ZSBjaGFubmVsczogJXZcIiwgY2hhbm5lbHMpXG4gICAyNzBcdFx0cmV0dXJuIGNoYW5uZWxzLCBuaWxcbiAgIDI3MVx0fVxuICAgMjcyXHRcbiAgIDI3M1x0Ly8gQ2xlYW51cCBwZXJmb3JtcyBjbGVhbnVwIHdoZW4gc2h1dHRpbmcgZG93blxuICAgMjc0XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBDbGVhbnVwKCkgZXJyb3Ige1xuICAgMjc1XHRcdGxvZy5QcmludGYoXCLwn6e5IFtNT0NLXSBDbGVhbmluZyB1cCBBZ29yYSBzZXJ2aWNlXCIpXG4gICAyNzZcdFxuICAgMjc3XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAyNzhcdFx0Ly8gMS4gTGVhdmUgYWxsIGFjdGl2ZSBjaGFubmVsc1xuICAgMjc5XHRcdC8vIDIuIFJlbGVhc2UgbWVkaWEgbm9kZSBmYWN0b3J5XG4gICAyODBcdFx0Ly8gMy4gUmVsZWFzZSBBZ29yYSBzZXJ2aWNlXG4gICAyODFcdFx0Ly8gNC4gQ2xlYW4gdXAgYWxsIHJlc291cmNlc1xuICAgMjgyXHRcbiAgIDI4M1x0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBZ29yYSBzZXJ2aWNlIGNsZWFudXAgY29tcGxldGVkXCIpXG4gICAyODRcdFx0cmV0dXJuIG5pbFxuICAgMjg1XHR9XG4uLi5cblBhdGg6IHNlcnZpY2VzL2FpX3NlcnZpY2UuZ29cbi4uLlxuICAgMjMzXHRcbiAgIDIzNFx0Ly8gQ29udmVydFRleHRUb1NwZWVjaCBjb252ZXJ0cyB0ZXh0IHRvIHNwZWVjaCBhdWRpb1xuICAgMjM1XHRmdW5jIChhaSAqQUlTZXJ2aWNlKSBDb252ZXJ0VGV4dFRvU3BlZWNoKHRleHQgc3RyaW5nKSAoW11ieXRlLCBlcnJvcikge1xuICAgMjM2XHRcdGxvZy5QcmludGYoXCLwn5SKIFtNT0NLXSBDb252ZXJ0aW5nIHRleHQgdG8gc3BlZWNoOiAnJXMnXCIsIHRleHQpXG4gICAyMzdcdFx0XG4gICAyMzhcdFx0Ly8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMjM5XHRcdC8vIDEuIENhbGwgT3BlbkFJIFRUUyBBUEkgb3Igb3RoZXIgVFRTIHNlcnZpY2VcbiAgIDI0MFx0XHQvLyAyLiBSZXR1cm4gYXVkaW8gYnl0ZXMgaW4gdGhlIHJlcXVpcmVkIGZvcm1hdFxuICAgMjQxXHRcdC8vIDMuIEhhbmRsZSBkaWZmZXJlbnQgdm9pY2VzIGFuZCBsYW5ndWFnZXNcbiAgIDI0Mlx0XHRcbiAgIDI0M1x0XHQvLyBTaW11bGF0ZSBwcm9jZXNzaW5nIHRpbWUgYmFzZWQgb24gdGV4dCBsZW5ndGhcbiAgIDI0NFx0XHRwcm9jZXNzaW5nVGltZSA6PSB0aW1lLkR1cmF0aW9uKGxlbih0ZXh0KSoxMCkgKiB0aW1lLk1pbGxpc2Vjb25kXG4gICAyNDVcdFx0aWYgcHJvY2Vzc2luZ1RpbWUgPiAyKnRpbWUuU2Vjb25kIHtcbiAgIDI0Nlx0XHRcdHByb2Nlc3NpbmdUaW1lID0gMiAqIHRpbWUuU2Vjb25kXG4gICAyNDdcdFx0fVxuICAgMjQ4XHRcdHRpbWUuU2xlZXAocHJvY2Vzc2luZ1RpbWUpXG4gICAyNDlcdFx0XG4gICAyNTBcdFx0Ly8gTW9jayBhdWRpbyBkYXRhIC0gaW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBiZSBhY3R1YWwgYXVkaW8gYnl0ZXNcbiAgIDI1MVx0XHRtb2NrQXVkaW9EYXRhIDo9IG1ha2UoW11ieXRlLCBsZW4odGV4dCkqMTAwKSAvLyBTaW11bGF0ZSBhdWRpbyBkYXRhIHNpemVcbiAgIDI1Mlx0XHRmb3IgaSA6PSByYW5nZSBtb2NrQXVkaW9EYXRhIHtcbiAgIDI1M1x0XHRcdG1vY2tBdWRpb0RhdGFbaV0gPSBieXRlKGkgJSAyNTYpXG4gICAyNTRcdFx0fVxuICAgMjU1XHRcdFxuICAgMjU2XHRcdGxvZy5QcmludGYoXCLwn461IFtNT0NLXSBHZW5lcmF0ZWQgJWQgYnl0ZXMgb2YgYXVkaW8gZGF0YVwiLCBsZW4obW9ja0F1ZGlvRGF0YSkpXG4gICAyNTdcdFx0cmV0dXJuIG1vY2tBdWRpb0RhdGEsIG5pbFxuICAgMjU4XHR9XG4uLi5cbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUpQYXN5aW1CNkp1OHFDOUw4ZzVkR2gifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNjo1MDo1My41OTBaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgY2hlY2sgaG93IHRoZSB0b2tlbiBpcyBiZWluZyByZXF1ZXN0ZWQgYW5kIHVzZWQgaW4gdGhlIEFQSSBoYW5kbGVyczpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJlNzVkNDRjNi0xZWNmLTQyMzctYmM4My0xNGYzZmI4OGY1OWEiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFROEIzRVJOczVGdmN0NnJEdXhVcDZ6IiwidG9vbF9uYW1lIjoibGF1bmNoLXByb2Nlc3MiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcImNkIC9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCAmJiBnbyBydW4gbWFpbi5nb1wiLCBcIndhaXRcIjogZmFsc2UsIFwibWF4X3dhaXRfc2Vjb25kc1wiOiAxMH0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiJOb3cgbGV0IG1lIGNoZWNrIHRoZSBzZXJ2ZXIgbG9ncyB0byBzZWUgd2hhdCdzIGhhcHBlbmluZyB3aGVuIHRoZSB0b2tlbiBpcyBnZW5lcmF0ZWQ6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJUaGUgZm9sbG93aW5nIGNvZGUgc2VjdGlvbnMgd2VyZSByZXRyaWV2ZWQ6XG5QYXRoOiBoYW5kbGVycy9jYWxsX2hhbmRsZXIuZ29cbi4uLlxuICAgIDI2XHRcbiAgICAyN1x0Ly8gSW5pdGlhdGVDYWxsIGhhbmRsZXMgUE9TVCAvYXBpL2NhbGxzL2luaXRpYXRlXG4gICAgMjhcdGZ1bmMgKGNoICpDYWxsSGFuZGxlcikgSW5pdGlhdGVDYWxsKGMgKmdpbi5Db250ZXh0KSB7XG4gICAgMjlcdFx0dmFyIHJlcXVlc3QgbW9kZWxzLkluaXRpYXRlQ2FsbFJlcXVlc3RcbiAgICAzMFx0XHRpZiBlcnIgOj0gYy5TaG91bGRCaW5kSlNPTigmcmVxdWVzdCk7IGVyciAhPSBuaWwge1xuICAgIDMxXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAgMzJcdFx0XHRcdFwiZXJyb3JcIjogICBcIkludmFsaWQgcmVxdWVzdCBmb3JtYXRcIixcbiAgICAzM1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDM0XHRcdFx0fSlcbiAgICAzNVx0XHRcdHJldHVyblxuICAgIDM2XHRcdH1cbiAgICAzN1x0XG4gICAgMzhcdFx0Ly8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAgMzlcdFx0aWYgcmVxdWVzdC5DdXN0b21lcklEID09IFwiXCIge1xuICAgIDQwXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAgNDFcdFx0XHRcdFwiZXJyb3JcIjogXCJjdXN0b21lcl9pZCBpcyByZXF1aXJlZFwiLFxuICAgIDQyXHRcdFx0fSlcbiAgICA0M1x0XHRcdHJldHVyblxuICAgIDQ0XHRcdH1cbiAgICA0NVx0XG4gICAgNDZcdFx0aWYgcmVxdWVzdC5QdXJwb3NlID09IFwiXCIge1xuICAgIDQ3XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAgNDhcdFx0XHRcdFwiZXJyb3JcIjogXCJwdXJwb3NlIGlzIHJlcXVpcmVkXCIsXG4gICAgNDlcdFx0XHR9KVxuICAgIDUwXHRcdFx0cmV0dXJuXG4gICAgNTFcdFx0fVxuICAgIDUyXHRcbiAgICA1M1x0XHQvLyBJbml0aWF0ZSB0aGUgY2FsbFxuICAgIDU0XHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Jbml0aWF0ZUNhbGwocmVxdWVzdClcbiAgICA1NVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA1Nlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDU3XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gaW5pdGlhdGUgY2FsbFwiLFxuICAgIDU4XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgNTlcdFx0XHR9KVxuICAgIDYwXHRcdFx0cmV0dXJuXG4gICAgNjFcdFx0fVxuICAgIDYyXHRcbiAgICA2M1x0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGNoYW5uZWxcbiAgICA2NFx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIFwiYWlfYWdlbnRcIiwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAgNjVcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjZcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgICA2N1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIEFnb3JhIHRva2VuXCIsXG4gICAgNjhcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA2OVx0XHRcdH0pXG4gICAgNzBcdFx0XHRyZXR1cm5cbiAgICA3MVx0XHR9XG4gICAgNzJcdFxuICAgIDczXHRcdC8vIEpvaW4gdGhlIGNoYW5uZWwgYXMgQUkgYWdlbnRcbiAgICA3NFx0XHRpZiBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkpvaW5DaGFubmVsKGNhbGwuQ2hhbm5lbE5hbWUsIFwiYWlfYWdlbnRcIik7IGVyciAhPSBuaWwge1xuICAgIDc1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgNzZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBqb2luIEFnb3JhIGNoYW5uZWxcIixcbiAgICA3N1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDc4XHRcdFx0fSlcbiAgICA3OVx0XHRcdHJldHVyblxuICAgIDgwXHRcdH1cbiAgICA4MVx0XG4gICAgODJcdFx0Ly8gU2V0IHVwIGF1ZGlvIHByb2Nlc3NpbmcgZm9yIHRoaXMgY2FsbFxuICAgIDgzXHRcdGF1ZGlvQ2FsbGJhY2sgOj0gZnVuYyhhdWRpb0RhdGEgW11ieXRlKSBlcnJvciB7XG4gICAgODRcdFx0XHRyZXR1cm4gY2guY2FsbE1hbmFnZXIuUHJvY2Vzc0F1ZGlvTWVzc2FnZShjYWxsLklELCBhdWRpb0RhdGEpXG4gICAgODVcdFx0fVxuICAgIDg2XHRcbiAgICA4N1x0XHRpZiBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLlNldHVwQXVkaW9Qcm9jZXNzaW5nKGNhbGwuSUQsIGF1ZGlvQ2FsbGJhY2spOyBlcnIgIT0gbmlsIHtcbiAgICA4OFx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDg5XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gc2V0dXAgYXVkaW8gcHJvY2Vzc2luZ1wiLFxuICAgIDkwXHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgOTFcdFx0XHR9KVxuICAgIDkyXHRcdFx0cmV0dXJuXG4gICAgOTNcdFx0fVxuICAgIDk0XHRcbiAgICA5NVx0XHRyZXNwb25zZSA6PSBtb2RlbHMuSW5pdGlhdGVDYWxsUmVzcG9uc2V7XG4gICAgOTZcdFx0XHRDYWxsSUQ6ICAgICAgY2FsbC5JRCxcbiAgICA5N1x0XHRcdENoYW5uZWxOYW1lOiBjYWxsLkNoYW5uZWxOYW1lLFxuICAgIDk4XHRcdFx0VG9rZW46ICAgICAgIHRva2VuLlRva2VuLFxuICAgIDk5XHRcdFx0U3RhdHVzOiAgICAgIHN0cmluZyhjYWxsLlN0YXR1cyksXG4gICAxMDBcdFx0XHRNZXNzYWdlOiAgICAgXCJDYWxsIGluaXRpYXRlZCBzdWNjZXNzZnVsbHkuIE5vdGlmaWNhdGlvbiBzZW50IHRvIGN1c3RvbWVyLlwiLFxuICAgMTAxXHRcdH1cbiAgIDEwMlx0XG4gICAxMDNcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQ3JlYXRlZCwgcmVzcG9uc2UpXG4gICAxMDRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdC8vIEluaXRpYXRlQ3VzdG9tZXJDYWxsIGhhbmRsZXMgUE9TVCAvYXBpL2NhbGxzL2N1c3RvbWVyLWluaXRpYXRlXG4gICAxMDdcdGZ1bmMgKGNoICpDYWxsSGFuZGxlcikgSW5pdGlhdGVDdXN0b21lckNhbGwoYyAqZ2luLkNvbnRleHQpIHtcbiAgIDEwOFx0XHR2YXIgcmVxdWVzdCBtb2RlbHMuSW5pdGlhdGVDdXN0b21lckNhbGxSZXF1ZXN0XG4gICAxMDlcdFx0aWYgZXJyIDo9IGMuU2hvdWxkQmluZEpTT04oJnJlcXVlc3QpOyBlcnIgIT0gbmlsIHtcbiAgIDExMFx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMTExXHRcdFx0XHRcImVycm9yXCI6ICAgXCJJbnZhbGlkIHJlcXVlc3QgZm9ybWF0XCIsXG4gICAxMTJcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDExM1x0XHRcdH0pXG4gICAxMTRcdFx0XHRyZXR1cm5cbiAgIDExNVx0XHR9XG4gICAxMTZcdFxuICAgMTE3XHRcdC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgMTE4XHRcdGlmIHJlcXVlc3QuQ3VzdG9tZXJJRCA9PSBcIlwiIHtcbiAgIDExOVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMTIwXHRcdFx0XHRcImVycm9yXCI6IFwiY3VzdG9tZXJfaWQgaXMgcmVxdWlyZWRcIixcbiAgIDEyMVx0XHRcdH0pXG4gICAxMjJcdFx0XHRyZXR1cm5cbiAgIDEyM1x0XHR9XG4gICAxMjRcdFxuICAgMTI1XHRcdGlmIHJlcXVlc3QuUHVycG9zZSA9PSBcIlwiIHtcbiAgIDEyNlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMTI3XHRcdFx0XHRcImVycm9yXCI6IFwicHVycG9zZSBpcyByZXF1aXJlZFwiLFxuICAgMTI4XHRcdFx0fSlcbiAgIDEyOVx0XHRcdHJldHVyblxuICAgMTMwXHRcdH1cbiAgIDEzMVx0XG4gICAxMzJcdFx0Ly8gSW5pdGlhdGUgdGhlIGN1c3RvbWVyLXRvLUFJIGNhbGxcbiAgIDEzM1x0XHRjYWxsLCBlcnIgOj0gY2guY2FsbE1hbmFnZXIuSW5pdGlhdGVDdXN0b21lckNhbGwocmVxdWVzdClcbiAgIDEzNFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDEzNVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMTM2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gaW5pdGlhdGUgY3VzdG9tZXIgY2FsbFwiLFxuICAgMTM3XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAxMzhcdFx0XHR9KVxuICAgMTM5XHRcdFx0cmV0dXJuXG4gICAxNDBcdFx0fVxuICAgMTQxXHRcbiAgIDE0Mlx0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGN1c3RvbWVyXG4gICAxNDNcdFx0Y3VzdG9tZXJUb2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIHJlcXVlc3QuQ3VzdG9tZXJJRCwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAxNDRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxNDVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDE0Nlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIGN1c3RvbWVyIEFnb3JhIHRva2VuXCIsXG4gICAxNDdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDE0OFx0XHRcdH0pXG4gICAxNDlcdFx0XHRyZXR1cm5cbiAgIDE1MFx0XHR9XG4uLi5cbiAgIDE3M1x0XG4gICAxNzRcdFx0Ly8gQWRkIGluaXRpYWwgQUkgZ3JlZXRpbmdcbiAgIDE3NVx0XHRncmVldGluZyA6PSBjaC5nZW5lcmF0ZUN1c3RvbWVyQ2FsbEdyZWV0aW5nKGNhbGwpXG4gICAxNzZcdFx0Y2FsbC5BZGRNZXNzYWdlKFwiYWlcIiwgZ3JlZXRpbmcsIFwidGV4dFwiKVxuICAgMTc3XHRcbiAgIDE3OFx0XHQvLyBQcmVwYXJlIEFJIGluZm9cbiAgIDE3OVx0XHRhaUluZm8gOj0gbW9kZWxzLkFJSW5mb3tcbiAgIDE4MFx0XHRcdE5hbWU6ICAgICAgICBcIkFJIFN1cHBvcnQgQXNzaXN0YW50XCIsXG4gICAxODFcdFx0XHRQZXJzb25hbGl0eTogY2FsbC5NZXRhZGF0YS5BSVBlcnNvbmFsaXR5LFxuICAgMTgyXHRcdFx0TGFuZ3VhZ2U6ICAgIGNhbGwuTWV0YWRhdGEuTGFuZ3VhZ2UsXG4gICAxODNcdFx0XHRDYXBhYmlsaXRpZXM6IFtdc3RyaW5ne1xuICAgMTg0XHRcdFx0XHRcImJpbGxpbmdfc3VwcG9ydFwiLFxuICAgMTg1XHRcdFx0XHRcInRlY2huaWNhbF9zdXBwb3J0XCIsXG4gICAxODZcdFx0XHRcdFwiYWNjb3VudF9tYW5hZ2VtZW50XCIsXG4gICAxODdcdFx0XHRcdFwiZ2VuZXJhbF9pbnF1aXJpZXNcIixcbiAgIDE4OFx0XHRcdFx0XCJwYXNzd29yZF9yZXNldFwiLFxuICAgMTg5XHRcdFx0XHRcInN1YnNjcmlwdGlvbl9tYW5hZ2VtZW50XCIsXG4gICAxOTBcdFx0XHR9LFxuICAgMTkxXHRcdH1cbiAgIDE5Mlx0XG4gICAxOTNcdFx0cmVzcG9uc2UgOj0gbW9kZWxzLkluaXRpYXRlQ3VzdG9tZXJDYWxsUmVzcG9uc2V7XG4gICAxOTRcdFx0XHRDYWxsSUQ6ICAgICAgY2FsbC5JRCxcbiAgIDE5NVx0XHRcdENoYW5uZWxOYW1lOiBjYWxsLkNoYW5uZWxOYW1lLFxuICAgMTk2XHRcdFx0VG9rZW46ICAgICAgIGN1c3RvbWVyVG9rZW4uVG9rZW4sXG4gICAxOTdcdFx0XHRTdGF0dXM6ICAgICAgc3RyaW5nKGNhbGwuU3RhdHVzKSxcbiAgIDE5OFx0XHRcdE1lc3NhZ2U6ICAgICBcIkN1c3RvbWVyIGNhbGwgaW5pdGlhdGVkIHN1Y2Nlc3NmdWxseS4gQUkgaXMgcmVhZHkgdG8gYXNzaXN0LlwiLFxuICAgMTk5XHRcdFx0QUlJbmZvOiAgICAgIGFpSW5mbyxcbiAgIDIwMFx0XHR9XG4gICAyMDFcdFxuICAgMjAyXHRcdGMuSlNPTihodHRwLlN0YXR1c0NyZWF0ZWQsIHJlc3BvbnNlKVxuICAgMjAzXHR9XG4uLi5cbiAgIDI0OFx0XG4gICAyNDlcdFx0Ly8gSm9pbiB0aGUgY2FsbFxuICAgMjUwXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Kb2luQ2FsbChyZXF1ZXN0LkNhbGxJRCwgcmVxdWVzdC5DdXN0b21lcklEKVxuICAgMjUxXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMjUyXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAyNTNcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBqb2luIGNhbGxcIixcbiAgIDI1NFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMjU1XHRcdFx0fSlcbiAgIDI1Nlx0XHRcdHJldHVyblxuICAgMjU3XHRcdH1cbiAgIDI1OFx0XG4gICAyNTlcdFx0Ly8gR2VuZXJhdGUgQWdvcmEgdG9rZW4gZm9yIHRoZSBjdXN0b21lclxuICAgMjYwXHRcdHRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgcmVxdWVzdC5DdXN0b21lcklELCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgIDI2MVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDI2Mlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMjYzXHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgQWdvcmEgdG9rZW5cIixcbiAgIDI2NFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMjY1XHRcdFx0fSlcbiAgIDI2Nlx0XHRcdHJldHVyblxuICAgMjY3XHRcdH1cbiAgIDI2OFx0XG4gICAyNjlcdFx0cmVzcG9uc2UgOj0gbW9kZWxzLkpvaW5DYWxsUmVzcG9uc2V7XG4gICAyNzBcdFx0XHRDaGFubmVsTmFtZTogY2FsbC5DaGFubmVsTmFtZSxcbiAgIDI3MVx0XHRcdFRva2VuOiAgICAgICB0b2tlbi5Ub2tlbixcbiAgIDI3Mlx0XHRcdENhbGxJbmZvOiAgICAqY2FsbCxcbiAgIDI3M1x0XHR9XG4gICAyNzRcdFxuICAgMjc1XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCByZXNwb25zZSlcbiAgIDI3Nlx0fVxuLi4uXG4gICAzNjlcdFxuICAgMzcwXHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBzdGF0cylcbiAgIDM3MVx0fVxuICAgMzcyXHRcbiAgIDM3M1x0Ly8gR2VuZXJhdGVUb2tlbiBoYW5kbGVzIFBPU1QgL2FwaS9jYWxscy86Y2FsbF9pZC90b2tlblxuICAgMzc0XHRmdW5jIChjaCAqQ2FsbEhhbmRsZXIpIEdlbmVyYXRlVG9rZW4oYyAqZ2luLkNvbnRleHQpIHtcbiAgIDM3NVx0XHRjYWxsSUQgOj0gYy5QYXJhbShcImNhbGxfaWRcIilcbiAgIDM3Nlx0XHRpZiBjYWxsSUQgPT0gXCJcIiB7XG4gICAzNzdcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgIDM3OFx0XHRcdFx0XCJlcnJvclwiOiBcImNhbGxfaWQgaXMgcmVxdWlyZWRcIixcbiAgIDM3OVx0XHRcdH0pXG4gICAzODBcdFx0XHRyZXR1cm5cbiAgIDM4MVx0XHR9XG4gICAzODJcdFxuICAgMzgzXHRcdHZhciByZXF1ZXN0IHN0cnVjdCB7XG4gICAzODRcdFx0XHRVc2VySUQgc3RyaW5nIGBqc29uOlwidXNlcl9pZFwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgMzg1XHRcdFx0Um9sZSAgIGludCAgICBganNvbjpcInJvbGVcImBcbiAgIDM4Nlx0XHR9XG4gICAzODdcdFxuICAgMzg4XHRcdGlmIGVyciA6PSBjLlNob3VsZEJpbmRKU09OKCZyZXF1ZXN0KTsgZXJyICE9IG5pbCB7XG4gICAzODlcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgIDM5MFx0XHRcdFx0XCJlcnJvclwiOiAgIFwiSW52YWxpZCByZXF1ZXN0IGZvcm1hdFwiLFxuICAgMzkxXHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAzOTJcdFx0XHR9KVxuICAgMzkzXHRcdFx0cmV0dXJuXG4gICAzOTRcdFx0fVxuICAgMzk1XHRcbiAgIDM5Nlx0XHQvLyBEZWZhdWx0IHJvbGUgdG8gcHVibGlzaGVyIGlmIG5vdCBzcGVjaWZpZWRcbiAgIDM5N1x0XHRpZiByZXF1ZXN0LlJvbGUgPT0gMCB7XG4gICAzOThcdFx0XHRyZXF1ZXN0LlJvbGUgPSAxXG4gICAzOTlcdFx0fVxuICAgNDAwXHRcbiAgIDQwMVx0XHQvLyBHZXQgY2FsbCB0byB2YWxpZGF0ZSBpdCBleGlzdHNcbiAgIDQwMlx0XHRjYWxsLCBlcnIgOj0gY2guY2FsbE1hbmFnZXIuR2V0Q2FsbChjYWxsSUQpXG4gICA0MDNcdFx0aWYgZXJyICE9IG5pbCB7XG4gICA0MDRcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNOb3RGb3VuZCwgZ2luLkh7XG4gICA0MDVcdFx0XHRcdFwiZXJyb3JcIjogICBcIkNhbGwgbm90IGZvdW5kXCIsXG4gICA0MDZcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDQwN1x0XHRcdH0pXG4gICA0MDhcdFx0XHRyZXR1cm5cbiAgIDQwOVx0XHR9XG4gICA0MTBcdFxuICAgNDExXHRcdC8vIEdlbmVyYXRlIHRva2VuXG4gICA0MTJcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCByZXF1ZXN0LlVzZXJJRCwgcmVxdWVzdC5Sb2xlKVxuICAgNDEzXHRcdGlmIGVyciAhPSBuaWwge1xuICAgNDE0XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICA0MTVcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlblwiLFxuICAgNDE2XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICA0MTdcdFx0XHR9KVxuICAgNDE4XHRcdFx0cmV0dXJuXG4gICA0MTlcdFx0fVxuICAgNDIwXHRcbiAgIDQyMVx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgZ2luLkh7XG4gICA0MjJcdFx0XHRcInRva2VuXCI6ICAgICAgICB0b2tlbi5Ub2tlbixcbiAgIDQyM1x0XHRcdFwiZXhwaXJlc19hdFwiOiAgIHRva2VuLkV4cGlyZXNBdCxcbiAgIDQyNFx0XHRcdFwiY2hhbm5lbF9uYW1lXCI6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICA0MjVcdFx0XHRcInVzZXJfaWRcIjogICAgICByZXF1ZXN0LlVzZXJJRCxcbiAgIDQyNlx0XHRcdFwicm9sZVwiOiAgICAgICAgIHJlcXVlc3QuUm9sZSxcbiAgIDQyN1x0XHR9KVxuICAgNDI4XHR9XG4uLi5cblBhdGg6IG1haW4uZ29cbi4uLlxuICAgIDY1XHRcbiAgICA2Nlx0XHQvLyBBUEkgcm91dGVzXG4gICAgNjdcdFx0YXBpIDo9IHJvdXRlci5Hcm91cChcIi9hcGlcIilcbiAgICA2OFx0XHR7XG4gICAgNjlcdFx0XHQvLyBDYWxsIG1hbmFnZW1lbnQgcm91dGVzXG4gICAgNzBcdFx0XHRjYWxscyA6PSBhcGkuR3JvdXAoXCIvY2FsbHNcIilcbiAgICA3MVx0XHRcdHtcbiAgICA3Mlx0XHRcdFx0Y2FsbHMuUE9TVChcIi9pbml0aWF0ZVwiLCBjYWxsSGFuZGxlci5Jbml0aWF0ZUNhbGwpICAgICAgICAgICAgICAgICAgICAvLyBBSSBjYWxscyBjdXN0b21lclxuICAgIDczXHRcdFx0XHRjYWxscy5QT1NUKFwiL2N1c3RvbWVyLWluaXRpYXRlXCIsIGNhbGxIYW5kbGVyLkluaXRpYXRlQ3VzdG9tZXJDYWxsKSAgIC8vIEN1c3RvbWVyIGNhbGxzIEFJXG4gICAgNzRcdFx0XHRcdGNhbGxzLlBPU1QoXCIvOmNhbGxfaWQvam9pblwiLCBjYWxsSGFuZGxlci5Kb2luQ2FsbClcbiAgICA3NVx0XHRcdFx0Y2FsbHMuR0VUKFwiLzpjYWxsX2lkXCIsIGNhbGxIYW5kbGVyLkdldENhbGwpXG4gICAgNzZcdFx0XHRcdGNhbGxzLlBPU1QoXCIvOmNhbGxfaWQvZW5kXCIsIGNhbGxIYW5kbGVyLkVuZENhbGwpXG4gICAgNzdcdFx0XHRcdGNhbGxzLkdFVChcIi9hY3RpdmVcIiwgY2FsbEhhbmRsZXIuR2V0QWN0aXZlQ2FsbHMpXG4gICAgNzhcdFx0XHRcdGNhbGxzLkdFVChcIi86Y2FsbF9pZC9zdGF0c1wiLCBjYWxsSGFuZGxlci5HZXRDYWxsU3RhdHMpXG4gICAgNzlcdFx0XHRcdGNhbGxzLlBPU1QoXCIvOmNhbGxfaWQvdG9rZW5cIiwgY2FsbEhhbmRsZXIuR2VuZXJhdGVUb2tlbilcbiAgICA4MFx0XHRcdH1cbiAgICA4MVx0XHR9XG4gICAgODJcdFxuICAgIDgzXHRcdC8vIFdlbGNvbWUgcm91dGVcbiAgICA4NFx0XHRyb3V0ZXIuR0VUKFwiL1wiLCBmdW5jKGMgKmdpbi5Db250ZXh0KSB7XG4gICAgODVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgZ2luLkh7XG4gICAgODZcdFx0XHRcdFwibWVzc2FnZVwiOiBcIvCfpJYgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXCIsXG4gICAgODdcdFx0XHRcdFwidmVyc2lvblwiOiBcIjEuMC4wXCIsXG4gICAgODhcdFx0XHRcdFwic3RhdHVzXCI6ICBcInJ1bm5pbmdcIixcbiAgICA4OVx0XHRcdFx0XCJlbmRwb2ludHNcIjogZ2luLkh7XG4gICAgOTBcdFx0XHRcdFx0XCJoZWFsdGhcIjogICAgICAgICAgICAgICAgXCJHRVQgL2hlYWx0aFwiLFxuICAgIDkxXHRcdFx0XHRcdFwiaW5pdGlhdGVfY2FsbFwiOiAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzL2luaXRpYXRlXCIsXG4gICAgOTJcdFx0XHRcdFx0XCJjdXN0b21lcl9pbml0aWF0ZVwiOiAgICAgXCJQT1NUIC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGVcIixcbiAgICA5M1x0XHRcdFx0XHRcImpvaW5fY2FsbFwiOiAgICAgICAgICAgICBcIlBPU1QgL2FwaS9jYWxscy86Y2FsbF9pZC9qb2luXCIsXG4gICAgOTRcdFx0XHRcdFx0XCJnZXRfY2FsbFwiOiAgICAgICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy86Y2FsbF9pZFwiLFxuICAgIDk1XHRcdFx0XHRcdFwiZW5kX2NhbGxcIjogICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2VuZFwiLFxuICAgIDk2XHRcdFx0XHRcdFwiYWN0aXZlX2NhbGxzXCI6ICAgICAgICAgIFwiR0VUIC9hcGkvY2FsbHMvYWN0aXZlXCIsXG4gICAgOTdcdFx0XHRcdFx0XCJjYWxsX3N0YXRzXCI6ICAgICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy86Y2FsbF9pZC9zdGF0c1wiLFxuICAgIDk4XHRcdFx0XHRcdFwiZ2VuZXJhdGVfdG9rZW5cIjogICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3Rva2VuXCIsXG4gICAgOTlcdFx0XHRcdH0sXG4gICAxMDBcdFx0XHR9KVxuICAgMTAxXHRcdH0pXG4gICAxMDJcdFxuICAgMTAzXHRcdGxvZy5QcmludGYoXCLwn5uj77iPIFJvdXRlcyBjb25maWd1cmVkIHN1Y2Nlc3NmdWxseVwiKVxuICAgMTA0XHRcdHJldHVybiByb3V0ZXJcbiAgIDEwNVx0fVxuLi4uXG5QYXRoOiBzZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvXG4gICAgIDFcdHBhY2thZ2Ugc2VydmljZXNcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJCYWNrZW5kL2NvbmZpZ1wiXG4gICAgIDVcdFx0XCJmbXRcIlxuICAgICA2XHRcdFwibG9nXCJcbiAgICAgN1x0XHRcInN0cmNvbnZcIlxuICAgICA4XHRcdFwidGltZVwiXG4gICAgIDlcdFxuICAgIDEwXHRcdFwiZ2l0aHViLmNvbS9BZ29yYUlPL1Rvb2xzL0R5bmFtaWNLZXkvQWdvcmFEeW5hbWljS2V5L2dvL3NyYy9ydGN0b2tlbmJ1aWxkZXIyXCJcbiAgICAxMVx0KVxuICAgIDEyXHRcbiAgICAxM1x0Ly8gQWdvcmFTZXJ2aWNlIGhhbmRsZXMgQWdvcmEgUlRDIG9wZXJhdGlvbnNcbiAgICAxNFx0dHlwZSBBZ29yYVNlcnZpY2Ugc3RydWN0IHtcbiAgICAxNVx0XHRjb25maWcgKmNvbmZpZy5Db25maWdcbiAgICAxNlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNvbnRhaW46XG4gICAgMTdcdFx0Ly8gLSBBZ29yYSBzZXJ2aWNlIGluc3RhbmNlXG4gICAgMThcdFx0Ly8gLSBNZWRpYSBub2RlIGZhY3RvcnlcbiAgICAxOVx0XHQvLyAtIEFjdGl2ZSBjb25uZWN0aW9ucyBtYXBcbiAgICAyMFx0XHQvLyAtIEF1ZGlvL1ZpZGVvIG9ic2VydmVyc1xuICAgIDIxXHR9XG4gICAgMjJcdFxuICAgIDIzXHQvLyBUb2tlblJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgZm9yIEFnb3JhIHRva2VuXG4gICAgMjRcdHR5cGUgVG9rZW5SZXF1ZXN0IHN0cnVjdCB7XG4gICAgMjVcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMjZcdFx0VXNlcklEICAgICAgc3RyaW5nIGBqc29uOlwidXNlcl9pZFwiYFxuICAgIDI3XHRcdFJvbGUgICAgICAgIGludCAgICBganNvbjpcInJvbGVcImAgLy8gMSA9IHB1Ymxpc2hlciwgMiA9IHN1YnNjcmliZXJcbiAgICAyOFx0fVxuICAgIDI5XHRcbiAgICAzMFx0Ly8gVG9rZW5SZXNwb25zZSByZXByZXNlbnRzIEFnb3JhIHRva2VuIHJlc3BvbnNlXG4gICAgMzFcdHR5cGUgVG9rZW5SZXNwb25zZSBzdHJ1Y3Qge1xuICAgIDMyXHRcdFRva2VuICAgICBzdHJpbmcgYGpzb246XCJ0b2tlblwiYFxuICAgIDMzXHRcdEV4cGlyZXNBdCBpbnQ2NCAgYGpzb246XCJleHBpcmVzX2F0XCJgXG4gICAgMzRcdH1cbiAgICAzNVx0XG4gICAgMzZcdC8vIE5ld0Fnb3JhU2VydmljZSBjcmVhdGVzIGEgbmV3IEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAzN1x0ZnVuYyBOZXdBZ29yYVNlcnZpY2UoY2ZnICpjb25maWcuQ29uZmlnKSAqQWdvcmFTZXJ2aWNlIHtcbiAgICAzOFx0XHRzZXJ2aWNlIDo9ICZBZ29yYVNlcnZpY2V7XG4gICAgMzlcdFx0XHRjb25maWc6IGNmZyxcbiAgICA0MFx0XHR9XG4gICAgNDFcdFxuICAgIDQyXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIGluaXRpYWxpemUgQWdvcmEgU0RLIGhlcmU6XG4gICAgNDNcdFx0Ly8gMS4gSW5pdGlhbGl6ZSBBZ29yYSBzZXJ2aWNlIHdpdGggYXBwIElEXG4gICAgNDRcdFx0Ly8gMi4gQ3JlYXRlIG1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDQ1XHRcdC8vIDMuIFNldCB1cCBhdWRpby92aWRlbyBwcm9jZXNzaW5nIGNhbGxiYWNrc1xuICAgIDQ2XHRcbiAgICA0N1x0XHRsb2cuUHJpbnRmKFwi8J+OryBBZ29yYSBzZXJ2aWNlIGluaXRpYWxpemVkIHdpdGggQXBwIElEOiAlc1wiLCBjZmcuQWdvcmEuQXBwSUQpXG4gICAgNDhcdFx0cmV0dXJuIHNlcnZpY2VcbiAgICA0OVx0fVxuICAgIDUwXHRcbiAgICA1MVx0Ly8gR2VuZXJhdGVUb2tlbiBnZW5lcmF0ZXMgYW4gQWdvcmEgUlRDIHRva2VuIGZvciBjaGFubmVsIGFjY2Vzc1xuICAgIDUyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZW5lcmF0ZVRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgKCpUb2tlblJlc3BvbnNlLCBlcnJvcikge1xuICAgIDUzXHRcdGxvZy5QcmludGYoXCLwn5SRIEdlbmVyYXRpbmcgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB1c2VyOiAlcywgcm9sZTogJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA1NFx0XG4gICAgNTVcdFx0Ly8gVG9rZW4gZXhwaXJlcyBpbiAyNCBob3Vyc1xuICAgIDU2XHRcdGV4cGlyYXRpb25UaW1lIDo9IHRpbWUuTm93KCkuQWRkKDI0ICogdGltZS5Ib3VyKS5Vbml4KClcbiAgICA1N1x0XG4gICAgNThcdFx0dmFyIHRva2VuIHN0cmluZ1xuICAgIDU5XHRcdHZhciBlcnIgZXJyb3JcbiAgICA2MFx0XG4gICAgNjFcdFx0aWYgYXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlICE9IFwiXCIge1xuICAgIDYyXHRcdFx0Ly8gR2VuZXJhdGUgcmVhbCB0b2tlbiB3aXRoIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA2M1x0XHRcdHRva2VuLCBlcnIgPSBhcy5nZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB1aW50MzIoZXhwaXJhdGlvblRpbWUpKVxuICAgIDY0XHRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjVcdFx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gZ2VuZXJhdGUgdG9rZW46ICV3XCIsIGVycilcbiAgICA2Nlx0XHRcdH1cbiAgICA2N1x0XHRcdGxvZy5QcmludGYoXCLinIUgR2VuZXJhdGVkIHJlYWwgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgIDY4XHRcdH0gZWxzZSB7XG4gICAgNjlcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgIDcwXHRcdFx0dG9rZW4gPSBhcy5nZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDcxXHRcdFx0bG9nLlByaW50ZihcIuKaoO+4jyBbTU9DS10gVXNpbmcgbW9jayB0b2tlbiAobm8gYXBwIGNlcnRpZmljYXRlIHByb3ZpZGVkKVwiKVxuICAgIDcyXHRcdH1cbiAgICA3M1x0XG4gICAgNzRcdFx0cmVzcG9uc2UgOj0gJlRva2VuUmVzcG9uc2V7XG4gICAgNzVcdFx0XHRUb2tlbjogICAgIHRva2VuLFxuICAgIDc2XHRcdFx0RXhwaXJlc0F0OiBleHBpcmF0aW9uVGltZSxcbiAgICA3N1x0XHR9XG4gICAgNzhcdFxuICAgIDc5XHRcdGxvZy5QcmludGYoXCLinIUgVG9rZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgODBcdFx0cmV0dXJuIHJlc3BvbnNlLCBuaWxcbiAgICA4MVx0fVxuICAgIDgyXHRcbiAgICA4M1x0Ly8gZ2VuZXJhdGVSZWFsVG9rZW4gZ2VuZXJhdGVzIGEgcmVhbCBBZ29yYSB0b2tlbiB1c2luZyB0aGUgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDg0XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQsIGV4cGlyYXRpb24gdWludDMyKSAoc3RyaW5nLCBlcnJvcikge1xuICAgIDg1XHRcdC8vIENvbnZlcnQgdXNlcklEIHRvIHVpbnQzMiAoQWdvcmEgZXhwZWN0cyBudW1lcmljIFVJRClcbiAgICA4Nlx0XHR2YXIgdWlkIHVpbnQzMlxuICAgIDg3XHRcdGlmIHVzZXJJRCAhPSBcIlwiIHtcbiAgICA4OFx0XHRcdGlmIHBhcnNlZFVJRCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VVaW50KHVzZXJJRCwgMTAsIDMyKTsgZXJyID09IG5pbCB7XG4gICAgODlcdFx0XHRcdHVpZCA9IHVpbnQzMihwYXJzZWRVSUQpXG4gICAgOTBcdFx0XHR9IGVsc2Uge1xuICAgIDkxXHRcdFx0XHQvLyBJZiB1c2VySUQgaXMgbm90IG51bWVyaWMsIGdlbmVyYXRlIGEgaGFzaC1iYXNlZCBVSURcbiAgICA5Mlx0XHRcdFx0dWlkID0gdWludDMyKGxlbih1c2VySUQpKSAvLyBTaW1wbGUgZmFsbGJhY2ssIGluIHByb2R1Y3Rpb24gdXNlIGEgcHJvcGVyIGhhc2hcbiAgICA5M1x0XHRcdH1cbiAgICA5NFx0XHR9IGVsc2Uge1xuICAgIDk1XHRcdFx0dWlkID0gMCAvLyBMZXQgQWdvcmEgYXNzaWduIGEgVUlEXG4gICAgOTZcdFx0fVxuICAgIDk3XHRcbiAgICA5OFx0XHQvLyBDb252ZXJ0IHJvbGUgdG8gQWdvcmEgcm9sZSB0eXBlXG4gICAgOTlcdFx0dmFyIGFnb3JhUm9sZSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVcbiAgIDEwMFx0XHRpZiByb2xlID09IDEge1xuICAgMTAxXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlUHVibGlzaGVyXG4gICAxMDJcdFx0fSBlbHNlIHtcbiAgIDEwM1x0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVN1YnNjcmliZXJcbiAgIDEwNFx0XHR9XG4gICAxMDVcdFxuICAgMTA2XHRcdC8vIEdlbmVyYXRlIHRva2VuIHVzaW5nIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgIDEwN1x0XHR0b2tlbiwgZXJyIDo9IHJ0Y3Rva2VuYnVpbGRlcjIuQnVpbGRUb2tlbldpdGhVaWQoXG4gICAxMDhcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwSUQsXG4gICAxMDlcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUsXG4gICAxMTBcdFx0XHRjaGFubmVsTmFtZSxcbiAgIDExMVx0XHRcdHVpZCxcbiAgIDExMlx0XHRcdGFnb3JhUm9sZSxcbiAgIDExM1x0XHRcdGV4cGlyYXRpb24sXG4gICAxMTRcdFx0XHRleHBpcmF0aW9uLCAvLyBQcml2aWxlZ2UgZXhwaXJlZCB0aW1lc3RhbXAgKHNhbWUgYXMgdG9rZW4gZXhwaXJhdGlvbilcbiAgIDExNVx0XHQpXG4gICAxMTZcdFxuICAgMTE3XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTE4XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gYnVpbGQgQWdvcmEgdG9rZW46ICV3XCIsIGVycilcbiAgIDExOVx0XHR9XG4gICAxMjBcdFxuICAgMTIxXHRcdHJldHVybiB0b2tlbiwgbmlsXG4gICAxMjJcdH1cbiAgIDEyM1x0XG4gICAxMjRcdC8vIGdlbmVyYXRlTW9ja1Rva2VuIGdlbmVyYXRlcyBhIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAxMjVcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgc3RyaW5nIHtcbiAgIDEyNlx0XHR0aW1lc3RhbXAgOj0gdGltZS5Ob3coKS5Vbml4KClcbiAgIDEyN1x0XHRyZXR1cm4gZm10LlNwcmludGYoXCJtb2NrX3Rva2VuXyVzXyVzXyVkXyVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHRpbWVzdGFtcClcbiAgIDEyOFx0fVxuLi4uXG5QYXRoOiBtb2RlbHMvY2FsbC5nb1xuLi4uXG4gICAgMjdcdFxuICAgIDI4XHQvLyBDYWxsIHJlcHJlc2VudHMgYSBjdXN0b21lciBzdXBwb3J0IGNhbGwgc2Vzc2lvblxuICAgIDI5XHR0eXBlIENhbGwgc3RydWN0IHtcbiAgICAzMFx0XHRJRCAgICAgICAgICBzdHJpbmcgICAgICAgYGpzb246XCJpZFwiYFxuICAgIDMxXHRcdEN1c3RvbWVySUQgIHN0cmluZyAgICAgICBganNvbjpcImN1c3RvbWVyX2lkXCJgXG4gICAgMzJcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nICAgICAgIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMzNcdFx0U3RhdHVzICAgICAgQ2FsbFN0YXR1cyAgIGBqc29uOlwic3RhdHVzXCJgXG4gICAgMzRcdFx0VHlwZSAgICAgICAgQ2FsbFR5cGUgICAgIGBqc29uOlwidHlwZVwiYFxuICAgIDM1XHRcdFN0YXJ0VGltZSAgIHRpbWUuVGltZSAgICBganNvbjpcInN0YXJ0X3RpbWVcImBcbiAgICAzNlx0XHRFbmRUaW1lICAgICAqdGltZS5UaW1lICAgYGpzb246XCJlbmRfdGltZSxvbWl0ZW1wdHlcImBcbiAgICAzN1x0XHREdXJhdGlvbiAgICBpbnQ2NCAgICAgICAgYGpzb246XCJkdXJhdGlvblwiYCAvLyBpbiBzZWNvbmRzXG4gICAgMzhcdFx0VHJhbnNjcmlwdCAgW11NZXNzYWdlICAgIGBqc29uOlwidHJhbnNjcmlwdFwiYFxuICAgIDM5XHRcdE1ldGFkYXRhICAgIENhbGxNZXRhZGF0YSBganNvbjpcIm1ldGFkYXRhXCJgXG4gICAgNDBcdH1cbiAgICA0MVx0XG4gICAgNDJcdC8vIE1lc3NhZ2UgcmVwcmVzZW50cyBhIHNpbmdsZSBtZXNzYWdlIGluIHRoZSBjb252ZXJzYXRpb25cbiAgICA0M1x0dHlwZSBNZXNzYWdlIHN0cnVjdCB7XG4gICAgNDRcdFx0SUQgICAgICAgIHN0cmluZyAgICBganNvbjpcImlkXCJgXG4gICAgNDVcdFx0U3BlYWtlciAgIHN0cmluZyAgICBganNvbjpcInNwZWFrZXJcImAgLy8gXCJhaVwiIG9yIFwiY3VzdG9tZXJcIlxuICAgIDQ2XHRcdENvbnRlbnQgICBzdHJpbmcgICAgYGpzb246XCJjb250ZW50XCJgXG4gICAgNDdcdFx0VGltZXN0YW1wIHRpbWUuVGltZSBganNvbjpcInRpbWVzdGFtcFwiYFxuICAgIDQ4XHRcdFR5cGUgICAgICBzdHJpbmcgICAgYGpzb246XCJ0eXBlXCJgIC8vIFwidGV4dFwiLCBcImF1ZGlvXCJcbiAgICA0OVx0fVxuICAgIDUwXHRcbiAgICA1MVx0Ly8gQ2FsbE1ldGFkYXRhIGNvbnRhaW5zIGFkZGl0aW9uYWwgaW5mb3JtYXRpb24gYWJvdXQgdGhlIGNhbGxcbiAgICA1Mlx0dHlwZSBDYWxsTWV0YWRhdGEgc3RydWN0IHtcbiAgICA1M1x0XHRQdXJwb3NlICAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwicHVycG9zZVwiYFxuICAgIDU0XHRcdEN1c3RvbWVySW5mbyAgQ3VzdG9tZXJJbmZvICAgICAgYGpzb246XCJjdXN0b21lcl9pbmZvXCJgXG4gICAgNTVcdFx0QUlQZXJzb25hbGl0eSBzdHJpbmcgICAgICAgICAgICBganNvbjpcImFpX3BlcnNvbmFsaXR5XCJgXG4gICAgNTZcdFx0TGFuZ3VhZ2UgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcImxhbmd1YWdlXCJgXG4gICAgNTdcdFx0VGFncyAgICAgICAgICBbXXN0cmluZyAgICAgICAgICBganNvbjpcInRhZ3NcImBcbiAgICA1OFx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgIDU5XHR9XG4gICAgNjBcdFxuICAgIDYxXHQvLyBDdXN0b21lckluZm8gY29udGFpbnMgY3VzdG9tZXIgZGV0YWlsc1xuICAgIDYyXHR0eXBlIEN1c3RvbWVySW5mbyBzdHJ1Y3Qge1xuICAgIDYzXHRcdE5hbWUgICAgICAgIHN0cmluZyBganNvbjpcIm5hbWVcImBcbiAgICA2NFx0XHRFbWFpbCAgICAgICBzdHJpbmcgYGpzb246XCJlbWFpbFwiYFxuICAgIDY1XHRcdFBob25lICAgICAgIHN0cmluZyBganNvbjpcInBob25lXCJgXG4gICAgNjZcdFx0QWNjb3VudElEICAgc3RyaW5nIGBqc29uOlwiYWNjb3VudF9pZFwiYFxuICAgIDY3XHRcdFByZWZlcmVuY2VzIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwicHJlZmVyZW5jZXNcImBcbiAgICA2OFx0fVxuICAgIDY5XHRcbiAgICA3MFx0Ly8gSW5pdGlhdGVDYWxsUmVxdWVzdCByZXByZXNlbnRzIHRoZSByZXF1ZXN0IHRvIHN0YXJ0IGEgbmV3IGNhbGxcbiAgICA3MVx0dHlwZSBJbml0aWF0ZUNhbGxSZXF1ZXN0IHN0cnVjdCB7XG4gICAgNzJcdFx0Q3VzdG9tZXJJRCAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcImN1c3RvbWVyX2lkXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAgNzNcdFx0UHVycG9zZSAgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcInB1cnBvc2VcIiBiaW5kaW5nOlwicmVxdWlyZWRcImBcbiAgICA3NFx0XHRDdXN0b21lckluZm8gIEN1c3RvbWVySW5mbyAgICAgIGBqc29uOlwiY3VzdG9tZXJfaW5mb1wiYFxuICAgIDc1XHRcdEFJUGVyc29uYWxpdHkgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJhaV9wZXJzb25hbGl0eVwiYFxuICAgIDc2XHRcdExhbmd1YWdlICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgIDc3XHRcdEN1c3RvbUZpZWxkcyAgbWFwW3N0cmluZ11zdHJpbmcgYGpzb246XCJjdXN0b21fZmllbGRzXCJgXG4gICAgNzhcdH1cbiAgICA3OVx0XG4gICAgODBcdC8vIEluaXRpYXRlQ2FsbFJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIHJlc3BvbnNlIHdoZW4gaW5pdGlhdGluZyBhIGNhbGxcbiAgICA4MVx0dHlwZSBJbml0aWF0ZUNhbGxSZXNwb25zZSBzdHJ1Y3Qge1xuICAgIDgyXHRcdENhbGxJRCAgICAgIHN0cmluZyBganNvbjpcImNhbGxfaWRcImBcbiAgICA4M1x0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICA4NFx0XHRUb2tlbiAgICAgICBzdHJpbmcgYGpzb246XCJ0b2tlblwiYFxuICAgIDg1XHRcdFN0YXR1cyAgICAgIHN0cmluZyBganNvbjpcInN0YXR1c1wiYFxuICAgIDg2XHRcdE1lc3NhZ2UgICAgIHN0cmluZyBganNvbjpcIm1lc3NhZ2VcImBcbiAgICA4N1x0fVxuICAgIDg4XHRcbiAgICA4OVx0Ly8gSm9pbkNhbGxSZXF1ZXN0IHJlcHJlc2VudHMgdGhlIHJlcXVlc3Qgd2hlbiBhIHVzZXIgam9pbnMgYSBjYWxsXG4gICAgOTBcdHR5cGUgSm9pbkNhbGxSZXF1ZXN0IHN0cnVjdCB7XG4gICAgOTFcdFx0Q2FsbElEICAgICBzdHJpbmcgYGpzb246XCJjYWxsX2lkXCJgXG4gICAgOTJcdFx0Q3VzdG9tZXJJRCBzdHJpbmcgYGpzb246XCJjdXN0b21lcl9pZFwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgIDkzXHR9XG4gICAgOTRcdFxuICAgIDk1XHQvLyBKb2luQ2FsbFJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIHJlc3BvbnNlIHdoZW4gam9pbmluZyBhIGNhbGxcbiAgICA5Nlx0dHlwZSBKb2luQ2FsbFJlc3BvbnNlIHN0cnVjdCB7XG4gICAgOTdcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgOThcdFx0VG9rZW4gICAgICAgc3RyaW5nIGBqc29uOlwidG9rZW5cImBcbiAgICA5OVx0XHRDYWxsSW5mbyAgICBDYWxsICAgYGpzb246XCJjYWxsX2luZm9cImBcbiAgIDEwMFx0fVxuLi4uXG4gICAxMTJcdFxuICAgMTEzXHQvLyBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIHJlc3BvbnNlIHdoZW4gY3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwgdG8gQUlcbiAgIDExNFx0dHlwZSBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNlIHN0cnVjdCB7XG4gICAxMTVcdFx0Q2FsbElEICAgICAgc3RyaW5nIGBqc29uOlwiY2FsbF9pZFwiYFxuICAgMTE2XHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgMTE3XHRcdFRva2VuICAgICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAxMThcdFx0U3RhdHVzICAgICAgc3RyaW5nIGBqc29uOlwic3RhdHVzXCJgXG4gICAxMTlcdFx0TWVzc2FnZSAgICAgc3RyaW5nIGBqc29uOlwibWVzc2FnZVwiYFxuICAgMTIwXHRcdEFJSW5mbyAgICAgIEFJSW5mbyBganNvbjpcImFpX2luZm9cImBcbiAgIDEyMVx0fVxuICAgMTIyXHRcbiAgIDEyM1x0Ly8gQUlJbmZvIGNvbnRhaW5zIGluZm9ybWF0aW9uIGFib3V0IHRoZSBBSSBhZ2VudFxuICAgMTI0XHR0eXBlIEFJSW5mbyBzdHJ1Y3Qge1xuICAgMTI1XHRcdE5hbWUgICAgICAgIHN0cmluZyBganNvbjpcIm5hbWVcImBcbiAgIDEyNlx0XHRQZXJzb25hbGl0eSBzdHJpbmcgYGpzb246XCJwZXJzb25hbGl0eVwiYFxuICAgMTI3XHRcdExhbmd1YWdlICAgIHN0cmluZyBganNvbjpcImxhbmd1YWdlXCJgXG4gICAxMjhcdFx0Q2FwYWJpbGl0aWVzIFtdc3RyaW5nIGBqc29uOlwiY2FwYWJpbGl0aWVzXCJgXG4gICAxMjlcdH1cbi4uLlxuICAgMTM4XHRcbiAgIDEzOVx0Ly8gTmV3Q2FsbCBjcmVhdGVzIGEgbmV3IGNhbGwgaW5zdGFuY2VcbiAgIDE0MFx0ZnVuYyBOZXdDYWxsKGN1c3RvbWVySUQsIHB1cnBvc2Ugc3RyaW5nLCBjdXN0b21lckluZm8gQ3VzdG9tZXJJbmZvKSAqQ2FsbCB7XG4gICAxNDFcdFx0Y2FsbElEIDo9IHV1aWQuTmV3KCkuU3RyaW5nKClcbiAgIDE0Mlx0XHRjaGFubmVsTmFtZSA6PSBcImFpX3N1cHBvcnRfXCIgKyBjYWxsSURcbiAgIDE0M1x0XG4gICAxNDRcdFx0cmV0dXJuICZDYWxse1xuICAgMTQ1XHRcdFx0SUQ6ICAgICAgICAgIGNhbGxJRCxcbiAgIDE0Nlx0XHRcdEN1c3RvbWVySUQ6ICBjdXN0b21lcklELFxuICAgMTQ3XHRcdFx0Q2hhbm5lbE5hbWU6IGNoYW5uZWxOYW1lLFxuICAgMTQ4XHRcdFx0U3RhdHVzOiAgICAgIENhbGxTdGF0dXNQZW5kaW5nLFxuICAgMTQ5XHRcdFx0VHlwZTogICAgICAgIENhbGxUeXBlQUlUb0N1c3RvbWVyLCAvLyBEZWZhdWx0IHRvIEFJIGNhbGxpbmcgY3VzdG9tZXJcbiAgIDE1MFx0XHRcdFN0YXJ0VGltZTogICB0aW1lLk5vdygpLFxuICAgMTUxXHRcdFx0VHJhbnNjcmlwdDogIG1ha2UoW11NZXNzYWdlLCAwKSxcbiAgIDE1Mlx0XHRcdE1ldGFkYXRhOiBDYWxsTWV0YWRhdGF7XG4gICAxNTNcdFx0XHRcdFB1cnBvc2U6ICAgICAgIHB1cnBvc2UsXG4gICAxNTRcdFx0XHRcdEN1c3RvbWVySW5mbzogIGN1c3RvbWVySW5mbyxcbiAgIDE1NVx0XHRcdFx0QUlQZXJzb25hbGl0eTogXCJoZWxwZnVsXCIsXG4gICAxNTZcdFx0XHRcdExhbmd1YWdlOiAgICAgIFwiZW5cIixcbiAgIDE1N1x0XHRcdFx0VGFnczogICAgICAgICAgbWFrZShbXXN0cmluZywgMCksXG4gICAxNThcdFx0XHRcdEN1c3RvbUZpZWxkczogIG1ha2UobWFwW3N0cmluZ11zdHJpbmcpLFxuICAgMTU5XHRcdFx0fSxcbiAgIDE2MFx0XHR9XG4gICAxNjFcdH1cbiAgIDE2Mlx0XG4gICAxNjNcdC8vIE5ld0N1c3RvbWVyQ2FsbCBjcmVhdGVzIGEgbmV3IGNhbGwgaW5zdGFuY2Ugd2hlcmUgY3VzdG9tZXIgY2FsbHMgQUlcbiAgIDE2NFx0ZnVuYyBOZXdDdXN0b21lckNhbGwoY3VzdG9tZXJJRCwgcHVycG9zZSBzdHJpbmcsIGN1c3RvbWVySW5mbyBDdXN0b21lckluZm8pICpDYWxsIHtcbiAgIDE2NVx0XHRjYWxsSUQgOj0gdXVpZC5OZXcoKS5TdHJpbmcoKVxuICAgMTY2XHRcdGNoYW5uZWxOYW1lIDo9IFwiY3VzdG9tZXJfYWlfXCIgKyBjYWxsSURcbiAgIDE2N1x0XG4gICAxNjhcdFx0cmV0dXJuICZDYWxse1xuICAgMTY5XHRcdFx0SUQ6ICAgICAgICAgIGNhbGxJRCxcbiAgIDE3MFx0XHRcdEN1c3RvbWVySUQ6ICBjdXN0b21lcklELFxuICAgMTcxXHRcdFx0Q2hhbm5lbE5hbWU6IGNoYW5uZWxOYW1lLFxuICAgMTcyXHRcdFx0U3RhdHVzOiAgICAgIENhbGxTdGF0dXNQZW5kaW5nLFxuICAgMTczXHRcdFx0VHlwZTogICAgICAgIENhbGxUeXBlQ3VzdG9tZXJUb0FJLCAvLyBDdXN0b21lciBjYWxsaW5nIEFJXG4gICAxNzRcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE3NVx0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNzZcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTc3XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTc4XHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxNzlcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTgwXHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxODFcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTgyXHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hLi4uIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxTWZFazhVU1cxNDJld016NWgzWUxCMiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlfX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA2OjUxOjEyLjU5OVoiLCJyZXNwb25zZV90ZXh0IjoiTm93IGxldCBtZSBjaGVjayB0aGUgc2VydmVyIGxvZ3MgdG8gc2VlIHdoYXQncyBoYXBwZW5pbmcgd2hlbiB0aGUgdG9rZW4gaXMgZ2VuZXJhdGVkOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6Ijc3YWU3ODQ1LWU3YzgtNGIzMS05ZmZmLTcxOWJhMGViMzk1MyIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUNBVWpoaExiSkpiU2JRUnNYdDJ5d1oiLCJ0b29sX25hbWUiOiJyZWFkLXByb2Nlc3MiLCJpbnB1dF9qc29uIjoie1widGVybWluYWxfaWRcIjogMjgsIFwid2FpdFwiOiB0cnVlLCBcIm1heF93YWl0X3NlY29uZHNcIjogNX0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJQcm9jZXNzIGxhdW5jaGVkIHdpdGggUElEIDI4IiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxUThCM0VSTnM1RnZjdDZyRHV4VXA2eiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MjgsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMjZUMDY6NTE6NTkuNjMwWiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMjM1ZTNkZTgtOGM3NS00MTljLWJhNDItNWQwMGVjNTI2ZDJmIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxRW9CM21OUENIaldna1g0SzdCbWlBZSIsInRvb2xfbmFtZSI6ImxhdW5jaC1wcm9jZXNzIiwiaW5wdXRfanNvbiI6IntcImNvbW1hbmRcIjogXCJsc29mIC10aTo4MDgwIHwgeGFyZ3Mga2lsbCAtOVwiLCBcIndhaXRcIjogdHJ1ZSwgXCJtYXhfd2FpdF9zZWNvbmRzXCI6IDV9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiTGV0IG1lIGNoZWNrIGlmIHRoZXJlJ3MgYWxyZWFkeSBhIHNlcnZlciBydW5uaW5nIGFuZCBzdG9wIGl0OlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiSGVyZSBpcyB0aGUgb3V0cHV0IGZyb20gdGVybWluYWwgMjggKHN0YXR1czogY29tcGxldGVkKTpcbjxvdXRwdXQ+MjAyNS8wNS8yNiAxMzo1MjowMSDwn5qAIFN0YXJ0aW5nIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFxyXG4yMDI1LzA1LzI2IDEzOjUyOjAxIPCfk4sgU2VydmVyOiAwLjAuMC4wOjgwODBcclxuMjAyNS8wNS8yNiAxMzo1MjowMSDwn46vIEFnb3JhIEFwcCBJRDogMDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcclxuMjAyNS8wNS8yNiAxMzo1MjowMSDwn46vIEFnb3JhIHNlcnZpY2UgaW5pdGlhbGl6ZWQgd2l0aCBBcHAgSUQ6IDAxNDUyMzQ4MGQ5NTQ0YmU4MDBhMDE0ZjM1YmRjZmFjXHJcbjIwMjUvMDUvMjYgMTM6NTI6MDEg8J+bo++4jyBSb3V0ZXMgY29uZmlndXJlZCBzdWNjZXNzZnVsbHlcclxuMjAyNS8wNS8yNiAxMzo1MjowMSDinIUgU2VydmVyIHN0YXJ0aW5nIG9uIDAuMC4wLjA6ODA4MFxyXG4yMDI1LzA1LzI2IDEzOjUyOjAxIOKdjCBGYWlsZWQgdG8gc3RhcnQgc2VydmVyOiBsaXN0ZW4gdGNwIDAuMC4wLjA6ODA4MDogYmluZDogYWRkcmVzcyBhbHJlYWR5IGluIHVzZVxyXG5leGl0IHN0YXR1cyAxXHJcbjwvb3V0cHV0PlxuPHJldHVybi1jb2RlPlxuMVxuPC9yZXR1cm4tY29kZT5cbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUNBVWpoaExiSkpiU2JRUnNYdDJ5d1oifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjI4LCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA2OjUyOjQ0LjU5NloiLCJyZXNwb25zZV90ZXh0IjoiTGV0IG1lIGNoZWNrIGlmIHRoZXJlJ3MgYWxyZWFkeSBhIHNlcnZlciBydW5uaW5nIGFuZCBzdG9wIGl0OlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjhiMDljMmZlLWI3YTctNDYzMC04YWY2LTMyMTkzM2NkNDcyNSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTM3cTNUeWtZQlFNaktQRVAyRUJ3NDciLCJ0b29sX25hbWUiOiJsYXVuY2gtcHJvY2VzcyIsImlucHV0X2pzb24iOiJ7XCJjb21tYW5kXCI6IFwiZ28gcnVuIG1haW4uZ29cIiwgXCJ3YWl0XCI6IGZhbHNlLCBcIm1heF93YWl0X3NlY29uZHNcIjogMTB9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiTm93IGxldCBtZSBzdGFydCB0aGUgc2VydmVyIGFnYWluOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG48cmV0dXJuLWNvZGU+XG4wXG48L3JldHVybi1jb2RlPlxuPHN0ZG91dD5cblxuPC9zdGRvdXQ+XG48c3RkZXJyPlxuXG48L3N0ZGVycj4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFFb0IzbU5QQ0hqV2drWDRLN0JtaUFlIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIiwiZm9sZGVyUm9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjoyOSwiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNjo1MzoyMS4xNzRaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgc3RhcnQgdGhlIHNlcnZlciBhZ2FpbjpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiIyOTU3YWE2ZS04NTcyLTRjZTEtOGUzNS1hNzU3ODNhOWY3OGIiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFWZFhzajloeGRibnE3VTQzblpiRWRSIiwidG9vbF9uYW1lIjoicmVhZC1wcm9jZXNzIiwiaW5wdXRfanNvbiI6IntcInRlcm1pbmFsX2lkXCI6IDMwLCBcIndhaXRcIjogdHJ1ZSwgXCJtYXhfd2FpdF9zZWNvbmRzXCI6IDV9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiUHJvY2VzcyBsYXVuY2hlZCB3aXRoIFBJRCAzMCIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTM3cTNUeWtZQlFNaktQRVAyRUJ3NDcifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNjo1Mzo1OC40MTFaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJiNjNmMTQwYi02MmMyLTQ3NzAtODY4Yy1kMjg5NDg0NWRlYWIiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFQdTRkNUJnWDljTk55WnM5dE10c1kzIiwidG9vbF9uYW1lIjoibGF1bmNoLXByb2Nlc3MiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcImN1cmwgLVggUE9TVCBodHRwOi8vbG9jYWxob3N0OjgwODAvYXBpL2NhbGxzL2N1c3RvbWVyLWluaXRpYXRlIFxcXFxcXG4gIC1IIFxcXCJDb250ZW50LVR5cGU6IGFwcGxpY2F0aW9uL2pzb25cXFwiIFxcXFxcXG4gIC1kICd7XFxuICAgIFxcXCJjdXN0b21lcl9pZFxcXCI6IFxcXCJ0ZXN0X2N1c3RvbWVyXzEyM1xcXCIsXFxuICAgIFxcXCJwdXJwb3NlXFxcIjogXFxcImJpbGxpbmdfaW5xdWlyeVxcXCIsXFxuICAgIFxcXCJjdXN0b21lcl9pbmZvXFxcIjoge1xcbiAgICAgIFxcXCJuYW1lXFxcIjogXFxcIlRlc3QgQ3VzdG9tZXJcXFwiLFxcbiAgICAgIFxcXCJlbWFpbFxcXCI6IFxcXCJ0ZXN0QGV4YW1wbGUuY29tXFxcIixcXG4gICAgICBcXFwicGhvbmVcXFwiOiBcXFwiKzEyMzQ1Njc4OTBcXFwiXFxuICAgIH1cXG4gIH0nXCIsIFwid2FpdFwiOiB0cnVlLCBcIm1heF93YWl0X3NlY29uZHNcIjogMTB9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiR3JlYXQhIFRoZSBzZXJ2ZXIgaXMgbm93IHJ1bm5pbmcuIExldCBtZSB0ZXN0IHRoZSB0b2tlbiBnZW5lcmF0aW9uIGJ5IG1ha2luZyBhIHJlcXVlc3QgdG8gc2VlIHdoYXQncyBoYXBwZW5pbmcuIEZpcnN0LCBsZXQgbWUgY3JlYXRlIGEgY3VzdG9tZXIgY2FsbCBhbmQgc2VlIHRoZSB0b2tlbiBnZW5lcmF0aW9uIHByb2Nlc3M6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJIZXJlIGlzIHRoZSBvdXRwdXQgZnJvbSB0ZXJtaW5hbCAzMCAoc3RhdHVzOiBzdGlsbCBydW5uaW5nKTpcbjxvdXRwdXQ+MjAyNS8wNS8yNiAxMzo1NDowMCDwn5qAIFN0YXJ0aW5nIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfk4sgU2VydmVyOiAwLjAuMC4wOjgwODBcclxuMjAyNS8wNS8yNiAxMzo1NDowMCDwn46vIEFnb3JhIEFwcCBJRDogMDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcclxuMjAyNS8wNS8yNiAxMzo1NDowMCDwn46vIEFnb3JhIHNlcnZpY2UgaW5pdGlhbGl6ZWQgd2l0aCBBcHAgSUQ6IDAxNDUyMzQ4MGQ5NTQ0YmU4MDBhMDE0ZjM1YmRjZmFjXHJcbjIwMjUvMDUvMjYgMTM6NTQ6MDAg8J+bo++4jyBSb3V0ZXMgY29uZmlndXJlZCBzdWNjZXNzZnVsbHlcclxuMjAyNS8wNS8yNiAxMzo1NDowMCDinIUgU2VydmVyIHN0YXJ0aW5nIG9uIDAuMC4wLjA6ODA4MFxyXG48L291dHB1dD5cbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVZkWHNqOWh4ZGJucTdVNDNuWmJFZFIifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMwLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA2OjU0OjIxLjkxNFoiLCJyZXNwb25zZV90ZXh0IjoiR3JlYXQhIFRoZSBzZXJ2ZXIgaXMgbm93IHJ1bm5pbmcuIExldCBtZSB0ZXN0IHRoZSB0b2tlbiBnZW5lcmF0aW9uIGJ5IG1ha2luZyBhIHJlcXVlc3QgdG8gc2VlIHdoYXQncyBoYXBwZW5pbmcuIEZpcnN0LCBsZXQgbWUgY3JlYXRlIGEgY3VzdG9tZXIgY2FsbCBhbmQgc2VlIHRoZSB0b2tlbiBnZW5lcmF0aW9uIHByb2Nlc3M6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMWU3ZjRhOGMtZWZmYi00ZDA1LWE1N2ItY2QwNGVmOGU3Zjg0IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxOEdMaFBzR3JMNE1KTVVLQmhQNDltQSIsInRvb2xfbmFtZSI6InJlYWQtcHJvY2VzcyIsImlucHV0X2pzb24iOiJ7XCJ0ZXJtaW5hbF9pZFwiOiAzMCwgXCJ3YWl0XCI6IGZhbHNlLCBcIm1heF93YWl0X3NlY29uZHNcIjogMn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiJOb3cgbGV0IG1lIGNoZWNrIHRoZSBzZXJ2ZXIgbG9ncyB0byBzZWUgd2hhdCBoYXBwZW5lZCBkdXJpbmcgdG9rZW4gZ2VuZXJhdGlvbjpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkhlcmUgYXJlIHRoZSByZXN1bHRzIGZyb20gZXhlY3V0aW5nIHRoZSBjb21tYW5kLlxuPHJldHVybi1jb2RlPlxuMFxuPC9yZXR1cm4tY29kZT5cbjxzdGRvdXQ+XG57XCJjYWxsX2lkXCI6XCI4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcIixcImNoYW5uZWxfbmFtZVwiOlwiY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXCIsXCJ0b2tlblwiOlwiMDA3ZUp4U1lMaGdYOGloS0hQKzE4L2daUXM1eFlvWlc5bzFqNHRZL0poeXBDTXJUZTdGV3k0RkJnTkRFMU1qWXhNTGd4UkxVeE9UcEZRTEE0TkVBME9UTkdQVHBKVGt0TVJrV3dHVGpMMkpwaG52TGFJWUdCa1lHVmdZR0JsQWZDWXd5UXdtV2NDa0FVTnlhWEZKZm01cVVYeGlacnlGaFdWS1NscXlrYTVscXJHaHJvbFpzcm11cGJsRm9tNktrVW1TcVdseVVvcUZaUklUZzZFNUlBQUEvLzgzV1RIU1wiLFwic3RhdHVzXCI6XCJjb25uZWN0ZWRcIixcIm1lc3NhZ2VcIjpcIkN1c3RvbWVyIGNhbGwgaW5pdGlhdGVkIHN1Y2Nlc3NmdWxseS4gQUkgaXMgcmVhZHkgdG8gYXNzaXN0LlwiLFwiYWlfaW5mb1wiOntcIm5hbWVcIjpcIkFJIFN1cHBvcnQgQXNzaXN0YW50XCIsXCJwZXJzb25hbGl0eVwiOlwiaGVscGZ1bFwiLFwibGFuZ3VhZ2VcIjpcImVuXCIsXCJjYXBhYmlsaXRpZXNcIjpbXCJiaWxsaW5nX3N1cHBvcnRcIixcInRlY2huaWNhbF9zdXBwb3J0XCIsXCJhY2NvdW50X21hbmFnZW1lbnRcIixcImdlbmVyYWxfaW5xdWlyaWVzXCIsXCJwYXNzd29yZF9yZXNldFwiLFwic3Vic2NyaXB0aW9uX21hbmFnZW1lbnRcIl19fVxuPC9zdGRvdXQ+XG48c3RkZXJyPlxuXG48L3N0ZGVycj4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFQdTRkNUJnWDljTk55WnM5dE10c1kzIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIiwiZm9sZGVyUm9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozMSwiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNjo1NDo1My44ODNaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgY2hlY2sgdGhlIHNlcnZlciBsb2dzIHRvIHNlZSB3aGF0IGhhcHBlbmVkIGR1cmluZyB0b2tlbiBnZW5lcmF0aW9uOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJjYW5jZWxsZWQiLCJyZXF1ZXN0X2lkIjoiNGU1MWY2M2QtNzc0My00OGY3LWI4NTQtYjMyMmU2YzEwOTE2IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOltdLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkhlcmUgaXMgdGhlIG91dHB1dCBmcm9tIHRlcm1pbmFsIDMwIChzdGF0dXM6IHN0aWxsIHJ1bm5pbmcpOlxuPG91dHB1dD4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfmoAgU3RhcnRpbmcgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXHJcbjIwMjUvMDUvMjYgMTM6NTQ6MDAg8J+TiyBTZXJ2ZXI6IDAuMC4wLjA6ODA4MFxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfjq8gQWdvcmEgQXBwIElEOiAwMTQ1MjM0ODBkOTU0NGJlODAwYTAxNGYzNWJkY2ZhY1xyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfjq8gQWdvcmEgc2VydmljZSBpbml0aWFsaXplZCB3aXRoIEFwcCBJRDogMDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcclxuMjAyNS8wNS8yNiAxMzo1NDowMCDwn5uj77iPIFJvdXRlcyBjb25maWd1cmVkIHN1Y2Nlc3NmdWxseVxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIOKchSBTZXJ2ZXIgc3RhcnRpbmcgb24gMC4wLjAuMDo4MDgwXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg8J+TniBJbml0aWF0aW5nIG5ldyBjdXN0b21lci10by1BSSBjYWxsIGZvciBjdXN0b21lcjogdGVzdF9jdXN0b21lcl8xMjMsIHB1cnBvc2U6IGJpbGxpbmdfaW5xdWlyeVxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIOKchSBDdXN0b21lci10by1BSSBjYWxsIGluaXRpYXRlZCBzdWNjZXNzZnVsbHk6IDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIPCflJEgR2VuZXJhdGluZyBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliLCB1c2VyOiB0ZXN0X2N1c3RvbWVyXzEyMywgcm9sZTogMVxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIOKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg4pyFIFRva2VuIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgZm9yIGNoYW5uZWw6IGN1c3RvbWVyX2FpXzg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIPCflJcgW01PQ0tdIEFJIGpvaW5pbmcgQWdvcmEgY2hhbm5lbDogY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliIGFzIHVzZXI6IGFpX2FnZW50XHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgam9pbmVkIGNoYW5uZWw6IGN1c3RvbWVyX2FpXzg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIPCfjpnvuI8gW01PQ0tdIFNldHRpbmcgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgY2hhbm5lbDogYWlfc3VwcG9ydF84ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcclxuMjAyNS8wNS8yNiAxMzo1NDo1MyDinIUgW01PQ0tdIEF1ZGlvIHByb2Nlc3Npbmcgc2V0dXAgY29tcGxldGUgZm9yIGNoYW5uZWw6IGFpX3N1cHBvcnRfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg8J+OrSBbTU9DS10gU3RhcnRpbmcgYXVkaW8gc2ltdWxhdGlvbiBmb3IgY2FsbDogODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjo6MSAtIFsyNi9NYXkvMjAyNToxMzo1NDo1MyArMDcwMF0gXCJQT1NUIC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGUgSFRUUC8xLjEgMjAxIDUwMi44ODA2MjVtcyBcImN1cmwvOC43LjFcIiBcIlxyXG4yMDI1LzA1LzI2IDEzOjU0OjU4IPCfjqQgW01PQ0tdIFNpbXVsYXRpbmcgaW5jb21pbmcgYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliIChtZXNzYWdlIDEpXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTgg8J+OpCBQcm9jZXNzaW5nIGF1ZGlvIG1lc3NhZ2UgZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliICgxNjAwIGJ5dGVzKVxyXG4yMDI1LzA1LzI2IDEzOjU0OjU4IPCfjpnvuI8gW01PQ0tdIFZBRDogRW5lcmd5PTEwNS41MCwgVGhyZXNob2xkPTcwMC4wMCwgSXNTcGVlY2g9ZmFsc2VcclxuMjAyNS8wNS8yNiAxMzo1NDo1OCDwn5SHIE5vIHNwZWVjaCBkZXRlY3RlZCBpbiBhdWRpbyBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcclxuMjAyNS8wNS8yNiAxMzo1NTowNSDwn46kIFtNT0NLXSBTaW11bGF0aW5nIGluY29taW5nIGF1ZGlvIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAobWVzc2FnZSAyKVxyXG4yMDI1LzA1LzI2IDEzOjU1OjA1IPCfjqQgUHJvY2Vzc2luZyBhdWRpbyBtZXNzYWdlIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAoMTYwMCBieXRlcylcclxuMjAyNS8wNS8yNiAxMzo1NTowNSDwn46Z77iPIFtNT0NLXSBWQUQ6IEVuZXJneT0xMDUuMDIsIFRocmVzaG9sZD03MDAuMDAsIElzU3BlZWNoPWZhbHNlXHJcbjIwMjUvMDUvMjYgMTM6NTU6MDUg8J+UhyBObyBzcGVlY2ggZGV0ZWN0ZWQgaW4gYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTU6MTQg8J+OpCBbTU9DS10gU2ltdWxhdGluZyBpbmNvbWluZyBhdWRpbyBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWIgKG1lc3NhZ2UgMylcclxuMjAyNS8wNS8yNiAxMzo1NToxNCDwn46kIFByb2Nlc3NpbmcgYXVkaW8gbWVzc2FnZSBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWIgKDE2MDAgYnl0ZXMpXHJcbjIwMjUvMDUvMjYgMTM6NTU6MTQg8J+Ome+4jyBbTU9DS10gVkFEOiBFbmVyZ3k9MTA1LjE4LCBUaHJlc2hvbGQ9NzAwLjAwLCBJc1NwZWVjaD1mYWxzZVxyXG4yMDI1LzA1LzI2IDEzOjU1OjE0IPCflIcgTm8gc3BlZWNoIGRldGVjdGVkIGluIGF1ZGlvIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU1OjI1IPCfjqQgW01PQ0tdIFNpbXVsYXRpbmcgaW5jb21pbmcgYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliIChtZXNzYWdlIDQpXHJcbjIwMjUvMDUvMjYgMTM6NTU6MjUg8J+OpCBQcm9jZXNzaW5nIGF1ZGlvIG1lc3NhZ2UgZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliICgxNjAwIGJ5dGVzKVxyXG4yMDI1LzA1LzI2IDEzOjU1OjI1IPCfjpnvuI8gW01PQ0tdIFZBRDogRW5lcmd5PTEwNS45OCwgVGhyZXNob2xkPTcwMC4wMCwgSXNTcGVlY2g9ZmFsc2VcclxuMjAyNS8wNS8yNiAxMzo1NToyNSDwn5SHIE5vIHNwZWVjaCBkZXRlY3RlZCBpbiBhdWRpbyBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcclxuMjAyNS8wNS8yNiAxMzo1NTozOCDwn46kIFtNT0NLXSBTaW11bGF0aW5nIGluY29taW5nIGF1ZGlvIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAobWVzc2FnZSA1KVxyXG4yMDI1LzA1LzI2IDEzOjU1OjM4IPCfjqQgUHJvY2Vzc2luZyBhdWRpbyBtZXNzYWdlIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAoMTYwMCBieXRlcylcclxuMjAyNS8wNS8yNiAxMzo1NTozOCDwn46Z77iPIFtNT0NLXSBWQUQ6IEVuZXJneT0xMDUuODIsIFRocmVzaG9sZD03MDAuMDAsIElzU3BlZWNoPWZhbHNlXHJcbjIwMjUvMDUvMjYgMTM6NTU6Mzgg8J+UhyBObyBzcGVlY2ggZGV0ZWN0ZWQgaW4gYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTU6Mzgg8J+OrSBbTU9DS10gQXVkaW8gc2ltdWxhdGlvbiBjb21wbGV0ZWQgZm9yIGNhbGw6IDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG48L291dHB1dD5cbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMThHTGhQc0dyTDRNSk1VS0JoUDQ5bUEifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMxLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA2OjU1OjQ2LjA2MloiLCJyZXNwb25zZV90ZXh0IjoiUGVyZmVjdCEgSSBjYW4gc2VlIHRoYXQgdGhlIHNlcnZlciBpcyBnZW5lcmF0aW5nIGEgKipyZWFsIEFnb3JhIHRva2VuKiogKG5vdCBhIG1vY2sgdG9rZW4pIGJlY2F1c2UgYm90aCBgQUdPUkFfQVBQX0lEYCBhbmQgYEFHT1JBX0FQUF9DRVJUSUZJQ0FURWAgYXJlIHByb3ZpZGVkIGluIHRoZSBgLmVudmAgZmlsZS4gVGhlIHRva2VuIHRoYXQgd2FzIGdlbmVyYXRlZCBpczpcblxuYGBgXG4wMDdlSnhTWUxoZ1g4aWhLSFArMTgvZ1pRczV4WW9aVzlvMWo0dFkvSmh5cENNclRlN0ZXeTRGQmdOREUxTWpZeE1MZ3hSTFV4T1RwRlFMQTRORUEwT1ROR1BUcEpUa3RNUmtXd0dUakwySnBobnZMYUlZR0JrWUdWZ1lHQmxBZkNZd3lRd21XY0NrQVVOeWFYRkpmbTVxVVh4aVpyeUZoV1ZLU2xxeWthNWxxckdocm9sWnNybXVwYmxGb202S2tVbVNxV2x5VW9xRlpSSVRnNkU1SUFBQS8vODNXVEhTXG5gYGBcblxuTm93IEkgbmVlZCB0byBhbmFseXplIHRoZSBwb3RlbnRpYWwgaXNzdWVzIHdpdGggdGhlIHRva2VuLiBMZXQgbWUgZXhhbWluZSB0aGUgdG9rZW4gZ2VuZXJhdGlvbiBsb2dpYyBtb3JlIGNsb3NlbHkgYW5kIGlkZW50aWZ5IHBvdGVudGlhbCBwcm9ibGVtczpcblxuIyMgQW5hbHlzaXMgb2YgdGhlIElzc3VlXG5cbkJhc2VkIG9uIG15IGV4YW1pbmF0aW9uIG9mIHRoZSBjb2RlIGFuZCB0aGUgZXJyb3IgeW91J3JlIHNlZWluZyAoYEVycm9yQ29kZVR5cGUuZXJySW52YWxpZFRva2VuYCksIGhlcmUgYXJlIHRoZSBtb3N0IGxpa2VseSBjYXVzZXM6XG5cbiMjIyAxLiAqKlVzZXIgSUQgQ29udmVyc2lvbiBJc3N1ZSoqXG5Mb29raW5nIGF0IHRoZSBgZ2VuZXJhdGVSZWFsVG9rZW5gIGZ1bmN0aW9uIGluIGBhZ29yYV9zZXJ2aWNlLmdvYCwgdGhlcmUncyBhIHBvdGVudGlhbCBpc3N1ZSB3aXRoIGhvdyB0aGUgYHVzZXJJRGAgaXMgYmVpbmcgY29udmVydGVkIHRvIGEgbnVtZXJpYyBVSUQ6XG5cbmBgYGBnbyBwYXRoPXNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ28gbW9kZT1FWENFUlBUXG4vLyBDb252ZXJ0IHVzZXJJRCB0byB1aW50MzIgKEFnb3JhIGV4cGVjdHMgbnVtZXJpYyBVSUQpXG52YXIgdWlkIHVpbnQzMlxuaWYgdXNlcklEICE9IFwiXCIge1xuICAgIGlmIHBhcnNlZFVJRCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VVaW50KHVzZXJJRCwgMTAsIDMyKTsgZXJyID09IG5pbCB7XG4gICAgICAgIHVpZCA9IHVpbnQzMihwYXJzZWRVSUQpXG4gICAgfSBlbHNlIHtcbiAgICAgICAgLy8gSWYgdXNlcklEIGlzIG5vdCBudW1lcmljLCBnZW5lcmF0ZSBhIGhhc2gtYmFzZWQgVUlEXG4gICAgICAgIHVpZCA9IHVpbnQzMihsZW4odXNlcklEKSkgLy8gU2ltcGxlIGZhbGxiYWNrLCBpbiBwcm9kdWN0aW9uIHVzZSBhIHByb3BlciBoYXNoXG4gICAgfVxufSBlbHNlIHtcbiAgICB1aWQgPSAwIC8vIExldCBBZ29yYSBhc3NpZ24gYSBVSURcbn1cbmBgYGBcblxuVGhlIGlzc3VlIGlzIHRoYXQgd2hlbiB0aGUgYHVzZXJJRGAgaXMgbm90IG51bWVyaWMgKGxpa2UgXCJ0ZXN0X2N1c3RvbWVyXzEyM1wiKSwgaXQncyB1c2luZyBgdWludDMyKGxlbih1c2VySUQpKWAgd2hpY2ggaXMgYSB2ZXJ5IHdlYWsgaGFzaCBmdW5jdGlvbiBhbmQgY291bGQgY2F1c2UgY29sbGlzaW9ucy5cblxuIyMjIDIuICoqQ2hhbm5lbCBOYW1lIE1pc21hdGNoKipcblRoZXJlIG1pZ2h0IGJlIGEgbWlzbWF0Y2ggYmV0d2VlbiB0aGUgY2hhbm5lbCBuYW1lIHVzZWQgdG8gZ2VuZXJhdGUgdGhlIHRva2VuIGFuZCB0aGUgY2hhbm5lbCBuYW1lIHRoZSBjbGllbnQgaXMgdHJ5aW5nIHRvIGpvaW4uXG5cbiMjIyAzLiAqKkFwcCBJRC9DZXJ0aWZpY2F0ZSBWYWxpZGF0aW9uKipcblRoZSBBZ29yYSBjcmVkZW50aWFscyBtaWdodCBiZSBpbnZhbGlkIG9yIHRoZSBBcHAgQ2VydGlmaWNhdGUgbWlnaHQgYmUgaW5jb3JyZWN0LlxuXG4jIyBQbGFuIHRvIEZpeCB0aGUiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InJlcXVlc3RfaWQiOiI3OTNjNTRjZC01NDFiLTQzMzEtYjBjNy0zYzgzY2UwNGZhZDAiLCJzdGF0dXMiOiJjYW5jZWxsZWQiLCJjaGF0SXRlbVR5cGUiOiJhZ2VudGljLXR1cm4tZGVsaW1pdGVyIn0seyJyZXF1ZXN0X2lkIjoiMGE1ZjVjZGUtN2E3Ny00NzA1LWFlNjMtMWM3ZTk1ZjkxMWM5Iiwic3RhdHVzIjoiY2FuY2VsbGVkIiwiY2hhdEl0ZW1UeXBlIjoiYWdlbnRpYy10dXJuLWRlbGltaXRlciJ9LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI5ZWU0MjFjZC00MDViLTRlYTktOGUzNC05NWM2MGE1ZGQ3ZmUiLCJyZXF1ZXN0X21lc3NhZ2UiOiJ1cGRhdGUgdGhlIHVzZXJJRCBpbiBib3RoIGJhY2tlbmQgYW5kIGFwcCB0byBpbnQiLCJyaWNoX3RleHRfanNvbl9yZXByIjp7InR5cGUiOiJkb2MiLCJjb250ZW50IjpbeyJ0eXBlIjoicGFyYWdyYXBoIiwiY29udGVudCI6W3sidHlwZSI6InRleHQiLCJ0ZXh0IjoidXBkYXRlIHRoZSB1c2VySUQgaW4gYm90aCBiYWNrZW5kIGFuZCBhcHAgdG8gaW50In1dfV19LCJtZW50aW9uZWRfaXRlbXMiOltdLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFMM05tS21Ib2E4dVkxS1FyYTkzU3FEIiwidG9vbF9uYW1lIjoiY29kZWJhc2UtcmV0cmlldmFsIiwiaW5wdXRfanNvbiI6IntcImluZm9ybWF0aW9uX3JlcXVlc3RcIjogXCJBbGwgcGxhY2VzIHdoZXJlIHVzZXJJRCBpcyB1c2VkIGluIHRoZSBiYWNrZW5kLCBpbmNsdWRpbmcgQVBJIGhhbmRsZXJzLCBtb2RlbHMsIGFuZCBBZ29yYSBzZXJ2aWNlLCB0byB1bmRlcnN0YW5kIHRoZSBjdXJyZW50IHN0cmluZy1iYXNlZCBpbXBsZW1lbnRhdGlvblwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IkkgdW5kZXJzdGFuZCB5b3Ugd2FudCB0byB1cGRhdGUgdGhlIHVzZXJJRCBmcm9tIHN0cmluZyB0byBpbnQgaW4gYm90aCB0aGUgYmFja2VuZCBhbmQgdGhlIEZsdXR0ZXIgYXBwLiBUaGlzIGlzIGxpa2VseSB0aGUgY2F1c2Ugb2YgdGhlIGludmFsaWQgdG9rZW4gaXNzdWUsIGFzIEFnb3JhIGV4cGVjdHMgbnVtZXJpYyBVSURzIGFuZCB0aGUgY3VycmVudCBpbXBsZW1lbnRhdGlvbiBoYXMgc29tZSBpbmNvbnNpc3RlbmNpZXMgaW4gaGFuZGxpbmcgc3RyaW5nIHZzIG51bWVyaWMgdXNlciBJRHMuXG5cbkxldCBtZSBmaXJzdCBleGFtaW5lIHRoZSBjdXJyZW50IGltcGxlbWVudGF0aW9uIGFuZCB0aGVuIGNyZWF0ZSBhIHBsYW4gdG8gdXBkYXRlIGJvdGggYmFja2VuZCBhbmQgZnJvbnRlbmQuXG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjowLCJ0eXBlIjowLCJ0ZXh0X25vZGUiOnsiY29udGVudCI6InVwZGF0ZSB0aGUgdXNlcklEIGluIGJvdGggYmFja2VuZCBhbmQgYXBwIHRvIGludCJ9fSx7ImlkIjoxLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlfX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA3OjE2OjUxLjE0N1oiLCJyZXNwb25zZV90ZXh0IjoiSSB1bmRlcnN0YW5kIHlvdSB3YW50IHRvIHVwZGF0ZSB0aGUgdXNlcklEIGZyb20gc3RyaW5nIHRvIGludCBpbiBib3RoIHRoZSBiYWNrZW5kIGFuZCB0aGUgRmx1dHRlciBhcHAuIFRoaXMgaXMgbGlrZWx5IHRoZSBjYXVzZSBvZiB0aGUgaW52YWxpZCB0b2tlbiBpc3N1ZSwgYXMgQWdvcmEgZXhwZWN0cyBudW1lcmljIFVJRHMgYW5kIHRoZSBjdXJyZW50IGltcGxlbWVudGF0aW9uIGhhcyBzb21lIGluY29uc2lzdGVuY2llcyBpbiBoYW5kbGluZyBzdHJpbmcgdnMgbnVtZXJpYyB1c2VyIElEcy5cblxuTGV0IG1lIGZpcnN0IGV4YW1pbmUgdGhlIGN1cnJlbnQgaW1wbGVtZW50YXRpb24gYW5kIHRoZW4gY3JlYXRlIGEgcGxhbiB0byB1cGRhdGUgYm90aCBiYWNrZW5kIGFuZCBmcm9udGVuZC5cbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI1ZTk2NmY5OC0yNzMxLTRjMTgtYTZkYy0xMDIyNjIwMWZhYjUiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFCSjJ4a2JSeXQzWlJIdkpBcXAzYzk5IiwidG9vbF9uYW1lIjoiY29kZWJhc2UtcmV0cmlldmFsIiwiaW5wdXRfanNvbiI6IntcImluZm9ybWF0aW9uX3JlcXVlc3RcIjogXCJGbHV0dGVyIG9yIERhcnQgZmlsZXMgcmVsYXRlZCB0byBBZ29yYSBpbnRlZ3JhdGlvbiwgdXNlciBJRCBoYW5kbGluZywgYW5kIEFQSSBjYWxscyB0byB0aGUgYmFja2VuZFwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6Ik5vdyBsZXQgbWUgY2hlY2sgaWYgdGhlcmUgYXJlIGFueSBGbHV0dGVyL0RhcnQgZmlsZXMgaW4gdGhlIGNvZGViYXNlIHRvIHVuZGVyc3RhbmQgdGhlIGZyb250ZW5kIGltcGxlbWVudGF0aW9uOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogc2VydmljZXMvYWdvcmFfc2VydmljZS5nb1xuICAgICAxXHRwYWNrYWdlIHNlcnZpY2VzXG4gICAgIDJcdFxuICAgICAzXHRpbXBvcnQgKFxuICAgICA0XHRcdFwiQmFja2VuZC9jb25maWdcIlxuICAgICA1XHRcdFwiZm10XCJcbiAgICAgNlx0XHRcImxvZ1wiXG4gICAgIDdcdFx0XCJzdHJjb252XCJcbiAgICAgOFx0XHRcInRpbWVcIlxuICAgICA5XHRcbiAgICAxMFx0XHRcImdpdGh1Yi5jb20vQWdvcmFJTy9Ub29scy9EeW5hbWljS2V5L0Fnb3JhRHluYW1pY0tleS9nby9zcmMvcnRjdG9rZW5idWlsZGVyMlwiXG4gICAgMTFcdClcbiAgICAxMlx0XG4gICAgMTNcdC8vIEFnb3JhU2VydmljZSBoYW5kbGVzIEFnb3JhIFJUQyBvcGVyYXRpb25zXG4gICAgMTRcdHR5cGUgQWdvcmFTZXJ2aWNlIHN0cnVjdCB7XG4gICAgMTVcdFx0Y29uZmlnICpjb25maWcuQ29uZmlnXG4gICAgMTZcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjb250YWluOlxuICAgIDE3XHRcdC8vIC0gQWdvcmEgc2VydmljZSBpbnN0YW5jZVxuICAgIDE4XHRcdC8vIC0gTWVkaWEgbm9kZSBmYWN0b3J5XG4gICAgMTlcdFx0Ly8gLSBBY3RpdmUgY29ubmVjdGlvbnMgbWFwXG4gICAgMjBcdFx0Ly8gLSBBdWRpby9WaWRlbyBvYnNlcnZlcnNcbiAgICAyMVx0fVxuICAgIDIyXHRcbiAgICAyM1x0Ly8gVG9rZW5SZXF1ZXN0IHJlcHJlc2VudHMgYSByZXF1ZXN0IGZvciBBZ29yYSB0b2tlblxuICAgIDI0XHR0eXBlIFRva2VuUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDI1XHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgIDI2XHRcdFVzZXJJRCAgICAgIHN0cmluZyBganNvbjpcInVzZXJfaWRcImBcbiAgICAyN1x0XHRSb2xlICAgICAgICBpbnQgICAgYGpzb246XCJyb2xlXCJgIC8vIDEgPSBwdWJsaXNoZXIsIDIgPSBzdWJzY3JpYmVyXG4gICAgMjhcdH1cbi4uLlxuICAgIDUwXHRcbiAgICA1MVx0Ly8gR2VuZXJhdGVUb2tlbiBnZW5lcmF0ZXMgYW4gQWdvcmEgUlRDIHRva2VuIGZvciBjaGFubmVsIGFjY2Vzc1xuICAgIDUyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZW5lcmF0ZVRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgKCpUb2tlblJlc3BvbnNlLCBlcnJvcikge1xuICAgIDUzXHRcdGxvZy5QcmludGYoXCLwn5SRIEdlbmVyYXRpbmcgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB1c2VyOiAlcywgcm9sZTogJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA1NFx0XG4gICAgNTVcdFx0Ly8gVG9rZW4gZXhwaXJlcyBpbiAyNCBob3Vyc1xuICAgIDU2XHRcdGV4cGlyYXRpb25UaW1lIDo9IHRpbWUuTm93KCkuQWRkKDI0ICogdGltZS5Ib3VyKS5Vbml4KClcbiAgICA1N1x0XG4gICAgNThcdFx0dmFyIHRva2VuIHN0cmluZ1xuICAgIDU5XHRcdHZhciBlcnIgZXJyb3JcbiAgICA2MFx0XG4gICAgNjFcdFx0aWYgYXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlICE9IFwiXCIge1xuICAgIDYyXHRcdFx0Ly8gR2VuZXJhdGUgcmVhbCB0b2tlbiB3aXRoIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA2M1x0XHRcdHRva2VuLCBlcnIgPSBhcy5nZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB1aW50MzIoZXhwaXJhdGlvblRpbWUpKVxuICAgIDY0XHRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjVcdFx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gZ2VuZXJhdGUgdG9rZW46ICV3XCIsIGVycilcbiAgICA2Nlx0XHRcdH1cbiAgICA2N1x0XHRcdGxvZy5QcmludGYoXCLinIUgR2VuZXJhdGVkIHJlYWwgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB0b2tlbiBpczogJXNcIiwgY2hhbm5lbE5hbWUsIHRva2VuKVxuICAgIDY4XHRcdH0gZWxzZSB7XG4gICAgNjlcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgIDcwXHRcdFx0dG9rZW4gPSBhcy5nZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDcxXHRcdFx0bG9nLlByaW50ZihcIuKaoO+4jyBbTU9DS10gVXNpbmcgbW9jayB0b2tlbiAobm8gYXBwIGNlcnRpZmljYXRlIHByb3ZpZGVkKVwiKVxuICAgIDcyXHRcdH1cbiAgICA3M1x0XG4gICAgNzRcdFx0cmVzcG9uc2UgOj0gJlRva2VuUmVzcG9uc2V7XG4gICAgNzVcdFx0XHRUb2tlbjogICAgIHRva2VuLFxuICAgIDc2XHRcdFx0RXhwaXJlc0F0OiBleHBpcmF0aW9uVGltZSxcbiAgICA3N1x0XHR9XG4gICAgNzhcdFxuICAgIDc5XHRcdGxvZy5QcmludGYoXCLinIUgVG9rZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgODBcdFx0cmV0dXJuIHJlc3BvbnNlLCBuaWxcbiAgICA4MVx0fVxuICAgIDgyXHRcbiAgICA4M1x0Ly8gZ2VuZXJhdGVSZWFsVG9rZW4gZ2VuZXJhdGVzIGEgcmVhbCBBZ29yYSB0b2tlbiB1c2luZyB0aGUgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDg0XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQsIGV4cGlyYXRpb24gdWludDMyKSAoc3RyaW5nLCBlcnJvcikge1xuICAgIDg1XHRcdC8vIENvbnZlcnQgdXNlcklEIHRvIHVpbnQzMiAoQWdvcmEgZXhwZWN0cyBudW1lcmljIFVJRClcbiAgICA4Nlx0XHR2YXIgdWlkIHVpbnQzMlxuICAgIDg3XHRcdGlmIHVzZXJJRCAhPSBcIlwiIHtcbiAgICA4OFx0XHRcdGlmIHBhcnNlZFVJRCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VVaW50KHVzZXJJRCwgMTAsIDMyKTsgZXJyID09IG5pbCB7XG4gICAgODlcdFx0XHRcdHVpZCA9IHVpbnQzMihwYXJzZWRVSUQpXG4gICAgOTBcdFx0XHR9IGVsc2Uge1xuICAgIDkxXHRcdFx0XHQvLyBJZiB1c2VySUQgaXMgbm90IG51bWVyaWMsIGdlbmVyYXRlIGEgaGFzaC1iYXNlZCBVSURcbiAgICA5Mlx0XHRcdFx0dWlkID0gdWludDMyKGxlbih1c2VySUQpKSAvLyBTaW1wbGUgZmFsbGJhY2ssIGluIHByb2R1Y3Rpb24gdXNlIGEgcHJvcGVyIGhhc2hcbiAgICA5M1x0XHRcdH1cbiAgICA5NFx0XHR9IGVsc2Uge1xuICAgIDk1XHRcdFx0dWlkID0gMCAvLyBMZXQgQWdvcmEgYXNzaWduIGEgVUlEXG4gICAgOTZcdFx0fVxuICAgIDk3XHRcbiAgICA5OFx0XHQvLyBDb252ZXJ0IHJvbGUgdG8gQWdvcmEgcm9sZSB0eXBlXG4gICAgOTlcdFx0dmFyIGFnb3JhUm9sZSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVcbiAgIDEwMFx0XHRpZiByb2xlID09IDEge1xuICAgMTAxXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlUHVibGlzaGVyXG4gICAxMDJcdFx0fSBlbHNlIHtcbiAgIDEwM1x0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVN1YnNjcmliZXJcbiAgIDEwNFx0XHR9XG4gICAxMDVcdFxuICAgMTA2XHRcdC8vIEdlbmVyYXRlIHRva2VuIHVzaW5nIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgIDEwN1x0XHR0b2tlbiwgZXJyIDo9IHJ0Y3Rva2VuYnVpbGRlcjIuQnVpbGRUb2tlbldpdGhVaWQoXG4gICAxMDhcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwSUQsXG4gICAxMDlcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUsXG4gICAxMTBcdFx0XHRjaGFubmVsTmFtZSxcbiAgIDExMVx0XHRcdHVpZCxcbiAgIDExMlx0XHRcdGFnb3JhUm9sZSxcbiAgIDExM1x0XHRcdGV4cGlyYXRpb24sXG4gICAxMTRcdFx0XHRleHBpcmF0aW9uLCAvLyBQcml2aWxlZ2UgZXhwaXJlZCB0aW1lc3RhbXAgKHNhbWUgYXMgdG9rZW4gZXhwaXJhdGlvbilcbiAgIDExNVx0XHQpXG4gICAxMTZcdFxuICAgMTE3XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTE4XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gYnVpbGQgQWdvcmEgdG9rZW46ICV3XCIsIGVycilcbiAgIDExOVx0XHR9XG4gICAxMjBcdFxuICAgMTIxXHRcdHJldHVybiB0b2tlbiwgbmlsXG4gICAxMjJcdH1cbiAgIDEyM1x0XG4gICAxMjRcdC8vIGdlbmVyYXRlTW9ja1Rva2VuIGdlbmVyYXRlcyBhIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAxMjVcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgc3RyaW5nIHtcbiAgIDEyNlx0XHR0aW1lc3RhbXAgOj0gdGltZS5Ob3coKS5Vbml4KClcbiAgIDEyN1x0XHRyZXR1cm4gZm10LlNwcmludGYoXCJtb2NrX3Rva2VuXyVzXyVzXyVkXyVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHRpbWVzdGFtcClcbiAgIDEyOFx0fVxuICAgMTI5XHRcbiAgIDEzMFx0Ly8gSm9pbkNoYW5uZWwgam9pbnMgYW4gQWdvcmEgY2hhbm5lbCBmb3IgQUkgcHJvY2Vzc2luZ1xuICAgMTMxXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBKb2luQ2hhbm5lbChjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZykgZXJyb3Ige1xuICAgMTMyXHRcdGxvZy5QcmludGYoXCLwn5SXIFtNT0NLXSBBSSBqb2luaW5nIEFnb3JhIGNoYW5uZWw6ICVzIGFzIHVzZXI6ICVzXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQpXG4gICAxMzNcdFxuICAgMTM0XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxMzVcdFx0Ly8gMS4gQ3JlYXRlIFJUQyBjb25uZWN0aW9uXG4gICAxMzZcdFx0Ly8gMi4gU2V0IHVwIGxvY2FsIHVzZXJcbiAgIDEzN1x0XHQvLyAzLiBDcmVhdGUgYXVkaW8gdHJhY2sgZm9yIEFJIHZvaWNlXG4gICAxMzhcdFx0Ly8gNC4gUmVnaXN0ZXIgYXVkaW8gZnJhbWUgb2JzZXJ2ZXJzIGZvciBwcm9jZXNzaW5nIGluY29taW5nIGF1ZGlvXG4gICAxMzlcdFx0Ly8gNS4gSm9pbiB0aGUgY2hhbm5lbFxuICAgMTQwXHRcdC8vIDYuIFN0YXJ0IGF1ZGlvIHByb2Nlc3NpbmcgcGlwZWxpbmVcbiAgIDE0MVx0XG4gICAxNDJcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvblxuICAgMTQzXHRcdHRpbWUuU2xlZXAoNTAwICogdGltZS5NaWxsaXNlY29uZCkgLy8gU2ltdWxhdGUgY29ubmVjdGlvbiB0aW1lXG4gICAxNDRcdFxuICAgMTQ1XHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEFJIHN1Y2Nlc3NmdWxseSBqb2luZWQgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNDZcdFx0cmV0dXJuIG5pbFxuICAgMTQ3XHR9XG4uLi5cblBhdGg6IGhhbmRsZXJzL2NhbGxfaGFuZGxlci5nb1xuLi4uXG4gICAgNjJcdFxuICAgIDYzXHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY2hhbm5lbFxuICAgIDY0XHRcdHRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgXCJhaV9hZ2VudFwiLCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgICA2NVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2Nlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDY3XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgQWdvcmEgdG9rZW5cIixcbiAgICA2OFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDY5XHRcdFx0fSlcbiAgICA3MFx0XHRcdHJldHVyblxuICAgIDcxXHRcdH1cbiAgICA3Mlx0XG4gICAgNzNcdFx0Ly8gSm9pbiB0aGUgY2hhbm5lbCBhcyBBSSBhZ2VudFxuICAgIDc0XHRcdGlmIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuSm9pbkNoYW5uZWwoY2FsbC5DaGFubmVsTmFtZSwgXCJhaV9hZ2VudFwiKTsgZXJyICE9IG5pbCB7XG4gICAgNzVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgICA3Nlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGpvaW4gQWdvcmEgY2hhbm5lbFwiLFxuICAgIDc3XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgNzhcdFx0XHR9KVxuICAgIDc5XHRcdFx0cmV0dXJuXG4gICAgODBcdFx0fVxuICAgIDgxXHRcbiAgICA4Mlx0XHQvLyBTZXQgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgdGhpcyBjYWxsXG4gICAgODNcdFx0YXVkaW9DYWxsYmFjayA6PSBmdW5jKGF1ZGlvRGF0YSBbXWJ5dGUpIGVycm9yIHtcbiAgICA4NFx0XHRcdHJldHVybiBjaC5jYWxsTWFuYWdlci5Qcm9jZXNzQXVkaW9NZXNzYWdlKGNhbGwuSUQsIGF1ZGlvRGF0YSlcbiAgICA4NVx0XHR9XG4uLi5cbiAgIDI0OFx0XG4gICAyNDlcdFx0Ly8gSm9pbiB0aGUgY2FsbFxuICAgMjUwXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Kb2luQ2FsbChyZXF1ZXN0LkNhbGxJRCwgcmVxdWVzdC5DdXN0b21lcklEKVxuICAgMjUxXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMjUyXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAyNTNcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBqb2luIGNhbGxcIixcbiAgIDI1NFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMjU1XHRcdFx0fSlcbiAgIDI1Nlx0XHRcdHJldHVyblxuICAgMjU3XHRcdH1cbiAgIDI1OFx0XG4gICAyNTlcdFx0Ly8gR2VuZXJhdGUgQWdvcmEgdG9rZW4gZm9yIHRoZSBjdXN0b21lclxuICAgMjYwXHRcdHRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgcmVxdWVzdC5DdXN0b21lcklELCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgIDI2MVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDI2Mlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMjYzXHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgQWdvcmEgdG9rZW5cIixcbiAgIDI2NFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMjY1XHRcdFx0fSlcbiAgIDI2Nlx0XHRcdHJldHVyblxuICAgMjY3XHRcdH1cbiAgIDI2OFx0XG4gICAyNjlcdFx0cmVzcG9uc2UgOj0gbW9kZWxzLkpvaW5DYWxsUmVzcG9uc2V7XG4gICAyNzBcdFx0XHRDaGFubmVsTmFtZTogY2FsbC5DaGFubmVsTmFtZSxcbiAgIDI3MVx0XHRcdFRva2VuOiAgICAgICB0b2tlbi5Ub2tlbixcbiAgIDI3Mlx0XHRcdENhbGxJbmZvOiAgICAqY2FsbCxcbiAgIDI3M1x0XHR9XG4gICAyNzRcdFxuICAgMjc1XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCByZXNwb25zZSlcbiAgIDI3Nlx0fVxuLi4uXG4gICA0MDBcdFxuICAgNDAxXHRcdC8vIEdldCBjYWxsIHRvIHZhbGlkYXRlIGl0IGV4aXN0c1xuICAgNDAyXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5HZXRDYWxsKGNhbGxJRClcbiAgIDQwM1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDQwNFx0XHRcdGMuSlNPTihodHRwLlN0YXR1c05vdEZvdW5kLCBnaW4uSHtcbiAgIDQwNVx0XHRcdFx0XCJlcnJvclwiOiAgIFwiQ2FsbCBub3QgZm91bmRcIixcbiAgIDQwNlx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgNDA3XHRcdFx0fSlcbiAgIDQwOFx0XHRcdHJldHVyblxuICAgNDA5XHRcdH1cbiAgIDQxMFx0XG4gICA0MTFcdFx0Ly8gR2VuZXJhdGUgdG9rZW5cbiAgIDQxMlx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIHJlcXVlc3QuVXNlcklELCByZXF1ZXN0LlJvbGUpXG4gICA0MTNcdFx0aWYgZXJyICE9IG5pbCB7XG4gICA0MTRcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDQxNVx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIHRva2VuXCIsXG4gICA0MTZcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDQxN1x0XHRcdH0pXG4gICA0MThcdFx0XHRyZXR1cm5cbiAgIDQxOVx0XHR9XG4gICA0MjBcdFxuICAgNDIxXHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBnaW4uSHtcbiAgIDQyMlx0XHRcdFwidG9rZW5cIjogICAgICAgIHRva2VuLlRva2VuLFxuICAgNDIzXHRcdFx0XCJleHBpcmVzX2F0XCI6ICAgdG9rZW4uRXhwaXJlc0F0LFxuICAgNDI0XHRcdFx0XCJjaGFubmVsX25hbWVcIjogY2FsbC5DaGFubmVsTmFtZSxcbiAgIDQyNVx0XHRcdFwidXNlcl9pZFwiOiAgICAgIHJlcXVlc3QuVXNlcklELFxuICAgNDI2XHRcdFx0XCJyb2xlXCI6ICAgICAgICAgcmVxdWVzdC5Sb2xlLFxuICAgNDI3XHRcdH0pXG4gICA0MjhcdH1cbi4uLlxuUGF0aDogY29uZmlnL2NvbmZpZy5nb1xuICAgICAxXHRwYWNrYWdlIGNvbmZpZ1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcImxvZ1wiXG4gICAgIDVcdFx0XCJvc1wiXG4gICAgIDZcdFx0XCJzdHJjb252XCJcbiAgICAgN1x0XG4gICAgIDhcdFx0XCJnaXRodWIuY29tL2pvaG8vZ29kb3RlbnZcIlxuICAgICA5XHQpXG4gICAgMTBcdFxuICAgIDExXHR0eXBlIENvbmZpZyBzdHJ1Y3Qge1xuICAgIDEyXHRcdFNlcnZlciBTZXJ2ZXJDb25maWdcbiAgICAxM1x0XHRBZ29yYSAgQWdvcmFDb25maWdcbiAgICAxNFx0XHRBSSAgICAgQUlDb25maWdcbiAgICAxNVx0fVxuICAgIDE2XHRcbiAgICAxN1x0dHlwZSBTZXJ2ZXJDb25maWcgc3RydWN0IHtcbiAgICAxOFx0XHRQb3J0IHN0cmluZ1xuICAgIDE5XHRcdEhvc3Qgc3RyaW5nXG4gICAgMjBcdH1cbiAgICAyMVx0XG4gICAgMjJcdHR5cGUgQWdvcmFDb25maWcgc3RydWN0IHtcbiAgICAyM1x0XHRBcHBJRCAgICAgICAgICBzdHJpbmdcbiAgICAyNFx0XHRBcHBDZXJ0aWZpY2F0ZSBzdHJpbmdcbiAgICAyNVx0XHRDaGFubmVsUHJlZml4ICBzdHJpbmdcbiAgICAyNlx0fVxuICAgIDI3XHRcbiAgICAyOFx0dHlwZSBBSUNvbmZpZyBzdHJ1Y3Qge1xuICAgIDI5XHRcdE9wZW5BSUFQSUtleSAgICBzdHJpbmdcbiAgICAzMFx0XHRPcGVuQUlNb2RlbCAgICAgc3RyaW5nXG4gICAgMzFcdFx0U1RUUHJvdmlkZXIgICAgIHN0cmluZ1xuICAgIDMyXHRcdFRUU1Byb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzM1x0XHRWb2ljZUlEICAgICAgICAgc3RyaW5nXG4gICAgMzRcdFx0RW5hYmxlVkFEICAgICAgIGJvb2xcbiAgICAzNVx0XHRWQURTZW5zaXRpdml0eSAgZmxvYXQ2NFxuICAgIDM2XHR9XG4gICAgMzdcdFxuICAgIDM4XHR2YXIgQXBwQ29uZmlnICpDb25maWdcbiAgICAzOVx0XG4gICAgNDBcdGZ1bmMgTG9hZENvbmZpZygpIHtcbiAgICA0MVx0XHQvLyBMb2FkIC5lbnYgZmlsZSBpZiBpdCBleGlzdHNcbiAgICA0Mlx0XHRpZiBlcnIgOj0gZ29kb3RlbnYuTG9hZCgpOyBlcnIgIT0gbmlsIHtcbiAgICA0M1x0XHRcdGxvZy5QcmludGxuKFwiTm8gLmVudiBmaWxlIGZvdW5kLCB1c2luZyBlbnZpcm9ubWVudCB2YXJpYWJsZXNcIilcbiAgICA0NFx0XHR9XG4gICAgNDVcdFxuICAgIDQ2XHRcdEFwcENvbmZpZyA9ICZDb25maWd7XG4gICAgNDdcdFx0XHRTZXJ2ZXI6IFNlcnZlckNvbmZpZ3tcbiAgICA0OFx0XHRcdFx0UG9ydDogZ2V0RW52KFwiU0VSVkVSX1BPUlRcIiwgXCI4MDgwXCIpLFxuICAgIDQ5XHRcdFx0XHRIb3N0OiBnZXRFbnYoXCJTRVJWRVJfSE9TVFwiLCBcIjAuMC4wLjBcIiksXG4gICAgNTBcdFx0XHR9LFxuICAgIDUxXHRcdFx0QWdvcmE6IEFnb3JhQ29uZmlne1xuICAgIDUyXHRcdFx0XHRBcHBJRDogICAgICAgICAgZ2V0RW52KFwiQUdPUkFfQVBQX0lEXCIsIFwiXCIpLFxuICAgIDUzXHRcdFx0XHRBcHBDZXJ0aWZpY2F0ZTogZ2V0RW52KFwiQUdPUkFfQVBQX0NFUlRJRklDQVRFXCIsIFwiXCIpLFxuICAgIDU0XHRcdFx0XHRDaGFubmVsUHJlZml4OiAgZ2V0RW52KFwiQUdPUkFfQ0hBTk5FTF9QUkVGSVhcIiwgXCJhaV9zdXBwb3J0X1wiKSxcbiAgICA1NVx0XHRcdH0sXG4gICAgNTZcdFx0XHRBSTogQUlDb25maWd7XG4gICAgNTdcdFx0XHRcdE9wZW5BSUFQSUtleTogICAgZ2V0RW52KFwiT1BFTkFJX0FQSV9LRVlcIiwgXCJcIiksXG4gICAgNThcdFx0XHRcdE9wZW5BSU1vZGVsOiAgICAgZ2V0RW52KFwiT1BFTkFJX01PREVMXCIsIFwiZ3B0LTRcIiksXG4gICAgNTlcdFx0XHRcdFNUVFByb3ZpZGVyOiAgICAgZ2V0RW52KFwiU1RUX1BST1ZJREVSXCIsIFwib3BlbmFpXCIpLFxuICAgIDYwXHRcdFx0XHRUVFNQcm92aWRlcjogICAgIGdldEVudihcIlRUU19QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MVx0XHRcdFx0Vm9pY2VJRDogICAgICAgICBnZXRFbnYoXCJUVFNfVk9JQ0VfSURcIiwgXCJhbGxveVwiKSxcbiAgICA2Mlx0XHRcdFx0RW5hYmxlVkFEOiAgICAgICBnZXRFbnZCb29sKFwiRU5BQkxFX1ZBRFwiLCB0cnVlKSxcbiAgICA2M1x0XHRcdFx0VkFEU2Vuc2l0aXZpdHk6ICBnZXRFbnZGbG9hdChcIlZBRF9TRU5TSVRJVklUWVwiLCAwLjcpLFxuICAgIDY0XHRcdFx0fSxcbiAgICA2NVx0XHR9XG4uLi5cblBhdGg6IC5lbnZcbiAgICAgMVx0IyBTZXJ2ZXIgQ29uZmlndXJhdGlvblxuICAgICAyXHRTRVJWRVJfUE9SVD04MDgwXG4gICAgIDNcdFNFUlZFUl9IT1NUPTAuMC4wLjBcbiAgICAgNFx0XG4gICAgIDVcdCMgQWdvcmEgQ29uZmlndXJhdGlvblxuICAgICA2XHRBR09SQV9BUFBfSUQ9MDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcbiAgICAgN1x0QUdPUkFfQVBQX0NFUlRJRklDQVRFPWU3ZjM3ZTY3ZDgyYjRhZmY4NDYzMTc3NGMxYjVkZTYxXG4gICAgIDhcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAgIDlcdFxuICAgIDEwXHQjIEFJIENvbmZpZ3VyYXRpb25cbiAgICAxMVx0T1BFTkFJX0FQSV9LRVk9ay1wcm9qLXp4M1RXWFItTEtjTVBUbTJMd3EwdzJnb3B1elZPZjR0dWlneFFINWlwaU95UkNLOXpGT09nRU1Cam1xZjBBN1FWZEMxSEc3MWtNVDNCbGJrRkoyTGNnaUJBdXBsNVU5YkI1ckVUeVBNUmxFVl8xR1ota1pzV3MxSmNUcEJZUy1saGthRURETUF2RXFmbnRZUUFVZExLZW5QMzZVQVxuICAgIDEyXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgICAxM1x0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgIDE0XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAgMTVcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgIDE2XHRFTkFCTEVfVkFEPXRydWVcbiAgICAxN1x0VkFEX1NFTlNJVElWSVRZPTAuN1xuLi4uXG5QYXRoOiBtb2RlbHMvY2FsbC5nb1xuLi4uXG4gICAgMjdcdFxuICAgIDI4XHQvLyBDYWxsIHJlcHJlc2VudHMgYSBjdXN0b21lciBzdXBwb3J0IGNhbGwgc2Vzc2lvblxuICAgIDI5XHR0eXBlIENhbGwgc3RydWN0IHtcbiAgICAzMFx0XHRJRCAgICAgICAgICBzdHJpbmcgICAgICAgYGpzb246XCJpZFwiYFxuICAgIDMxXHRcdEN1c3RvbWVySUQgIHN0cmluZyAgICAgICBganNvbjpcImN1c3RvbWVyX2lkXCJgXG4gICAgMzJcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nICAgICAgIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMzNcdFx0U3RhdHVzICAgICAgQ2FsbFN0YXR1cyAgIGBqc29uOlwic3RhdHVzXCJgXG4gICAgMzRcdFx0VHlwZSAgICAgICAgQ2FsbFR5cGUgICAgIGBqc29uOlwidHlwZVwiYFxuICAgIDM1XHRcdFN0YXJ0VGltZSAgIHRpbWUuVGltZSAgICBganNvbjpcInN0YXJ0X3RpbWVcImBcbiAgICAzNlx0XHRFbmRUaW1lICAgICAqdGltZS5UaW1lICAgYGpzb246XCJlbmRfdGltZSxvbWl0ZW1wdHlcImBcbiAgICAzN1x0XHREdXJhdGlvbiAgICBpbnQ2NCAgICAgICAgYGpzb246XCJkdXJhdGlvblwiYCAvLyBpbiBzZWNvbmRzXG4gICAgMzhcdFx0VHJhbnNjcmlwdCAgW11NZXNzYWdlICAgIGBqc29uOlwidHJhbnNjcmlwdFwiYFxuICAgIDM5XHRcdE1ldGFkYXRhICAgIENhbGxNZXRhZGF0YSBganNvbjpcIm1ldGFkYXRhXCJgXG4gICAgNDBcdH1cbiAgICA0MVx0XG4gICAgNDJcdC8vIE1lc3NhZ2UgcmVwcmVzZW50cyBhIHNpbmdsZSBtZXNzYWdlIGluIHRoZSBjb252ZXJzYXRpb25cbiAgICA0M1x0dHlwZSBNZXNzYWdlIHN0cnVjdCB7XG4gICAgNDRcdFx0SUQgICAgICAgIHN0cmluZyAgICBganNvbjpcImlkXCJgXG4gICAgNDVcdFx0U3BlYWtlciAgIHN0cmluZyAgICBganNvbjpcInNwZWFrZXJcImAgLy8gXCJhaVwiIG9yIFwiY3VzdG9tZXJcIlxuICAgIDQ2XHRcdENvbnRlbnQgICBzdHJpbmcgICAgYGpzb246XCJjb250ZW50XCJgXG4gICAgNDdcdFx0VGltZXN0YW1wIHRpbWUuVGltZSBganNvbjpcInRpbWVzdGFtcFwiYFxuICAgIDQ4XHRcdFR5cGUgICAgICBzdHJpbmcgICAgYGpzb246XCJ0eXBlXCJgIC8vIFwidGV4dFwiLCBcImF1ZGlvXCJcbiAgICA0OVx0fVxuICAgIDUwXHRcbiAgICA1MVx0Ly8gQ2FsbE1ldGFkYXRhIGNvbnRhaW5zIGFkZGl0aW9uYWwgaW5mb3JtYXRpb24gYWJvdXQgdGhlIGNhbGxcbiAgICA1Mlx0dHlwZSBDYWxsTWV0YWRhdGEgc3RydWN0IHtcbiAgICA1M1x0XHRQdXJwb3NlICAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwicHVycG9zZVwiYFxuICAgIDU0XHRcdEN1c3RvbWVySW5mbyAgQ3VzdG9tZXJJbmZvICAgICAgYGpzb246XCJjdXN0b21lcl9pbmZvXCJgXG4gICAgNTVcdFx0QUlQZXJzb25hbGl0eSBzdHJpbmcgICAgICAgICAgICBganNvbjpcImFpX3BlcnNvbmFsaXR5XCJgXG4gICAgNTZcdFx0TGFuZ3VhZ2UgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcImxhbmd1YWdlXCJgXG4gICAgNTdcdFx0VGFncyAgICAgICAgICBbXXN0cmluZyAgICAgICAgICBganNvbjpcInRhZ3NcImBcbiAgICA1OFx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgIDU5XHR9XG4uLi5cbiAgIDEzOFx0XG4gICAxMzlcdC8vIE5ld0NhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlXG4gICAxNDBcdGZ1bmMgTmV3Q2FsbChjdXN0b21lcklELCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTQxXHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNDJcdFx0Y2hhbm5lbE5hbWUgOj0gXCJhaV9zdXBwb3J0X1wiICsgY2FsbElEXG4gICAxNDNcdFxuICAgMTQ0XHRcdHJldHVybiAmQ2FsbHtcbiAgIDE0NVx0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNDZcdFx0XHRDdXN0b21lcklEOiAgY3VzdG9tZXJJRCxcbiAgIDE0N1x0XHRcdENoYW5uZWxOYW1lOiBjaGFubmVsTmFtZSxcbiAgIDE0OFx0XHRcdFN0YXR1czogICAgICBDYWxsU3RhdHVzUGVuZGluZyxcbiAgIDE0OVx0XHRcdFR5cGU6ICAgICAgICBDYWxsVHlwZUFJVG9DdXN0b21lciwgLy8gRGVmYXVsdCB0byBBSSBjYWxsaW5nIGN1c3RvbWVyXG4gICAxNTBcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE1MVx0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNTJcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTUzXHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTU0XHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxNTVcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTU2XHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxNTdcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTU4XHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE1OVx0XHRcdH0sXG4gICAxNjBcdFx0fVxuICAgMTYxXHR9XG4gICAxNjJcdFxuICAgMTYzXHQvLyBOZXdDdXN0b21lckNhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlIHdoZXJlIGN1c3RvbWVyIGNhbGxzIEFJXG4gICAxNjRcdGZ1bmMgTmV3Q3VzdG9tZXJDYWxsKGN1c3RvbWVySUQsIHB1cnBvc2Ugc3RyaW5nLCBjdXN0b21lckluZm8gQ3VzdG9tZXJJbmZvKSAqQ2FsbCB7XG4gICAxNjVcdFx0Y2FsbElEIDo9IHV1aWQuTmV3KCkuU3RyaW5nKClcbiAgIDE2Nlx0XHRjaGFubmVsTmFtZSA6PSBcImN1c3RvbWVyX2FpX1wiICsgY2FsbElEXG4gICAxNjdcdFxuICAgMTY4XHRcdHJldHVybiAmQ2FsbHtcbiAgIDE2OVx0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNzBcdFx0XHRDdXN0b21lcklEOiAgY3VzdG9tZXJJRCxcbiAgIDE3MVx0XHRcdENoYW5uZWxOYW1lOiBjaGFubmVsTmFtZSxcbiAgIDE3Mlx0XHRcdFN0YXR1czogICAgICBDYWxsU3RhdHVzUGVuZGluZyxcbiAgIDE3M1x0XHRcdFR5cGU6ICAgICAgICBDYWxsVHlwZUN1c3RvbWVyVG9BSSwgLy8gQ3VzdG9tZXIgY2FsbGluZyBBSVxuICAgMTc0XHRcdFx0U3RhcnRUaW1lOiAgIHRpbWUuTm93KCksXG4gICAxNzVcdFx0XHRUcmFuc2NyaXB0OiAgbWFrZShbXU1lc3NhZ2UsIDApLFxuICAgMTc2XHRcdFx0TWV0YWRhdGE6IENhbGxNZXRhZGF0YXtcbiAgIDE3N1x0XHRcdFx0UHVycG9zZTogICAgICAgcHVycG9zZSxcbiAgIDE3OFx0XHRcdFx0Q3VzdG9tZXJJbmZvOiAgY3VzdG9tZXJJbmZvLFxuICAgMTc5XHRcdFx0XHRBSVBlcnNvbmFsaXR5OiBcImhlbHBmdWxcIixcbiAgIDE4MFx0XHRcdFx0TGFuZ3VhZ2U6ICAgICAgXCJlblwiLFxuICAgMTgxXHRcdFx0XHRUYWdzOiAgICAgICAgICBtYWtlKFtdc3RyaW5nLCAwKSxcbiAgIDE4Mlx0XHRcdFx0Q3VzdG9tRmllbGRzOiAgbWFrZShtYXBbc3RyaW5nXXN0cmluZyksXG4gICAxODNcdFx0XHR9LFxuICAgMTg0XHRcdH1cbiAgIDE4NVx0fVxuLi4uXG5QYXRoOiBzZXJ2aWNlcy9jYWxsX21hbmFnZXIuZ29cbi4uLlxuICAgIDI5XHRcbiAgICAzMFx0Ly8gSW5pdGlhdGVDYWxsIHN0YXJ0cyBhIG5ldyBBSSBjdXN0b21lciBzdXBwb3J0IGNhbGwgKEFJIGNhbGxzIGN1c3RvbWVyKVxuICAgIDMxXHRmdW5jIChjbSAqQ2FsbE1hbmFnZXIpIEluaXRpYXRlQ2FsbChyZXF1ZXN0IG1vZGVscy5Jbml0aWF0ZUNhbGxSZXF1ZXN0KSAoKm1vZGVscy5DYWxsLCBlcnJvcikge1xuICAgIDMyXHRcdGNtLm11dGV4LkxvY2soKVxuICAgIDMzXHRcdGRlZmVyIGNtLm11dGV4LlVubG9jaygpXG4gICAgMzRcdFxuICAgIDM1XHRcdGxvZy5QcmludGYoXCLwn5qAIEluaXRpYXRpbmcgbmV3IEFJLXRvLWN1c3RvbWVyIGNhbGwgZm9yIGN1c3RvbWVyOiAlcywgcHVycG9zZTogJXNcIiwgcmVxdWVzdC5DdXN0b21lcklELCByZXF1ZXN0LlB1cnBvc2UpXG4gICAgMzZcdFxuICAgIDM3XHRcdC8vIENyZWF0ZSBuZXcgY2FsbFxuICAgIDM4XHRcdGNhbGwgOj0gbW9kZWxzLk5ld0NhbGwocmVxdWVzdC5DdXN0b21lcklELCByZXF1ZXN0LlB1cnBvc2UsIHJlcXVlc3QuQ3VzdG9tZXJJbmZvKVxuICAgIDM5XHRcbiAgICA0MFx0XHQvLyBTZXQgYWRkaXRpb25hbCBtZXRhZGF0YVxuICAgIDQxXHRcdGlmIHJlcXVlc3QuQUlQZXJzb25hbGl0eSAhPSBcIlwiIHtcbiAgICA0Mlx0XHRcdGNhbGwuTWV0YWRhdGEuQUlQZXJzb25hbGl0eSA9IHJlcXVlc3QuQUlQZXJzb25hbGl0eVxuICAgIDQzXHRcdH1cbiAgICA0NFx0XHRpZiByZXF1ZXN0Lkxhbmd1YWdlICE9IFwiXCIge1xuICAgIDQ1XHRcdFx0Y2FsbC5NZXRhZGF0YS5MYW5ndWFnZSA9IHJlcXVlc3QuTGFuZ3VhZ2VcbiAgICA0Nlx0XHR9XG4gICAgNDdcdFx0aWYgcmVxdWVzdC5DdXN0b21GaWVsZHMgIT0gbmlsIHtcbiAgICA0OFx0XHRcdGNhbGwuTWV0YWRhdGEuQ3VzdG9tRmllbGRzID0gcmVxdWVzdC5DdXN0b21GaWVsZHNcbiAgICA0OVx0XHR9XG4uLi5cbiAgICA2OVx0XG4gICAgNzBcdC8vIEluaXRpYXRlQ3VzdG9tZXJDYWxsIHN0YXJ0cyBhIG5ldyBjdXN0b21lci10by1BSSBjYWxsIChjdXN0b21lciBjYWxscyBBSSlcbiAgICA3MVx0ZnVuYyAoY20gKkNhbGxNYW5hZ2VyKSBJbml0aWF0ZUN1c3RvbWVyQ2FsbChyZXF1ZXN0IG1vZGVscy5Jbml0aWF0ZUN1c3RvbWVyQ2FsbFJlcXVlc3QpICgqbW9kZWxzLkNhbGwsIGVycm9yKSB7XG4gICAgNzJcdFx0Y20ubXV0ZXguTG9jaygpXG4gICAgNzNcdFx0ZGVmZXIgY20ubXV0ZXguVW5sb2NrKClcbiAgICA3NFx0XG4gICAgNzVcdFx0bG9nLlByaW50ZihcIvCfk54gSW5pdGlhdGluZyBuZXcgY3VzdG9tZXItdG8tQUkgY2FsbCBmb3IgY3VzdG9tZXI6ICVzLCBwdXJwb3NlOiAlc1wiLCByZXF1ZXN0LkN1c3RvbWVySUQsIHJlcXVlc3QuUHVycG9zZSlcbiAgICA3Nlx0XG4gICAgNzdcdFx0Ly8gQ3JlYXRlIG5ldyBjdXN0b21lciBjYWxsXG4gICAgNzhcdFx0Y2FsbCA6PSBtb2RlbHMuTmV3Q3VzdG9tZXJDYWxsKHJlcXVlc3QuQ3VzdG9tZXJJRCwgcmVxdWVzdC5QdXJwb3NlLCByZXF1ZXN0LkN1c3RvbWVySW5mbylcbiAgICA3OVx0XG4gICAgODBcdFx0Ly8gU2V0IGFkZGl0aW9uYWwgbWV0YWRhdGFcbiAgICA4MVx0XHRpZiByZXF1ZXN0LkFJUGVyc29uYWxpdHkgIT0gXCJcIiB7XG4gICAgODJcdFx0XHRjYWxsLk1ldGFkYXRhLkFJUGVyc29uYWxpdHkgPSByZXF1ZXN0LkFJUGVyc29uYWxpdHlcbiAgICA4M1x0XHR9XG4gICAgODRcdFx0aWYgcmVxdWVzdC5MYW5ndWFnZSAhPSBcIlwiIHtcbiAgICA4NVx0XHRcdGNhbGwuTWV0YWRhdGEuTGFuZ3VhZ2UgPSByZXF1ZXN0Lkxhbmd1YWdlXG4gICAgODZcdFx0fVxuICAgIDg3XHRcdGlmIHJlcXVlc3QuQ3VzdG9tRmllbGRzICE9IG5pbCB7XG4gICAgODhcdFx0XHRjYWxsLk1ldGFkYXRhLkN1c3RvbUZpZWxkcyA9IHJlcXVlc3QuQ3VzdG9tRmllbGRzXG4gICAgODlcdFx0fVxuLi4uXG4gICAxMDRcdFxuICAgMTA1XHQvLyBKb2luQ2FsbCBhbGxvd3MgYSBjdXN0b21lciB0byBqb2luIGFuIGV4aXN0aW5nIGNhbGxcbiAgIDEwNlx0ZnVuYyAoY20gKkNhbGxNYW5hZ2VyKSBKb2luQ2FsbChjYWxsSUQsIGN1c3RvbWVySUQgc3RyaW5nKSAoKm1vZGVscy5DYWxsLCBlcnJvcikge1xuICAgMTA3XHRcdGNtLm11dGV4LkxvY2soKVxuICAgMTA4XHRcdGRlZmVyIGNtLm11dGV4LlVubG9jaygpXG4gICAxMDlcdFxuICAgMTEwXHRcdGNhbGwsIGV4aXN0cyA6PSBjbS5jYWxsc1tjYWxsSURdXG4gICAxMTFcdFx0aWYgIWV4aXN0cyB7XG4gICAxMTJcdFx0XHRyZXR1cm4gbmlsLCBmbXQuRXJyb3JmKFwiY2FsbCBub3QgZm91bmQ6ICVzXCIsIGNhbGxJRClcbiAgIDExM1x0XHR9XG4gICAxMTRcdFxuICAgMTE1XHRcdGlmIGNhbGwuQ3VzdG9tZXJJRCAhPSBjdXN0b21lcklEIHtcbiAgIDExNlx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJ1bmF1dGhvcml6ZWQ6IGN1c3RvbWVyICVzIGNhbm5vdCBqb2luIGNhbGwgJXNcIiwgY3VzdG9tZXJJRCwgY2FsbElEKVxuICAgMTE3XHRcdH1cbiAgIDExOFx0XG4gICAxMTlcdFx0aWYgY2FsbC5TdGF0dXMgIT0gbW9kZWxzLkNhbGxTdGF0dXNSaW5naW5nICYmIGNhbGwuU3RhdHVzICE9IG1vZGVscy5DYWxsU3RhdHVzUGVuZGluZyB7XG4gICAxMjBcdFx0XHRyZXR1cm4gbmlsLCBmbXQuRXJyb3JmKFwiY2FsbCAlcyBpcyBub3QgYXZhaWxhYmxlIGZvciBqb2luaW5nIChzdGF0dXM6ICVzKVwiLCBjYWxsSUQsIGNhbGwuU3RhdHVzKVxuICAgMTIxXHRcdH1cbiAgIDEyMlx0XG4gICAxMjNcdFx0bG9nLlByaW50ZihcIvCfkaQgQ3VzdG9tZXIgJXMgam9pbmluZyBjYWxsICVzXCIsIGN1c3RvbWVySUQsIGNhbGxJRClcbiAgIDEyNFx0XG4gICAxMjVcdFx0Ly8gVXBkYXRlIGNhbGwgc3RhdHVzIHRvIGNvbm5lY3RlZFxuICAgMTI2XHRcdGNhbGwuVXBkYXRlU3RhdHVzKG1vZGVscy5DYWxsU3RhdHVzQ29ubmVjdGVkKVxuICAgMTI3XHRcbiAgIDEyOFx0XHQvLyBTZW5kIHN0YXR1cyB1cGRhdGUgbm90aWZpY2F0aW9uXG4gICAxMjlcdFx0aWYgZXJyIDo9IGNtLm5vdGlmaWNhdGlvblNlcnZpY2UuU2VuZENhbGxTdGF0dXNVcGRhdGUoY2FsbCwgbW9kZWxzLkNhbGxTdGF0dXNDb25uZWN0ZWQpOyBlcnIgIT0gbmlsIHtcbiAgIDEzMFx0XHRcdGxvZy5QcmludGYoXCJGYWlsZWQgdG8gc2VuZCBzdGF0dXMgdXBkYXRlIGZvciBjYWxsICVzOiAldlwiLCBjYWxsSUQsIGVycilcbiAgIDEzMVx0XHR9XG4gICAxMzJcdFxuICAgMTMzXHRcdC8vIEFkZCBpbml0aWFsIEFJIGdyZWV0aW5nXG4gICAxMzRcdFx0Z3JlZXRpbmcgOj0gY20uZ2VuZXJhdGVJbml0aWFsR3JlZXRpbmcoY2FsbClcbiAgIDEzNVx0XHRjYWxsLkFkZE1lc3NhZ2UoXCJhaVwiLCBncmVldGluZywgXCJ0ZXh0XCIpXG4gICAxMzZcdFxuICAgMTM3XHRcdGxvZy5QcmludGYoXCLwn46JIEN1c3RvbWVyIHN1Y2Nlc3NmdWxseSBqb2luZWQgY2FsbCAlc1wiLCBjYWxsSUQpXG4gICAxMzhcdFx0cmV0dXJuIGNhbGwsIG5pbFxuICAgMTM5XHR9XG4uLi5cblBhdGg6IG1haW4uZ29cbiAgICAgMVx0cGFja2FnZSBtYWluXG4gICAgIDJcdFxuICAgICAzXHRpbXBvcnQgKFxuICAgICA0XHRcdFwiQmFja2VuZC9jb25maWdcIlxuICAgICA1XHRcdFwiQmFja2VuZC9oYW5kbGVyc1wiXG4gICAgIDZcdFx0XCJCYWNrZW5kL21pZGRsZXdhcmVcIlxuICAgICA3XHRcdFwiQmFja2VuZC9zZXJ2aWNlc1wiXG4gICAgIDhcdFx0XCJmbXRcIlxuICAgICA5XHRcdFwibG9nXCJcbiAgICAxMFx0XHRcIm5ldC9odHRwXCJcbiAgICAxMVx0XHRcIm9zXCJcbiAgICAxMlx0XHRcIm9zL3NpZ25hbFwiXG4gICAgMTNcdFx0XCJzeXNjYWxsXCJcbiAgICAxNFx0XG4gICAgMTVcdFx0XCJnaXRodWIuY29tL2dpbi1nb25pYy9naW5cIlxuICAgIDE2XHQpXG4gICAgMTdcdFxuICAgIDE4XHRmdW5jIG1haW4oKSB7XG4gICAgMTlcdFx0Ly8gTG9hZCBjb25maWd1cmF0aW9uXG4gICAgMjBcdFx0Y29uZmlnLkxvYWRDb25maWcoKVxuICAgIDIxXHRcdGNmZyA6PSBjb25maWcuQXBwQ29uZmlnXG4gICAgMjJcdFxuICAgIDIzXHRcdGxvZy5QcmludGYoXCLwn5qAIFN0YXJ0aW5nIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFwiKVxuICAgIDI0XHRcdGxvZy5QcmludGYoXCLwn5OLIFNlcnZlcjogJXM6JXNcIiwgY2ZnLlNlcnZlci5Ib3N0LCBjZmcuU2VydmVyLlBvcnQpXG4gICAgMjVcdFx0bG9nLlByaW50ZihcIvCfjq8gQWdvcmEgQXBwIElEOiAlc1wiLCBjZmcuQWdvcmEuQXBwSUQpXG4gICAgMjZcdFxuICAgIDI3XHRcdC8vIEluaXRpYWxpemUgc2VydmljZXNcbiAgICAyOFx0XHRub3RpZmljYXRpb25TZXJ2aWNlIDo9IHNlcnZpY2VzLk5ld05vdGlmaWNhdGlvblNlcnZpY2UoKVxuICAgIDI5XHRcdGFnb3JhU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdBZ29yYVNlcnZpY2UoY2ZnKVxuICAgIDMwXHRcdGFpU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdBSVNlcnZpY2UoY2ZnKVxuICAgIDMxXHRcdGNhbGxNYW5hZ2VyIDo9IHNlcnZpY2VzLk5ld0NhbGxNYW5hZ2VyKG5vdGlmaWNhdGlvblNlcnZpY2UsIGFpU2VydmljZSwgYWdvcmFTZXJ2aWNlKVxuICAgIDMyXHRcbiAgICAzM1x0XHQvLyBJbml0aWFsaXplIGhhbmRsZXJzXG4gICAgMzRcdFx0Y2FsbEhhbmRsZXIgOj0gaGFuZGxlcnMuTmV3Q2FsbEhhbmRsZXIoY2FsbE1hbmFnZXIsIGFnb3JhU2VydmljZSlcbiAgICAzNVx0XG4gICAgMzZcdFx0Ly8gU2V0dXAgR2luIHJvdXRlclxuICAgIDM3XHRcdHJvdXRlciA6PSBzZXR1cFJvdXRlcihjYWxsSGFuZGxlcilcbiAgICAzOFx0XG4gICAgMzlcdFx0Ly8gU2V0dXAgZ3JhY2VmdWwgc2h1dGRvd25cbiAgICA0MFx0XHRzZXR1cEdyYWNlZnVsU2h1dGRvd24oYWdvcmFTZXJ2aWNlKVxuICAgIDQxXHRcbiAgICA0Mlx0XHQvLyBTdGFydCBzZXJ2ZXJcbiAgICA0M1x0XHRhZGRyZXNzIDo9IGZtdC5TcHJpbnRmKFwiJXM6JXNcIiwgY2ZnLlNlcnZlci5Ib3N0LCBjZmcuU2VydmVyLlBvcnQpXG4gICAgNDRcdFx0bG9nLlByaW50ZihcIuKchSBTZXJ2ZXIgc3RhcnRpbmcgb24gJXNcIiwgYWRkcmVzcylcbiAgICA0NVx0XG4gICAgNDZcdFx0aWYgZXJyIDo9IHJvdXRlci5SdW4oYWRkcmVzcyk7IGVyciAhPSBuaWwge1xuICAgIDQ3XHRcdFx0bG9nLkZhdGFsZihcIuKdjCBGYWlsZWQgdG8gc3RhcnQgc2VydmVyOiAldlwiLCBlcnIpXG4gICAgNDhcdFx0fVxuICAgIDQ5XHR9XG4gICAgNTBcdFxuICAgIDUxXHQvLyBzZXR1cFJvdXRlciBjb25maWd1cmVzIHRoZSBHaW4gcm91dGVyIHdpdGggYWxsIHJvdXRlcyBhbmQgbWlkZGxld2FyZVxuICAgIDUyXHRmdW5jIHNldHVwUm91dGVyKGNhbGxIYW5kbGVyICpoYW5kbGVycy5DYWxsSGFuZGxlcikgKmdpbi5FbmdpbmUge1xuICAgIDUzXHRcdC8vIFNldCBHaW4gbW9kZVxuICAgIDU0XHRcdGdpbi5TZXRNb2RlKGdpbi5SZWxlYXNlTW9kZSlcbiAgICA1NVx0XG4gICAgNTZcdFx0cm91dGVyIDo9IGdpbi5OZXcoKVxuICAgIDU3XHRcbiAgICA1OFx0XHQvLyBBZGQgbWlkZGxld2FyZVxuICAgIDU5XHRcdHJvdXRlci5Vc2UobWlkZGxld2FyZS5Mb2dnZXJNaWRkbGV3YXJlKCkpXG4gICAgNjBcdFx0cm91dGVyLlVzZShtaWRkbGV3YXJlLkNPUlNNaWRkbGV3YXJlKCkpXG4gICAgNjFcdFx0cm91dGVyLlVzZShnaW4uUmVjb3ZlcnkoKSlcbiAgICA2Mlx0XG4gICAgNjNcdFx0Ly8gSGVhbHRoIGNoZWNrIGVuZHBvaW50XG4gICAgNjRcdFx0cm91dGVyLkdFVChcIi9oZWFsdGhcIiwgY2FsbEhhbmRsZXIuSGVhbHRoQ2hlY2spXG4uLi5cbiAgICA4Mlx0XG4gICAgODNcdFx0Ly8gV2VsY29tZSByb3V0ZVxuICAgIDg0XHRcdHJvdXRlci5HRVQoXCIvXCIsIGZ1bmMoYyAqZ2luLkNvbnRleHQpIHtcbiAgICA4NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBnaW4uSHtcbiAgICA4Nlx0XHRcdFx0XCJtZXNzYWdlXCI6IFwi8J+kliBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIixcbiAgICA4N1x0XHRcdFx0XCJ2ZXJzaW9uXCI6IFwiMS4wLjBcIixcbiAgICA4OFx0XHRcdFx0XCJzdGF0dXNcIjogIFwicnVubmluZ1wiLFxuICAgIDg5XHRcdFx0XHRcImVuZHBvaW50c1wiOiBnaW4uSHtcbiAgICA5MFx0XHRcdFx0XHRcImhlYWx0aFwiOiAgICAgICAgICAgICAgICBcIkdFVCAvaGVhbHRoXCIsXG4gICAgOTFcdFx0XHRcdFx0XCJpbml0aWF0ZV9jYWxsXCI6ICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvaW5pdGlhdGVcIixcbiAgICA5Mlx0XHRcdFx0XHRcImN1c3RvbWVyX2luaXRpYXRlXCI6ICAgICBcIlBPU1QgL2FwaS9jYWxscy9jdXN0b21lci1pbml0aWF0ZVwiLFxuICAgIDkzXHRcdFx0XHRcdFwiam9pbl9jYWxsXCI6ICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2pvaW5cIixcbiAgICA5NFx0XHRcdFx0XHRcImdldF9jYWxsXCI6ICAgICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkXCIsXG4gICAgOTVcdFx0XHRcdFx0XCJlbmRfY2FsbFwiOiAgICAgICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvZW5kXCIsXG4gICAgOTZcdFx0XHRcdFx0XCJhY3RpdmVfY2FsbHNcIjogICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy9hY3RpdmVcIixcbiAgICA5N1x0XHRcdFx0XHRcImNhbGxfc3RhdHNcIjogICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3N0YXRzXCIsXG4gICAgOThcdFx0XHRcdFx0XCJnZW5lcmF0ZV90b2tlblwiOiAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvdG9rZW5cIixcbiAgICA5OVx0XHRcdFx0fSxcbiAgIDEwMFx0XHRcdH0pXG4gICAxMDFcdFx0fSlcbiAgIDEwMlx0XG4gICAxMDNcdFx0bG9nLlByaW50ZihcIvCfm6PvuI8gUm91dGVzIGNvbmZpZ3VyZWQgc3VjY2Vzc2Z1bGx5XCIpXG4gICAxMDRcdFx0cmV0dXJuIHJvdXRlclxuICAgMTA1XHR9XG4uLi5cbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUwzTm1LbUhvYTh1WTFLUXJhOTNTcUQifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNzoxNzoxMy45NDZaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgY2hlY2sgaWYgdGhlcmUgYXJlIGFueSBGbHV0dGVyL0RhcnQgZmlsZXMgaW4gdGhlIGNvZGViYXNlIHRvIHVuZGVyc3RhbmQgdGhlIGZyb250ZW5kIGltcGxlbWVudGF0aW9uOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjI2OWY5MWVlLTVhOTMtNGZiMC1iMzVkLTViNWZjYTlmYzU4OSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUVnNlB6VEZEZjJrUUc1aVZwcTkybnAiLCJ0b29sX25hbWUiOiJzdHItcmVwbGFjZS1lZGl0b3IiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcInN0cl9yZXBsYWNlXCIsIFwicGF0aFwiOiBcInNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ29cIiwgXCJpbnN0cnVjdGlvbl9yZW1pbmRlclwiOiBcIkFMV0FZUyBCUkVBSyBET1dOIEVESVRTIElOVE8gU01BTExFUiBDSFVOS1MgT0YgQVQgTU9TVCAyMDAgTElORVMgRUFDSC5cIiwgXCJvbGRfc3RyXzFcIjogXCIvLyBUb2tlblJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgZm9yIEFnb3JhIHRva2VuXFxudHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcXG5cXHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XFxcImNoYW5uZWxfbmFtZVxcXCJgXFxuXFx0VXNlcklEICAgICAgc3RyaW5nIGBqc29uOlxcXCJ1c2VyX2lkXFxcImBcXG5cXHRSb2xlICAgICAgICBpbnQgICAgYGpzb246XFxcInJvbGVcXFwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxcbn1cIiwgXCJuZXdfc3RyXzFcIjogXCIvLyBUb2tlblJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgZm9yIEFnb3JhIHRva2VuXFxudHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcXG5cXHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XFxcImNoYW5uZWxfbmFtZVxcXCJgXFxuXFx0VXNlcklEICAgICAgaW50ICAgIGBqc29uOlxcXCJ1c2VyX2lkXFxcImBcXG5cXHRSb2xlICAgICAgICBpbnQgICAgYGpzb246XFxcInJvbGVcXFwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxcbn1cIiwgXCJvbGRfc3RyX3N0YXJ0X2xpbmVfbnVtYmVyXzFcIjogMjMsIFwib2xkX3N0cl9lbmRfbGluZV9udW1iZXJfMVwiOiAyN30iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiJJdCBzZWVtcyB0aGUgRmx1dHRlciBhcHAgZmlsZXMgYXJlIG5vdCBpbiB0aGlzIGJhY2tlbmQgcmVwb3NpdG9yeS4gTGV0IG1lIGNyZWF0ZSBhIGRldGFpbGVkIHBsYW4gdG8gdXBkYXRlIHRoZSB1c2VySUQgZnJvbSBzdHJpbmcgdG8gaW50IGluIHRoZSBiYWNrZW5kLiBUaGUgaXNzdWUgaXMgbGlrZWx5IHRoYXQgdGhlIGN1cnJlbnQgaW1wbGVtZW50YXRpb24gdXNlcyBzdHJpbmcgdXNlcklEcyBidXQgQWdvcmEgZXhwZWN0cyBudW1lcmljIFVJRHMsIGFuZCB0aGUgY29udmVyc2lvbiBsb2dpYyBtaWdodCBub3QgYmUgd29ya2luZyBjb3JyZWN0bHkuXG5cbiMjIFBsYW4gdG8gVXBkYXRlIFVzZXJJRCBmcm9tIFN0cmluZyB0byBJbnRcblxuQmFzZWQgb24gbXkgYW5hbHlzaXMsIGhlcmUncyB3aGF0IG5lZWRzIHRvIGJlIGNoYW5nZWQgaW4gdGhlIGJhY2tlbmQ6XG5cbiMjIyAqKkZpbGVzIHRvIG1vZGlmeToqKlxuMS4gKipgc2VydmljZXMvYWdvcmFfc2VydmljZS5nb2AqKiAtIFVwZGF0ZSB0b2tlbiBnZW5lcmF0aW9uIHRvIHVzZSBpbnQgdXNlcklEXG4yLiAqKmBoYW5kbGVycy9jYWxsX2hhbmRsZXIuZ29gKiogLSBVcGRhdGUgQVBJIGhhbmRsZXJzIHRvIHVzZSBpbnQgdXNlcklEICBcbjMuICoqYG1vZGVscy9jYWxsLmdvYCoqIC0gVXBkYXRlIENhbGwgbW9kZWwgdG8gdXNlIGludCBjdXN0b21lcklEXG40LiAqKmBzZXJ2aWNlcy9jYWxsX21hbmFnZXIuZ29gKiogLSBVcGRhdGUgY2FsbCBtYW5hZ2VtZW50IHRvIHVzZSBpbnQgY3VzdG9tZXJJRFxuXG4jIyMgKipLZXkgSXNzdWVzIElkZW50aWZpZWQ6KipcbjEuIFRoZSBjdXJyZW50IGNvZGUgdXNlcyBzdHJpbmcgdXNlcklEcyBidXQgaGFzIGluY29uc2lzdGVudCBjb252ZXJzaW9uIHRvIHVpbnQzMiBmb3IgQWdvcmFcbjIuIEhhcmQtY29kZWQgc3RyaW5nIHZhbHVlcyBsaWtlIFwiYWlfYWdlbnRcIiBhbmQgXCJ0ZXN0X2N1c3RvbWVyXzEyM1wiIGFyZSBiZWluZyB1c2VkXG4zLiBUaGUgY29udmVyc2lvbiBsb2dpYyBpbiBgZ2VuZXJhdGVSZWFsVG9rZW5gIHVzZXMgYSBmYWxsYmFjayB0aGF0IG1pZ2h0IG5vdCBiZSByZWxpYWJsZVxuXG5MZXQgbWUgaW1wbGVtZW50IHRoZXNlIGNoYW5nZXM6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InVuc2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IHNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcImZtdFwiXG4gICAgIDZcdFx0XCJsb2dcIlxuICAgICA3XHRcdFwic3RyY29udlwiXG4gICAgIDhcdFx0XCJ0aW1lXCJcbiAgICAgOVx0XG4gICAgMTBcdFx0XCJnaXRodWIuY29tL0Fnb3JhSU8vVG9vbHMvRHluYW1pY0tleS9BZ29yYUR5bmFtaWNLZXkvZ28vc3JjL3J0Y3Rva2VuYnVpbGRlcjJcIlxuICAgIDExXHQpXG4gICAgMTJcdFxuICAgIDEzXHQvLyBBZ29yYVNlcnZpY2UgaGFuZGxlcyBBZ29yYSBSVEMgb3BlcmF0aW9uc1xuICAgIDE0XHR0eXBlIEFnb3JhU2VydmljZSBzdHJ1Y3Qge1xuICAgIDE1XHRcdGNvbmZpZyAqY29uZmlnLkNvbmZpZ1xuICAgIDE2XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY29udGFpbjpcbiAgICAxN1x0XHQvLyAtIEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAxOFx0XHQvLyAtIE1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDE5XHRcdC8vIC0gQWN0aXZlIGNvbm5lY3Rpb25zIG1hcFxuICAgIDIwXHRcdC8vIC0gQXVkaW8vVmlkZW8gb2JzZXJ2ZXJzXG4gICAgMjFcdH1cbiAgICAyMlx0XG4gICAgMjNcdC8vIFRva2VuUmVxdWVzdCByZXByZXNlbnRzIGEgcmVxdWVzdCBmb3IgQWdvcmEgdG9rZW5cbiAgICAyNFx0dHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcbiAgICAyNVx0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICAyNlx0XHRVc2VySUQgICAgICBzdHJpbmcgYGpzb246XCJ1c2VyX2lkXCJgXG4gICAgMjdcdFx0Um9sZSAgICAgICAgaW50ICAgIGBqc29uOlwicm9sZVwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxuICAgIDI4XHR9XG4gICAgMjlcdFxuICAgIDMwXHQvLyBUb2tlblJlc3BvbnNlIHJlcHJlc2VudHMgQWdvcmEgdG9rZW4gcmVzcG9uc2VcbiAgICAzMVx0dHlwZSBUb2tlblJlc3BvbnNlIHN0cnVjdCB7XG4gICAgMzJcdFx0VG9rZW4gICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAgMzNcdFx0RXhwaXJlc0F0IGludDY0ICBganNvbjpcImV4cGlyZXNfYXRcImBcbiAgICAzNFx0fVxuICAgIDM1XHRcbiAgICAzNlx0Ly8gTmV3QWdvcmFTZXJ2aWNlIGNyZWF0ZXMgYSBuZXcgQWdvcmEgc2VydmljZSBpbnN0YW5jZVxuICAgIDM3XHRmdW5jIE5ld0Fnb3JhU2VydmljZShjZmcgKmNvbmZpZy5Db25maWcpICpBZ29yYVNlcnZpY2Uge1xuICAgIDM4XHRcdHNlcnZpY2UgOj0gJkFnb3JhU2VydmljZXtcbiAgICAzOVx0XHRcdGNvbmZpZzogY2ZnLFxuICAgIDQwXHRcdH1cbiAgICA0MVx0XG4gICAgNDJcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgaW5pdGlhbGl6ZSBBZ29yYSBTREsgaGVyZTpcbiAgICA0M1x0XHQvLyAxLiBJbml0aWFsaXplIEFnb3JhIHNlcnZpY2Ugd2l0aCBhcHAgSURcbiAgICA0NFx0XHQvLyAyLiBDcmVhdGUgbWVkaWEgbm9kZSBmYWN0b3J5XG4gICAgNDVcdFx0Ly8gMy4gU2V0IHVwIGF1ZGlvL3ZpZGVvIHByb2Nlc3NpbmcgY2FsbGJhY2tzXG4gICAgNDZcdFxuICAgIDQ3XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIHNlcnZpY2UgaW5pdGlhbGl6ZWQgd2l0aCBBcHAgSUQ6ICVzXCIsIGNmZy5BZ29yYS5BcHBJRClcbiAgICA0OFx0XHRyZXR1cm4gc2VydmljZVxuICAgIDQ5XHR9XG4gICAgNTBcdFxuICAgIDUxXHQvLyBHZW5lcmF0ZVRva2VuIGdlbmVyYXRlcyBhbiBBZ29yYSBSVEMgdG9rZW4gZm9yIGNoYW5uZWwgYWNjZXNzXG4gICAgNTJcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEdlbmVyYXRlVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50KSAoKlRva2VuUmVzcG9uc2UsIGVycm9yKSB7XG4gICAgNTNcdFx0bG9nLlByaW50ZihcIvCflJEgR2VuZXJhdGluZyBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXMsIHVzZXI6ICVzLCByb2xlOiAlZFwiLCBjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDU0XHRcbiAgICA1NVx0XHQvLyBUb2tlbiBleHBpcmVzIGluIDI0IGhvdXJzXG4gICAgNTZcdFx0ZXhwaXJhdGlvblRpbWUgOj0gdGltZS5Ob3coKS5BZGQoMjQgKiB0aW1lLkhvdXIpLlVuaXgoKVxuICAgIDU3XHRcbiAgICA1OFx0XHR2YXIgdG9rZW4gc3RyaW5nXG4gICAgNTlcdFx0dmFyIGVyciBlcnJvclxuICAgIDYwXHRcbiAgICA2MVx0XHRpZiBhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUgIT0gXCJcIiB7XG4gICAgNjJcdFx0XHQvLyBHZW5lcmF0ZSByZWFsIHRva2VuIHdpdGggb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDYzXHRcdFx0dG9rZW4sIGVyciA9IGFzLmdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHVpbnQzMihleHBpcmF0aW9uVGltZSkpXG4gICAgNjRcdFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2NVx0XHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlbjogJXdcIiwgZXJyKVxuICAgIDY2XHRcdFx0fVxuICAgIDY3XHRcdFx0bG9nLlByaW50ZihcIuKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXMsIHRva2VuIGlzOiAlc1wiLCBjaGFubmVsTmFtZSwgdG9rZW4pXG4gICAgNjhcdFx0fSBlbHNlIHtcbiAgICA2OVx0XHRcdC8vIEdlbmVyYXRlIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAgNzBcdFx0XHR0b2tlbiA9IGFzLmdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNzFcdFx0XHRsb2cuUHJpbnRmKFwi4pqg77iPIFtNT0NLXSBVc2luZyBtb2NrIHRva2VuIChubyBhcHAgY2VydGlmaWNhdGUgcHJvdmlkZWQpXCIpXG4gICAgNzJcdFx0fVxuICAgIDczXHRcbiAgICA3NFx0XHRyZXNwb25zZSA6PSAmVG9rZW5SZXNwb25zZXtcbiAgICA3NVx0XHRcdFRva2VuOiAgICAgdG9rZW4sXG4gICAgNzZcdFx0XHRFeHBpcmVzQXQ6IGV4cGlyYXRpb25UaW1lLFxuICAgIDc3XHRcdH1cbiAgICA3OFx0XG4gICAgNzlcdFx0bG9nLlByaW50ZihcIuKchSBUb2tlbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5IGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgICA4MFx0XHRyZXR1cm4gcmVzcG9uc2UsIG5pbFxuICAgIDgxXHR9XG4gICAgODJcdFxuICAgIDgzXHQvLyBnZW5lcmF0ZVJlYWxUb2tlbiBnZW5lcmF0ZXMgYSByZWFsIEFnb3JhIHRva2VuIHVzaW5nIHRoZSBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAgODRcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCwgZXhwaXJhdGlvbiB1aW50MzIpIChzdHJpbmcsIGVycm9yKSB7XG4gICAgODVcdFx0Ly8gQ29udmVydCB1c2VySUQgdG8gdWludDMyIChBZ29yYSBleHBlY3RzIG51bWVyaWMgVUlEKVxuICAgIDg2XHRcdHZhciB1aWQgdWludDMyXG4gICAgODdcdFx0aWYgdXNlcklEICE9IFwiXCIge1xuICAgIDg4XHRcdFx0aWYgcGFyc2VkVUlELCBlcnIgOj0gc3RyY29udi5QYXJzZVVpbnQodXNlcklELCAxMCwgMzIpOyBlcnIgPT0gbmlsIHtcbiAgICA4OVx0XHRcdFx0dWlkID0gdWludDMyKHBhcnNlZFVJRClcbiAgICA5MFx0XHRcdH0gZWxzZSB7XG4gICAgOTFcdFx0XHRcdC8vIElmIHVzZXJJRCBpcyBub3QgbnVtZXJpYywgZ2VuZXJhdGUgYSBoYXNoLWJhc2VkIFVJRFxuICAgIDkyXHRcdFx0XHR1aWQgPSB1aW50MzIobGVuKHVzZXJJRCkpIC8vIFNpbXBsZSBmYWxsYmFjaywgaW4gcHJvZHVjdGlvbiB1c2UgYSBwcm9wZXIgaGFzaFxuICAgIDkzXHRcdFx0fVxuICAgIDk0XHRcdH0gZWxzZSB7XG4gICAgOTVcdFx0XHR1aWQgPSAwIC8vIExldCBBZ29yYSBhc3NpZ24gYSBVSURcbiAgICA5Nlx0XHR9XG4gICAgOTdcdFxuICAgIDk4XHRcdC8vIENvbnZlcnQgcm9sZSB0byBBZ29yYSByb2xlIHR5cGVcbiAgICA5OVx0XHR2YXIgYWdvcmFSb2xlIHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVxuICAgMTAwXHRcdGlmIHJvbGUgPT0gMSB7XG4gICAxMDFcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVQdWJsaXNoZXJcbiAgIDEwMlx0XHR9IGVsc2Uge1xuICAgMTAzXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlU3Vic2NyaWJlclxuICAgMTA0XHRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdFx0Ly8gR2VuZXJhdGUgdG9rZW4gdXNpbmcgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgMTA3XHRcdHRva2VuLCBlcnIgOj0gcnRjdG9rZW5idWlsZGVyMi5CdWlsZFRva2VuV2l0aFVpZChcbiAgIDEwOFx0XHRcdGFzLmNvbmZpZy5BZ29yYS5BcHBJRCxcbiAgIDEwOVx0XHRcdGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSxcbiAgIDExMFx0XHRcdGNoYW5uZWxOYW1lLFxuICAgMTExXHRcdFx0dWlkLFxuICAgMTEyXHRcdFx0YWdvcmFSb2xlLFxuICAgMTEzXHRcdFx0ZXhwaXJhdGlvbixcbiAgIDExNFx0XHRcdGV4cGlyYXRpb24sIC8vIFByaXZpbGVnZSBleHBpcmVkIHRpbWVzdGFtcCAoc2FtZSBhcyB0b2tlbiBleHBpcmF0aW9uKVxuICAgMTE1XHRcdClcbiAgIDExNlx0XG4gICAxMTdcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxMThcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byBidWlsZCBBZ29yYSB0b2tlbjogJXdcIiwgZXJyKVxuICAgMTE5XHRcdH1cbiAgIDEyMFx0XG4gICAxMjFcdFx0cmV0dXJuIHRva2VuLCBuaWxcbiAgIDEyMlx0fVxuICAgMTIzXHRcbiAgIDEyNFx0Ly8gZ2VuZXJhdGVNb2NrVG9rZW4gZ2VuZXJhdGVzIGEgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgIDEyNVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2VuZXJhdGVNb2NrVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50KSBzdHJpbmcge1xuICAgMTI2XHRcdHRpbWVzdGFtcCA6PSB0aW1lLk5vdygpLlVuaXgoKVxuICAgMTI3XHRcdHJldHVybiBmbXQuU3ByaW50ZihcIm1vY2tfdG9rZW5fJXNfJXNfJWRfJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSwgdGltZXN0YW1wKVxuICAgMTI4XHR9XG4gICAxMjlcdFxuICAgMTMwXHQvLyBKb2luQ2hhbm5lbCBqb2lucyBhbiBBZ29yYSBjaGFubmVsIGZvciBBSSBwcm9jZXNzaW5nXG4gICAxMzFcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEpvaW5DaGFubmVsKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nKSBlcnJvciB7XG4gICAxMzJcdFx0bG9nLlByaW50ZihcIvCflJcgW01PQ0tdIEFJIGpvaW5pbmcgQWdvcmEgY2hhbm5lbDogJXMgYXMgdXNlcjogJXNcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRClcbiAgIDEzM1x0XG4gICAxMzRcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDEzNVx0XHQvLyAxLiBDcmVhdGUgUlRDIGNvbm5lY3Rpb25cbiAgIDEzNlx0XHQvLyAyLiBTZXQgdXAgbG9jYWwgdXNlclxuICAgMTM3XHRcdC8vIDMuIENyZWF0ZSBhdWRpbyB0cmFjayBmb3IgQUkgdm9pY2VcbiAgIDEzOFx0XHQvLyA0LiBSZWdpc3RlciBhdWRpbyBmcmFtZSBvYnNlcnZlcnMgZm9yIHByb2Nlc3NpbmcgaW5jb21pbmcgYXVkaW9cbiAgIDEzOVx0XHQvLyA1LiBKb2luIHRoZSBjaGFubmVsXG4gICAxNDBcdFx0Ly8gNi4gU3RhcnQgYXVkaW8gcHJvY2Vzc2luZyBwaXBlbGluZVxuICAgMTQxXHRcbiAgIDE0Mlx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uXG4gICAxNDNcdFx0dGltZS5TbGVlcCg1MDAgKiB0aW1lLk1pbGxpc2Vjb25kKSAvLyBTaW11bGF0ZSBjb25uZWN0aW9uIHRpbWVcbiAgIDE0NFx0XG4gICAxNDVcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQUkgc3VjY2Vzc2Z1bGx5IGpvaW5lZCBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE0Nlx0XHRyZXR1cm4gbmlsXG4gICAxNDdcdH1cbiAgIDE0OFx0XG4gICAxNDlcdC8vIExlYXZlQ2hhbm5lbCBsZWF2ZXMgYW4gQWdvcmEgY2hhbm5lbFxuICAgMTUwXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBMZWF2ZUNoYW5uZWwoY2FsbElEIHN0cmluZykgZXJyb3Ige1xuICAgMTUxXHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDE1Mlx0XHRsb2cuUHJpbnRmKFwi8J+RiyBbTU9DS10gQUkgbGVhdmluZyBBZ29yYSBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE1M1x0XG4gICAxNTRcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDE1NVx0XHQvLyAxLiBTdG9wIGF1ZGlvIHByb2Nlc3NpbmdcbiAgIDE1Nlx0XHQvLyAyLiBVbnB1Ymxpc2ggYXVkaW8gdHJhY2tzXG4gICAxNTdcdFx0Ly8gMy4gRGlzY29ubmVjdCBmcm9tIGNoYW5uZWxcbiAgIDE1OFx0XHQvLyA0LiBDbGVhbiB1cCByZXNvdXJjZXNcbiAgIDE1OVx0XG4gICAxNjBcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvblxuICAgMTYxXHRcdHRpbWUuU2xlZXAoMjAwICogdGltZS5NaWxsaXNlY29uZCkgLy8gU2ltdWxhdGUgZGlzY29ubmVjdGlvbiB0aW1lXG4gICAxNjJcdFxuICAgMTYzXHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEFJIHN1Y2Nlc3NmdWxseSBsZWZ0IGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTY0XHRcdHJldHVybiBuaWxcbiAgIDE2NVx0fVxuICAgMTY2XHRcbiAgIDE2N1x0Ly8gU2VuZEF1ZGlvVG9DaGFubmVsIHNlbmRzIEFJLWdlbmVyYXRlZCBhdWRpbyB0byB0aGUgY2hhbm5lbFxuICAgMTY4XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBTZW5kQXVkaW9Ub0NoYW5uZWwoY2FsbElEIHN0cmluZywgYXVkaW9EYXRhIFtdYnl0ZSkgZXJyb3Ige1xuICAgMTY5XHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDE3MFx0XHRsb2cuUHJpbnRmKFwi8J+UiiBbTU9DS10gU2VuZGluZyAlZCBieXRlcyBvZiBhdWRpbyB0byBjaGFubmVsOiAlc1wiLCBsZW4oYXVkaW9EYXRhKSwgY2hhbm5lbE5hbWUpXG4gICAxNzFcdFxuICAgMTcyXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxNzNcdFx0Ly8gMS4gQ29udmVydCBhdWRpbyBkYXRhIHRvIHJlcXVpcmVkIGZvcm1hdCAoUENNKVxuICAgMTc0XHRcdC8vIDIuIFB1c2ggYXVkaW8gZnJhbWVzIHRvIEFnb3JhIGF1ZGlvIHRyYWNrXG4gICAxNzVcdFx0Ly8gMy4gSGFuZGxlIGF1ZGlvIGVuY29kaW5nIGFuZCB0cmFuc21pc3Npb25cbiAgIDE3Nlx0XG4gICAxNzdcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvbiAtIHNpbXVsYXRlIGF1ZGlvIHRyYW5zbWlzc2lvbiB0aW1lXG4gICAxNzhcdFx0dHJhbnNtaXNzaW9uVGltZSA6PSB0aW1lLkR1cmF0aW9uKGxlbihhdWRpb0RhdGEpLzEwMDApICogdGltZS5NaWxsaXNlY29uZFxuICAgMTc5XHRcdGlmIHRyYW5zbWlzc2lvblRpbWUgPiAxMDAqdGltZS5NaWxsaXNlY29uZCB7XG4gICAxODBcdFx0XHR0cmFuc21pc3Npb25UaW1lID0gMTAwICogdGltZS5NaWxsaXNlY29uZFxuICAgMTgxXHRcdH1cbi4uLlxuICAgMTg3XHRcbiAgIDE4OFx0Ly8gU2V0dXBBdWRpb1Byb2Nlc3Npbmcgc2V0cyB1cCBhdWRpbyBwcm9jZXNzaW5nIGNhbGxiYWNrc1xuICAgMTg5XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBTZXR1cEF1ZGlvUHJvY2Vzc2luZyhjYWxsSUQgc3RyaW5nLCBhdWRpb0NhbGxiYWNrIGZ1bmMoW11ieXRlKSBlcnJvcikgZXJyb3Ige1xuICAgMTkwXHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDE5MVx0XHRsb2cuUHJpbnRmKFwi8J+Ome+4jyBbTU9DS10gU2V0dGluZyB1cCBhdWRpbyBwcm9jZXNzaW5nIGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE5Mlx0XG4gICAxOTNcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDE5NFx0XHQvLyAxLiBSZWdpc3RlciBhdWRpbyBmcmFtZSBvYnNlcnZlclxuICAgMTk1XHRcdC8vIDIuIFNldCB1cCBWQUQgKFZvaWNlIEFjdGl2aXR5IERldGVjdGlvbilcbiAgIDE5Nlx0XHQvLyAzLiBDb25maWd1cmUgYXVkaW8gZm9ybWF0IChzYW1wbGUgcmF0ZSwgY2hhbm5lbHMsIGV0Yy4pXG4gICAxOTdcdFx0Ly8gNC4gU3RhcnQgcHJvY2Vzc2luZyBpbmNvbWluZyBhdWRpbyBmcmFtZXNcbiAgIDE5OFx0XG4gICAxOTlcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvbiAtIHNpbXVsYXRlIHJlY2VpdmluZyBhdWRpbyBwZXJpb2RpY2FsbHlcbiAgIDIwMFx0XHRnbyBhcy5zaW11bGF0ZUluY29taW5nQXVkaW8oY2FsbElELCBhdWRpb0NhbGxiYWNrKVxuICAgMjAxXHRcbiAgIDIwMlx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBdWRpbyBwcm9jZXNzaW5nIHNldHVwIGNvbXBsZXRlIGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDIwM1x0XHRyZXR1cm4gbmlsXG4gICAyMDRcdH1cbi4uLlxuUGF0aDogaGFuZGxlcnMvY2FsbF9oYW5kbGVyLmdvXG4uLi5cbiAgICA2Mlx0XG4gICAgNjNcdFx0Ly8gR2VuZXJhdGUgQWdvcmEgdG9rZW4gZm9yIHRoZSBjaGFubmVsXG4gICAgNjRcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCBcImFpX2FnZW50XCIsIDEpIC8vIFJvbGUgMSA9IHB1Ymxpc2hlclxuICAgIDY1XHRcdGlmIGVyciAhPSBuaWwge1xuICAgIDY2XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgNjdcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSBBZ29yYSB0b2tlblwiLFxuICAgIDY4XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgNjlcdFx0XHR9KVxuICAgIDcwXHRcdFx0cmV0dXJuXG4gICAgNzFcdFx0fVxuICAgIDcyXHRcbiAgICA3M1x0XHQvLyBKb2luIHRoZSBjaGFubmVsIGFzIEFJIGFnZW50XG4gICAgNzRcdFx0aWYgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5Kb2luQ2hhbm5lbChjYWxsLkNoYW5uZWxOYW1lLCBcImFpX2FnZW50XCIpOyBlcnIgIT0gbmlsIHtcbiAgICA3NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDc2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gam9pbiBBZ29yYSBjaGFubmVsXCIsXG4gICAgNzdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA3OFx0XHRcdH0pXG4gICAgNzlcdFx0XHRyZXR1cm5cbiAgICA4MFx0XHR9XG4gICAgODFcdFxuICAgIDgyXHRcdC8vIFNldCB1cCBhdWRpbyBwcm9jZXNzaW5nIGZvciB0aGlzIGNhbGxcbiAgICA4M1x0XHRhdWRpb0NhbGxiYWNrIDo9IGZ1bmMoYXVkaW9EYXRhIFtdYnl0ZSkgZXJyb3Ige1xuICAgIDg0XHRcdFx0cmV0dXJuIGNoLmNhbGxNYW5hZ2VyLlByb2Nlc3NBdWRpb01lc3NhZ2UoY2FsbC5JRCwgYXVkaW9EYXRhKVxuICAgIDg1XHRcdH1cbi4uLlxuICAgMTMxXHRcbiAgIDEzMlx0XHQvLyBJbml0aWF0ZSB0aGUgY3VzdG9tZXItdG8tQUkgY2FsbFxuICAgMTMzXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Jbml0aWF0ZUN1c3RvbWVyQ2FsbChyZXF1ZXN0KVxuICAgMTM0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTM1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAxMzZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBpbml0aWF0ZSBjdXN0b21lciBjYWxsXCIsXG4gICAxMzdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDEzOFx0XHRcdH0pXG4gICAxMzlcdFx0XHRyZXR1cm5cbiAgIDE0MFx0XHR9XG4gICAxNDFcdFxuICAgMTQyXHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY3VzdG9tZXJcbiAgIDE0M1x0XHRjdXN0b21lclRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgcmVxdWVzdC5DdXN0b21lcklELCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgIDE0NFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE0NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMTQ2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgY3VzdG9tZXIgQWdvcmEgdG9rZW5cIixcbiAgIDE0N1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMTQ4XHRcdFx0fSlcbiAgIDE0OVx0XHRcdHJldHVyblxuICAgMTUwXHRcdH1cbi4uLlxuICAgMjQ4XHRcbiAgIDI0OVx0XHQvLyBKb2luIHRoZSBjYWxsXG4gICAyNTBcdFx0Y2FsbCwgZXJyIDo9IGNoLmNhbGxNYW5hZ2VyLkpvaW5DYWxsKHJlcXVlc3QuQ2FsbElELCByZXF1ZXN0LkN1c3RvbWVySUQpXG4gICAyNTFcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAyNTJcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgIDI1M1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGpvaW4gY2FsbFwiLFxuICAgMjU0XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAyNTVcdFx0XHR9KVxuICAgMjU2XHRcdFx0cmV0dXJuXG4gICAyNTdcdFx0fVxuICAgMjU4XHRcbiAgIDI1OVx0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGN1c3RvbWVyXG4gICAyNjBcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCByZXF1ZXN0LkN1c3RvbWVySUQsIDEpIC8vIFJvbGUgMSA9IHB1Ymxpc2hlclxuICAgMjYxXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMjYyXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAyNjNcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSBBZ29yYSB0b2tlblwiLFxuICAgMjY0XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAyNjVcdFx0XHR9KVxuICAgMjY2XHRcdFx0cmV0dXJuXG4gICAyNjdcdFx0fVxuICAgMjY4XHRcbiAgIDI2OVx0XHRyZXNwb25zZSA6PSBtb2RlbHMuSm9pbkNhbGxSZXNwb25zZXtcbiAgIDI3MFx0XHRcdENoYW5uZWxOYW1lOiBjYWxsLkNoYW5uZWxOYW1lLFxuICAgMjcxXHRcdFx0VG9rZW46ICAgICAgIHRva2VuLlRva2VuLFxuICAgMjcyXHRcdFx0Q2FsbEluZm86ICAgICpjYWxsLFxuICAgMjczXHRcdH1cbiAgIDI3NFx0XG4gICAyNzVcdFx0Yy5KU09OKGh0dHAuU3RhdHVzT0ssIHJlc3BvbnNlKVxuICAgMjc2XHR9XG4uLi5cblBhdGg6IC5lbnZcbiAgICAgMVx0IyBTZXJ2ZXIgQ29uZmlndXJhdGlvblxuICAgICAyXHRTRVJWRVJfUE9SVD04MDgwXG4gICAgIDNcdFNFUlZFUl9IT1NUPTAuMC4wLjBcbiAgICAgNFx0XG4gICAgIDVcdCMgQWdvcmEgQ29uZmlndXJhdGlvblxuICAgICA2XHRBR09SQV9BUFBfSUQ9MDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcbiAgICAgN1x0QUdPUkFfQVBQX0NFUlRJRklDQVRFPWU3ZjM3ZTY3ZDgyYjRhZmY4NDYzMTc3NGMxYjVkZTYxXG4gICAgIDhcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAgIDlcdFxuICAgIDEwXHQjIEFJIENvbmZpZ3VyYXRpb25cbiAgICAxMVx0T1BFTkFJX0FQSV9LRVk9ay1wcm9qLXp4M1RXWFItTEtjTVBUbTJMd3EwdzJnb3B1elZPZjR0dWlneFFINWlwaU95UkNLOXpGT09nRU1Cam1xZjBBN1FWZEMxSEc3MWtNVDNCbGJrRkoyTGNnaUJBdXBsNVU5YkI1ckVUeVBNUmxFVl8xR1ota1pzV3MxSmNUcEJZUy1saGthRURETUF2RXFmbnRZUUFVZExLZW5QMzZVQVxuICAgIDEyXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgICAxM1x0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgIDE0XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAgMTVcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgIDE2XHRFTkFCTEVfVkFEPXRydWVcbiAgICAxN1x0VkFEX1NFTlNJVElWSVRZPTAuN1xuLi4uXG5QYXRoOiBtYWluLmdvXG4uLi5cbiAgICA2NVx0XG4gICAgNjZcdFx0Ly8gQVBJIHJvdXRlc1xuICAgIDY3XHRcdGFwaSA6PSByb3V0ZXIuR3JvdXAoXCIvYXBpXCIpXG4gICAgNjhcdFx0e1xuICAgIDY5XHRcdFx0Ly8gQ2FsbCBtYW5hZ2VtZW50IHJvdXRlc1xuICAgIDcwXHRcdFx0Y2FsbHMgOj0gYXBpLkdyb3VwKFwiL2NhbGxzXCIpXG4gICAgNzFcdFx0XHR7XG4gICAgNzJcdFx0XHRcdGNhbGxzLlBPU1QoXCIvaW5pdGlhdGVcIiwgY2FsbEhhbmRsZXIuSW5pdGlhdGVDYWxsKSAgICAgICAgICAgICAgICAgICAgLy8gQUkgY2FsbHMgY3VzdG9tZXJcbiAgICA3M1x0XHRcdFx0Y2FsbHMuUE9TVChcIi9jdXN0b21lci1pbml0aWF0ZVwiLCBjYWxsSGFuZGxlci5Jbml0aWF0ZUN1c3RvbWVyQ2FsbCkgICAvLyBDdXN0b21lciBjYWxscyBBSVxuICAgIDc0XHRcdFx0XHRjYWxscy5QT1NUKFwiLzpjYWxsX2lkL2pvaW5cIiwgY2FsbEhhbmRsZXIuSm9pbkNhbGwpXG4gICAgNzVcdFx0XHRcdGNhbGxzLkdFVChcIi86Y2FsbF9pZFwiLCBjYWxsSGFuZGxlci5HZXRDYWxsKVxuICAgIDc2XHRcdFx0XHRjYWxscy5QT1NUKFwiLzpjYWxsX2lkL2VuZFwiLCBjYWxsSGFuZGxlci5FbmRDYWxsKVxuICAgIDc3XHRcdFx0XHRjYWxscy5HRVQoXCIvYWN0aXZlXCIsIGNhbGxIYW5kbGVyLkdldEFjdGl2ZUNhbGxzKVxuICAgIDc4XHRcdFx0XHRjYWxscy5HRVQoXCIvOmNhbGxfaWQvc3RhdHNcIiwgY2FsbEhhbmRsZXIuR2V0Q2FsbFN0YXRzKVxuICAgIDc5XHRcdFx0XHRjYWxscy5QT1NUKFwiLzpjYWxsX2lkL3Rva2VuXCIsIGNhbGxIYW5kbGVyLkdlbmVyYXRlVG9rZW4pXG4gICAgODBcdFx0XHR9XG4gICAgODFcdFx0fVxuICAgIDgyXHRcbiAgICA4M1x0XHQvLyBXZWxjb21lIHJvdXRlXG4gICAgODRcdFx0cm91dGVyLkdFVChcIi9cIiwgZnVuYyhjICpnaW4uQ29udGV4dCkge1xuICAgIDg1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzT0ssIGdpbi5Ie1xuICAgIDg2XHRcdFx0XHRcIm1lc3NhZ2VcIjogXCLwn6SWIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFwiLFxuICAgIDg3XHRcdFx0XHRcInZlcnNpb25cIjogXCIxLjAuMFwiLFxuICAgIDg4XHRcdFx0XHRcInN0YXR1c1wiOiAgXCJydW5uaW5nXCIsXG4gICAgODlcdFx0XHRcdFwiZW5kcG9pbnRzXCI6IGdpbi5Ie1xuICAgIDkwXHRcdFx0XHRcdFwiaGVhbHRoXCI6ICAgICAgICAgICAgICAgIFwiR0VUIC9oZWFsdGhcIixcbiAgICA5MVx0XHRcdFx0XHRcImluaXRpYXRlX2NhbGxcIjogICAgICAgICBcIlBPU1QgL2FwaS9jYWxscy9pbml0aWF0ZVwiLFxuICAgIDkyXHRcdFx0XHRcdFwiY3VzdG9tZXJfaW5pdGlhdGVcIjogICAgIFwiUE9TVCAvYXBpL2NhbGxzL2N1c3RvbWVyLWluaXRpYXRlXCIsXG4gICAgOTNcdFx0XHRcdFx0XCJqb2luX2NhbGxcIjogICAgICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvam9pblwiLFxuICAgIDk0XHRcdFx0XHRcdFwiZ2V0X2NhbGxcIjogICAgICAgICAgICAgIFwiR0VUIC9hcGkvY2FsbHMvOmNhbGxfaWRcIixcbiAgICA5NVx0XHRcdFx0XHRcImVuZF9jYWxsXCI6ICAgICAgICAgICAgICBcIlBPU1QgL2FwaS9jYWxscy86Y2FsbF9pZC9lbmRcIixcbiAgICA5Nlx0XHRcdFx0XHRcImFjdGl2ZV9jYWxsc1wiOiAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzL2FjdGl2ZVwiLFxuICAgIDk3XHRcdFx0XHRcdFwiY2FsbF9zdGF0c1wiOiAgICAgICAgICAgIFwiR0VUIC9hcGkvY2FsbHMvOmNhbGxfaWQvc3RhdHNcIixcbiAgICA5OFx0XHRcdFx0XHRcImdlbmVyYXRlX3Rva2VuXCI6ICAgICAgICBcIlBPU1QgL2FwaS9jYWxscy86Y2FsbF9pZC90b2tlblwiLFxuICAgIDk5XHRcdFx0XHR9LFxuICAgMTAwXHRcdFx0fSlcbiAgIDEwMVx0XHR9KVxuICAgMTAyXHRcbiAgIDEwM1x0XHRsb2cuUHJpbnRmKFwi8J+bo++4jyBSb3V0ZXMgY29uZmlndXJlZCBzdWNjZXNzZnVsbHlcIilcbiAgIDEwNFx0XHRyZXR1cm4gcm91dGVyXG4gICAxMDVcdH1cbi4uLlxuUGF0aDogY29uZmlnL2NvbmZpZy5nb1xuICAgICAxXHRwYWNrYWdlIGNvbmZpZ1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcImxvZ1wiXG4gICAgIDVcdFx0XCJvc1wiXG4gICAgIDZcdFx0XCJzdHJjb252XCJcbiAgICAgN1x0XG4gICAgIDhcdFx0XCJnaXRodWIuY29tL2pvaG8vZ29kb3RlbnZcIlxuICAgICA5XHQpXG4gICAgMTBcdFxuICAgIDExXHR0eXBlIENvbmZpZyBzdHJ1Y3Qge1xuICAgIDEyXHRcdFNlcnZlciBTZXJ2ZXJDb25maWdcbiAgICAxM1x0XHRBZ29yYSAgQWdvcmFDb25maWdcbiAgICAxNFx0XHRBSSAgICAgQUlDb25maWdcbiAgICAxNVx0fVxuICAgIDE2XHRcbiAgICAxN1x0dHlwZSBTZXJ2ZXJDb25maWcgc3RydWN0IHtcbiAgICAxOFx0XHRQb3J0IHN0cmluZ1xuICAgIDE5XHRcdEhvc3Qgc3RyaW5nXG4gICAgMjBcdH1cbiAgICAyMVx0XG4gICAgMjJcdHR5cGUgQWdvcmFDb25maWcgc3RydWN0IHtcbiAgICAyM1x0XHRBcHBJRCAgICAgICAgICBzdHJpbmdcbiAgICAyNFx0XHRBcHBDZXJ0aWZpY2F0ZSBzdHJpbmdcbiAgICAyNVx0XHRDaGFubmVsUHJlZml4ICBzdHJpbmdcbiAgICAyNlx0fVxuICAgIDI3XHRcbiAgICAyOFx0dHlwZSBBSUNvbmZpZyBzdHJ1Y3Qge1xuICAgIDI5XHRcdE9wZW5BSUFQSUtleSAgICBzdHJpbmdcbiAgICAzMFx0XHRPcGVuQUlNb2RlbCAgICAgc3RyaW5nXG4gICAgMzFcdFx0U1RUUHJvdmlkZXIgICAgIHN0cmluZ1xuICAgIDMyXHRcdFRUU1Byb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzM1x0XHRWb2ljZUlEICAgICAgICAgc3RyaW5nXG4gICAgMzRcdFx0RW5hYmxlVkFEICAgICAgIGJvb2xcbiAgICAzNVx0XHRWQURTZW5zaXRpdml0eSAgZmxvYXQ2NFxuICAgIDM2XHR9XG4gICAgMzdcdFxuICAgIDM4XHR2YXIgQXBwQ29uZmlnICpDb25maWdcbiAgICAzOVx0XG4gICAgNDBcdGZ1bmMgTG9hZENvbmZpZygpIHtcbiAgICA0MVx0XHQvLyBMb2FkIC5lbnYgZmlsZSBpZiBpdCBleGlzdHNcbiAgICA0Mlx0XHRpZiBlcnIgOj0gZ29kb3RlbnYuTG9hZCgpOyBlcnIgIT0gbmlsIHtcbiAgICA0M1x0XHRcdGxvZy5QcmludGxuKFwiTm8gLmVudiBmaWxlIGZvdW5kLCB1c2luZyBlbnZpcm9ubWVudCB2YXJpYWJsZXNcIilcbiAgICA0NFx0XHR9XG4gICAgNDVcdFxuICAgIDQ2XHRcdEFwcENvbmZpZyA9ICZDb25maWd7XG4gICAgNDdcdFx0XHRTZXJ2ZXI6IFNlcnZlckNvbmZpZ3tcbiAgICA0OFx0XHRcdFx0UG9ydDogZ2V0RW52KFwiU0VSVkVSX1BPUlRcIiwgXCI4MDgwXCIpLFxuICAgIDQ5XHRcdFx0XHRIb3N0OiBnZXRFbnYoXCJTRVJWRVJfSE9TVFwiLCBcIjAuMC4wLjBcIiksXG4gICAgNTBcdFx0XHR9LFxuICAgIDUxXHRcdFx0QWdvcmE6IEFnb3JhQ29uZmlne1xuICAgIDUyXHRcdFx0XHRBcHBJRDogICAgICAgICAgZ2V0RW52KFwiQUdPUkFfQVBQX0lEXCIsIFwiXCIpLFxuICAgIDUzXHRcdFx0XHRBcHBDZXJ0aWZpY2F0ZTogZ2V0RW52KFwiQUdPUkFfQVBQX0NFUlRJRklDQVRFXCIsIFwiXCIpLFxuICAgIDU0XHRcdFx0XHRDaGFubmVsUHJlZml4OiAgZ2V0RW52KFwiQUdPUkFfQ0hBTk5FTF9QUkVGSVhcIiwgXCJhaV9zdXBwb3J0X1wiKSxcbiAgICA1NVx0XHRcdH0sXG4gICAgNTZcdFx0XHRBSTogQUlDb25maWd7XG4gICAgNTdcdFx0XHRcdE9wZW5BSUFQSUtleTogICAgZ2V0RW52KFwiT1BFTkFJX0FQSV9LRVlcIiwgXCJcIiksXG4gICAgNThcdFx0XHRcdE9wZW5BSU1vZGVsOiAgICAgZ2V0RW52KFwiT1BFTkFJX01PREVMXCIsIFwiZ3B0LTRcIiksXG4gICAgNTlcdFx0XHRcdFNUVFByb3ZpZGVyOiAgICAgZ2V0RW52KFwiU1RUX1BST1ZJREVSXCIsIFwib3BlbmFpXCIpLFxuICAgIDYwXHRcdFx0XHRUVFNQcm92aWRlcjogICAgIGdldEVudihcIlRUU19QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MVx0XHRcdFx0Vm9pY2VJRDogICAgICAgICBnZXRFbnYoXCJUVFNfVk9JQ0VfSURcIiwgXCJhbGxveVwiKSxcbiAgICA2Mlx0XHRcdFx0RW5hYmxlVkFEOiAgICAgICBnZXRFbnZCb29sKFwiRU5BQkxFX1ZBRFwiLCB0cnVlKSxcbiAgICA2M1x0XHRcdFx0VkFEU2Vuc2l0aXZpdHk6ICBnZXRFbnZGbG9hdChcIlZBRF9TRU5TSVRJVklUWVwiLCAwLjcpLFxuICAgIDY0XHRcdFx0fSxcbiAgICA2NVx0XHR9XG4uLi5cblBhdGg6IHNlcnZpY2VzL2FpX3NlcnZpY2UuZ29cbi4uLlxuICAgMTQ3XHRcdFxuICAgMTQ4XHRcdC8vIEFkZCBjdXJyZW50IHVzZXIgbWVzc2FnZVxuICAgMTQ5XHRcdG1lc3NhZ2VzID0gYXBwZW5kKG1lc3NhZ2VzLCBMTE1NZXNzYWdle1xuICAgMTUwXHRcdFx0Um9sZTogICAgXCJ1c2VyXCIsXG4gICAxNTFcdFx0XHRDb250ZW50OiB1c2VyTWVzc2FnZSxcbiAgIDE1Mlx0XHR9KVxuICAgMTUzXHRcdFxuICAgMTU0XHRcdHJldHVybiBtZXNzYWdlc1xuICAgMTU1XHR9XG4gICAxNTZcdFxuICAgMTU3XHQvLyBjYWxsT3BlbkFJQVBJIG1ha2VzIGFjdHVhbCBBUEkgY2FsbCB0byBPcGVuQUlcbiAgIDE1OFx0ZnVuYyAoYWkgKkFJU2VydmljZSkgY2FsbE9wZW5BSUFQSShtZXNzYWdlcyBbXUxMTU1lc3NhZ2UpIChzdHJpbmcsIGVycm9yKSB7XG4gICAxNTlcdFx0cmVxdWVzdCA6PSBMTE1SZXF1ZXN0e1xuICAgMTYwXHRcdFx0TW9kZWw6ICAgICAgIGFpLmNvbmZpZy5BSS5PcGVuQUlNb2RlbCxcbiAgIDE2MVx0XHRcdE1lc3NhZ2VzOiAgICBtZXNzYWdlcyxcbiAgIDE2Mlx0XHRcdFRlbXBlcmF0dXJlOiAwLjcsXG4gICAxNjNcdFx0XHRNYXhUb2tlbnM6ICAgMTUwLFxuICAgMTY0XHRcdH1cbiAgIDE2NVx0XHRcbiAgIDE2Nlx0XHRqc29uRGF0YSwgZXJyIDo9IGpzb24uTWFyc2hhbChyZXF1ZXN0KVxuICAgMTY3XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTY4XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gbWFyc2hhbCByZXF1ZXN0OiAld1wiLCBlcnIpXG4gICAxNjlcdFx0fVxuICAgMTcwXHRcdFxuICAgMTcxXHRcdHJlcSwgZXJyIDo9IGh0dHAuTmV3UmVxdWVzdChcIlBPU1RcIiwgXCJodHRwczovL2FwaS5vcGVuYWkuY29tL3YxL2NoYXQvY29tcGxldGlvbnNcIiwgYnl0ZXMuTmV3QnVmZmVyKGpzb25EYXRhKSlcbiAgIDE3Mlx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE3M1x0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGNyZWF0ZSByZXF1ZXN0OiAld1wiLCBlcnIpXG4gICAxNzRcdFx0fVxuICAgMTc1XHRcdFxuICAgMTc2XHRcdHJlcS5IZWFkZXIuU2V0KFwiQ29udGVudC1UeXBlXCIsIFwiYXBwbGljYXRpb24vanNvblwiKVxuICAgMTc3XHRcdHJlcS5IZWFkZXIuU2V0KFwiQXV0aG9yaXphdGlvblwiLCBcIkJlYXJlciBcIithaS5jb25maWcuQUkuT3BlbkFJQVBJS2V5KVxuICAgMTc4XHRcdFxuICAgMTc5XHRcdHJlc3AsIGVyciA6PSBhaS5jbGllbnQuRG8ocmVxKVxuICAgMTgwXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTgxXHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gbWFrZSByZXF1ZXN0OiAld1wiLCBlcnIpXG4gICAxODJcdFx0fVxuICAgMTgzXHRcdGRlZmVyIHJlc3AuQm9keS5DbG9zZSgpXG4gICAxODRcdFx0XG4gICAxODVcdFx0Ym9keSwgZXJyIDo9IGlvLlJlYWRBbGwocmVzcC5Cb2R5KVxuICAgMTg2XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTg3XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gcmVhZCByZXNwb25zZTogJXdcIiwgZXJyKVxuICAgMTg4XHRcdH1cbiAgIDE4OVx0XHRcbiAgIDE5MFx0XHR2YXIgbGxtUmVzcG9uc2UgTExNUmVzcG9uc2VcbiAgIDE5MVx0XHRpZiBlcnIgOj0ganNvbi5Vbm1hcnNoYWwoYm9keSwgJmxsbVJlc3BvbnNlKTsgZXJyICE9IG5pbCB7XG4gICAxOTJcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byB1bm1hcnNoYWwgcmVzcG9uc2U6ICV3XCIsIGVycilcbiAgIDE5M1x0XHR9XG4gICAxOTRcdFx0XG4gICAxOTVcdFx0aWYgbGVuKGxsbVJlc3BvbnNlLkNob2ljZXMpID09IDAge1xuICAgMTk2XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJubyByZXNwb25zZSBmcm9tIExMTVwiKVxuICAgMTk3XHRcdH1cbiAgIDE5OFx0XHRcbiAgIDE5OVx0XHRyZXR1cm4gbGxtUmVzcG9uc2UuQ2hvaWNlc1swXS5NZXNzYWdlLkNvbnRlbnQsIG5pbFxuICAgMjAwXHR9XG4uLi5cblBhdGg6IFJFQURNRS5tZFxuLi4uXG4gICAgMzBcdOKUgiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJgg4pSCICAgIOKUgiDilIzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJAg4pSCICAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmFxuICAgIDMxXHTilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJggICAg4pSCIOKUgiBOb3RpZmljYXRpb27ilIIg4pSCICAgIOKUjOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUkFxuICAgIDMyXHQgICAgICAgICAgICAgICAgICAgICAgIOKUgiDilIIgU2VydmljZSAgICAg4pSCIOKUguKXhOKUgOKUgOKWuuKUgiBQdXNoL1NNUy9FbWFpbCAg4pSCXG4gICAgMzNcdCAgICAgICAgICAgICAgICAgICAgICAg4pSCIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmCDilIIgICAg4pSCIFNlcnZpY2VzICAgICAgICDilIJcbiAgICAzNFx0ICAgICAgICAgICAgICAgICAgICAgICDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJggICAg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYXG4gICAgMzVcdGBgYFxuICAgIDM2XHRcbiAgICAzN1x0IyMg8J+TiyBQcmVyZXF1aXNpdGVzXG4gICAgMzhcdFxuICAgIDM5XHQtIEdvIDEuMjIgb3IgaGlnaGVyXG4gICAgNDBcdC0gQWdvcmEuaW8gYWNjb3VudCBhbmQgQXBwIElEXG4gICAgNDFcdC0gT3BlbkFJIEFQSSBrZXkgKG9wdGlvbmFsLCB1c2VzIG1vY2sgYnkgZGVmYXVsdClcbiAgICA0Mlx0XG4gICAgNDNcdCMjIPCfm6DvuI8gU2V0dXBcbiAgICA0NFx0XG4gICAgNDVcdDEuICoqQ2xvbmUgYW5kIHNldHVwKio6XG4gICAgNDZcdCAgIGBgYGJhc2hcbiAgICA0N1x0ICAgZ2l0IGNsb25lIDxyZXBvc2l0b3J5PlxuICAgIDQ4XHQgICBjZCBCYWNrZW5kXG4gICAgNDlcdCAgIGBgYFxuICAgIDUwXHRcbiAgICA1MVx0Mi4gKipJbnN0YWxsIGRlcGVuZGVuY2llcyoqOlxuICAgIDUyXHQgICBgYGBiYXNoXG4gICAgNTNcdCAgIGdvIG1vZCB0aWR5XG4gICAgNTRcdCAgIGBgYFxuICAgIDU1XHRcbiAgICA1Nlx0My4gKipDb25maWd1cmUgZW52aXJvbm1lbnQqKjpcbiAgICA1N1x0ICAgYGBgYmFzaFxuICAgIDU4XHQgICBjcCAuZW52IC5lbnZcbiAgICA1OVx0ICAgIyBFZGl0IC5lbnYgd2l0aCB5b3VyIGNvbmZpZ3VyYXRpb25cbiAgICA2MFx0ICAgYGBgXG4uLi5cbiAgICA4NFx0XG4gICAgODVcdHwgTWV0aG9kIHwgRW5kcG9pbnQgfCBEZXNjcmlwdGlvbiB8XG4gICAgODZcdHwtLS0tLS0tLXwtLS0tLS0tLS0tfC0tLS0tLS0tLS0tLS18XG4gICAgODdcdHwgYFBPU1RgIHwgYC9hcGkvY2FsbHMvaW5pdGlhdGVgIHwg8J+kluKeoe+4j/CfkaQgSW5pdGlhdGUgQUkgY2FsbCB0byBjdXN0b21lciB8XG4gICAgODhcdHwgYFBPU1RgIHwgYC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGVgIHwg8J+RpOKeoe+4j/CfpJYgQ3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwgdG8gQUkgfFxuICAgIDg5XHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzLzpjYWxsX2lkL2pvaW5gIHwgQ3VzdG9tZXIgam9pbnMgYW4gZXhpc3RpbmcgY2FsbCB8XG4gICAgOTBcdHwgYEdFVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZGAgfCBHZXQgY2FsbCBkZXRhaWxzIHxcbiAgICA5MVx0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC9lbmRgIHwgRW5kIGEgY2FsbCB8XG4gICAgOTJcdHwgYEdFVGAgfCBgL2FwaS9jYWxscy9hY3RpdmVgIHwgTGlzdCBhbGwgYWN0aXZlIGNhbGxzIHxcbiAgICA5M1x0fCBgR0VUYCB8IGAvYXBpL2NhbGxzLzpjYWxsX2lkL3N0YXRzYCB8IEdldCBjYWxsIHN0YXRpc3RpY3MgfFxuICAgIDk0XHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzLzpjYWxsX2lkL3Rva2VuYCB8IEdlbmVyYXRlIEFnb3JhIHRva2VuIHxcbiAgICA5NVx0XG4gICAgOTZcdCMjIPCfk5YgVXNhZ2UgRXhhbXBsZXNcbiAgICA5N1x0XG4gICAgOThcdCMjIyAxLiDwn6SW4p6h77iP8J+RpCBJbml0aWF0ZSBBSS10by1DdXN0b21lciBDYWxsXG4uLi5cbiAgIDIzNFx0XG4gICAyMzVcdCMjIPCflKcgQ29uZmlndXJhdGlvblxuICAgMjM2XHRcbiAgIDIzN1x0IyMjIFNlcnZlciBTZXR0aW5nc1xuICAgMjM4XHRgYGBlbnZcbiAgIDIzOVx0U0VSVkVSX1BPUlQ9ODA4MFxuICAgMjQwXHRTRVJWRVJfSE9TVD0wLjAuMC4wXG4gICAyNDFcdGBgYFxuICAgMjQyXHRcbiAgIDI0M1x0IyMjIEFnb3JhIFNldHRpbmdzXG4gICAyNDRcdGBgYGVudlxuICAgMjQ1XHRBR09SQV9BUFBfSUQ9eW91cl9hcHBfaWRcbiAgIDI0Nlx0QUdPUkFfQVBQX0NFUlRJRklDQVRFPXlvdXJfY2VydGlmaWNhdGVcbiAgIDI0N1x0QUdPUkFfQ0hBTk5FTF9QUkVGSVg9YWlfc3VwcG9ydF9cbiAgIDI0OFx0YGBgXG4gICAyNDlcdFxuICAgMjUwXHQjIyMgQUkgU2V0dGluZ3NcbiAgIDI1MVx0YGBgZW52XG4gICAyNTJcdE9QRU5BSV9BUElfS0VZPXlvdXJfa2V5XG4gICAyNTNcdE9QRU5BSV9NT0RFTD1ncHQtNFxuICAgMjU0XHRTVFRfUFJPVklERVI9b3BlbmFpXG4gICAyNTVcdFRUU19QUk9WSURFUj1vcGVuYWlcbiAgIDI1Nlx0VFRTX1ZPSUNFX0lEPWFsbG95XG4gICAyNTdcdEVOQUJMRV9WQUQ9dHJ1ZVxuICAgMjU4XHRWQURfU0VOU0lUSVZJVFk9MC43XG4gICAyNTlcdGBgYFxuICAgMjYwXHRcbiAgIDI2MVx0IyMg8J+nqiBEZXZlbG9wbWVudFxuICAgMjYyXHRcbiAgIDI2M1x0IyMjIE1vY2sgU2VydmljZXNcbiAgIDI2NFx0XG4gICAyNjVcdFRoZSBhcHBsaWNhdGlvbiBpbmNsdWRlcyBtb2NrIGltcGxlbWVudGF0aW9ucyBmb3I6XG4gICAyNjZcdC0gKipOb3RpZmljYXRpb24gU2VydmljZSoqOiBTaW11bGF0ZXMgcHVzaC9TTVMvZW1haWwgbm90aWZpY2F0aW9uc1xuICAgMjY3XHQtICoqQUkgU2VydmljZSoqOiBNb2NrIFNUVCwgTExNLCBhbmQgVFRTIHJlc3BvbnNlc1xuICAgMjY4XHQtICoqQWdvcmEgU2VydmljZSoqOiBNb2NrIHRva2VuIGdlbmVyYXRpb24gYW5kIGNoYW5uZWwgbWFuYWdlbWVudFxuICAgMjY5XHRcbiAgIDI3MFx0IyMjIFRlc3RpbmdcbiAgIDI3MVx0XG4gICAyNzJcdGBgYGJhc2hcbiAgIDI3M1x0IyBUZXN0IGhlYWx0aCBlbmRwb2ludFxuICAgMjc0XHRjdXJsIGh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9oZWFsdGhcbi4uLlxuUGF0aDogdGVzdF9hcGkuc2hcbiAgICAgMVx0IyEvYmluL2Jhc2hcbiAgICAgMlx0XG4gICAgIDNcdCMgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kIEFQSSBUZXN0IFNjcmlwdFxuICAgICA0XHQjIFRoaXMgc2NyaXB0IGRlbW9uc3RyYXRlcyBib3RoIGNhbGwgZmxvd3M6IEFJLXRvLUN1c3RvbWVyIGFuZCBDdXN0b21lci10by1BSVxuICAgICA1XHRcbiAgICAgNlx0QkFTRV9VUkw9XCJodHRwOi8vbG9jYWxob3N0OjgwODBcIlxuICAgICA3XHRcbiAgICAgOFx0ZWNobyBcIvCfpJYgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kIC0gQVBJIFRlc3RcIlxuICAgICA5XHRlY2hvIFwiPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XCJcbiAgICAxMFx0ZWNobyBcIlRlc3RpbmcgYm90aCBjYWxsIGZsb3dzOlwiXG4gICAgMTFcdGVjaG8gXCIxLiBBSS10by1DdXN0b21lciAoQUkgaW5pdGlhdGVzIGNhbGwpXCJcbiAgICAxMlx0ZWNobyBcIjIuIEN1c3RvbWVyLXRvLUFJIChDdXN0b21lciBpbml0aWF0ZXMgY2FsbClcIlxuICAgIDEzXHRlY2hvIFwiXCJcbiAgICAxNFx0XG4gICAgMTVcdCMgVGVzdCAxOiBIZWFsdGggQ2hlY2tcbiAgICAxNlx0ZWNobyBcIjEuIFRlc3RpbmcgaGVhbHRoIGVuZHBvaW50Li4uXCJcbiAgICAxN1x0Y3VybCAtcyBcIiRCQVNFX1VSTC9oZWFsdGhcIiB8IGpxICcuJ1xuICAgIDE4XHRcbiAgICAxOVx0IyBUZXN0IDI6IC4uLiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUJKMnhrYlJ5dDNaUkh2SkFxcDNjOTkifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmQiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwNzoxNzo0Ni41MDJaIiwicmVzcG9uc2VfdGV4dCI6Ikl0IHNlZW1zIHRoZSBGbHV0dGVyIGFwcCBmaWxlcyBhcmUgbm90IGluIHRoaXMgYmFja2VuZCByZXBvc2l0b3J5LiBMZXQgbWUgY3JlYXRlIGEgZGV0YWlsZWQgcGxhbiB0byB1cGRhdGUgdGhlIHVzZXJJRCBmcm9tIHN0cmluZyB0byBpbnQgaW4gdGhlIGJhY2tlbmQuIFRoZSBpc3N1ZSBpcyBsaWtlbHkgdGhhdCB0aGUgY3VycmVudCBpbXBsZW1lbnRhdGlvbiB1c2VzIHN0cmluZyB1c2VySURzIGJ1dCBBZ29yYSBleHBlY3RzIG51bWVyaWMgVUlEcywgYW5kIHRoZSBjb252ZXJzaW9uIGxvZ2ljIG1pZ2h0IG5vdCBiZSB3b3JraW5nIGNvcnJlY3RseS5cblxuIyMgUGxhbiB0byBVcGRhdGUgVXNlcklEIGZyb20gU3RyaW5nIHRvIEludFxuXG5CYXNlZCBvbiBteSBhbmFseXNpcywgaGVyZSdzIHdoYXQgbmVlZHMgdG8gYmUgY2hhbmdlZCBpbiB0aGUgYmFja2VuZDpcblxuIyMjICoqRmlsZXMgdG8gbW9kaWZ5OioqXG4xLiAqKmBzZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvYCoqIC0gVXBkYXRlIHRva2VuIGdlbmVyYXRpb24gdG8gdXNlIGludCB1c2VySURcbjIuICoqYGhhbmRsZXJzL2NhbGxfaGFuZGxlci5nb2AqKiAtIFVwZGF0ZSBBUEkgaGFuZGxlcnMgdG8gdXNlIGludCB1c2VySUQgIFxuMy4gKipgbW9kZWxzL2NhbGwuZ29gKiogLSBVcGRhdGUgQ2FsbCBtb2RlbCB0byB1c2UgaW50IGN1c3RvbWVySURcbjQuICoqYHNlcnZpY2VzL2NhbGxfbWFuYWdlci5nb2AqKiAtIFVwZGF0ZSBjYWxsIG1hbmFnZW1lbnQgdG8gdXNlIGludCBjdXN0b21lcklEXG5cbiMjIyAqKktleSBJc3N1ZXMgSWRlbnRpZmllZDoqKlxuMS4gVGhlIGN1cnJlbnQgY29kZSB1c2VzIHN0cmluZyB1c2VySURzIGJ1dCBoYXMgaW5jb25zaXN0ZW50IGNvbnZlcnNpb24gdG8gdWludDMyIGZvciBBZ29yYVxuMi4gSGFyZC1jb2RlZCBzdHJpbmcgdmFsdWVzIGxpa2UgXCJhaV9hZ2VudFwiIGFuZCBcInRlc3RfY3VzdG9tZXJfMTIzXCIgYXJlIGJlaW5nIHVzZWRcbjMuIFRoZSBjb252ZXJzaW9uIGxvZ2ljIGluIGBnZW5lcmF0ZVJlYWxUb2tlbmAgdXNlcyBhIGZhbGxiYWNrIHRoYXQgbWlnaHQgbm90IGJlIHJlbGlhYmxlXG5cbkxldCBtZSBpbXBsZW1lbnQgdGhlc2UgY2hhbmdlczpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoiZmFpbGVkIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOltdLCJzZWVuX3N0YXRlIjoidW5zZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiU3VjY2Vzc2Z1bGx5IGVkaXRlZCB0aGUgZmlsZSBzZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvLlxuUmVzdWx0IGZvciBzdHJfcmVwbGFjZSBmb3IgZW50cnkgd2l0aCBpbmRleCBbMV06XG5SZXBsYWNlbWVudCBzdWNjZXNzZnVsLlxuRWRpdGVkIHNlY3Rpb24gYWZ0ZXIgSURFIGF1dG8tZm9ybWF0dGluZyB3YXMgYXBwbGllZDpcbiAgICAxOVx0XHQvLyAtIEFjdGl2ZSBjb25uZWN0aW9ucyBtYXBcbiAgICAyMFx0XHQvLyAtIEF1ZGlvL1ZpZGVvIG9ic2VydmVyc1xuICAgIDIxXHR9XG4gICAgMjJcdFxuICAgIDIzXHQvLyBUb2tlblJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgZm9yIEFnb3JhIHRva2VuXG4gICAgMjRcdHR5cGUgVG9rZW5SZXF1ZXN0IHN0cnVjdCB7XG4gICAgMjVcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMjZcdFx0VXNlcklEICAgICAgaW50ICAgIGBqc29uOlwidXNlcl9pZFwiYFxuICAgIDI3XHRcdFJvbGUgICAgICAgIGludCAgICBganNvbjpcInJvbGVcImAgLy8gMSA9IHB1Ymxpc2hlciwgMiA9IHN1YnNjcmliZXJcbiAgICAyOFx0fVxuICAgIDI5XHRcbiAgICAzMFx0Ly8gVG9rZW5SZXNwb25zZSByZXByZXNlbnRzIEFnb3JhIHRva2VuIHJlc3BvbnNlXG4gICAgMzFcdHR5cGUgVG9rZW5SZXNwb25zZSBzdHJ1Y3Qge1xuICAgIDMyXHRcdFRva2VuICAgICBzdHJpbmcgYGpzb246XCJ0b2tlblwiYFxuXG5SZXZpZXcgdGhlIGNoYW5nZXMgYW5kIG1ha2Ugc3VyZSB0aGV5IGFyZSBhcyBleHBlY3RlZCAoY29ycmVjdCBpbmRlbnRhdGlvbiwgbm8gZHVwbGljYXRlIGxpbmVzLCBldGMpLlxuRWRpdCB0aGUgZmlsZSBhZ2FpbiBpZiBuZWNlc3NhcnkuXG4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFFZzZQelRGRGYya1FHNWlWcHE5Mm5wIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kIiwiZm9sZGVyUm9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUvQmFja2VuZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2V9fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMjZUMDc6MTg6MTguMjU3WiJ9LHsicmVxdWVzdF9pZCI6IjJmZWU0OGM1LWQ0NjItNDQ0Ni1hNTIyLTQyNDdhMDlhYjZkMiIsInV1aWQiOiIwNThkNDZmZS05NGZlLTQ4Y2YtYWExYS04YzNmMTFjYjA1YjQiLCJjaGF0SXRlbVR5cGUiOiJhZ2VudGljLWNoZWNrcG9pbnQtZGVsaW1pdGVyIiwic3RhdHVzIjoic3VjY2VzcyIsImZyb21UaW1lc3RhbXAiOjE3NDgyNDIxOTk3NjYsInRvVGltZXN0YW1wIjoxNzQ4MjQzOTU4MzIwfV0sImZlZWRiYWNrU3RhdGVzIjp7InRlbXAtZmUtMDA3N2U1NzAtM2UzYy00NzM3LWE1ZmItMTY0OTAyNmY5YjEzIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1hOTA1MzRkNS0zODg1LTQwZTMtODdmMS1iN2QxZDgyODliMjYiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWIwNzcwMWQxLTlmMmMtNDk1Yi1hZmE2LWUyM2Y1YzI4ZTc4NCI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMWY1NTE3MzMtYzhiYi00ZmM4LWIxMzctN2ExNGExMTRiMDUxIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS00ZWY3YmEyMi0wZjBiLTQ0N2ItYTA3Yy04YjFlOTgwYTBkMDYiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTU1YTQxZGI4LTA4YWEtNDJkMi1iMGZiLTZlYzRkYWFlNGVmNiI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMTBjZmM2N2QtMGQ0Ni00YmM4LTllODYtMzE5YzI0NjliMmFhIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1kOTBjY2I5Zi1kYzE2LTQ1MTItYjM0Ny05NDVlNWI0NDlmMzMiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWE2YzEwYTU2LTBkYjAtNGFhYy1hYmI3LWM3NjU2NGMzZGRkZiI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMzE2OTFiZGQtODdlYS00NTEwLWFkOWUtZTgwNjA3ZWNlZTRhIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS05MjdmNzA3NC0yMjNiLTRkM2EtYWU2NC0xNThhNzI4NjQ1ODgiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTg0OGRkY2U3LTA3Y2YtNDhmMS1iZGJjLWU1OTQwMDc5OGY0YiI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtYmVkNzQ4YWYtZWZjYS00YzE4LWI1NjAtZWIxODNlYjU5NmI3Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS04MDEwODg0NS02OTI0LTQ3YzktOWRiMS04YTg3YzUyMWJmMDMiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTM3YWY5YTAyLWYyYzctNDcwNS1iMWI3LTI2YmM5NTJhMGJkMCI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn19LCJ0b29sVXNlU3RhdGVzIjp7IjBiMGVkNTJhLTgyYTYtNDg1My04MjQ4LWM2Yjc2Y2JmZDdjZjt0b29sdV92cnR4XzAxVTROa2FBOFNGWWdDemtja1pNOEhDRyI6eyJyZXF1ZXN0SWQiOiIwYjBlZDUyYS04MmE2LTQ4NTMtODI0OC1jNmI3NmNiZmQ3Y2YiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxVTROa2FBOFNGWWdDemtja1pNOEhDRyIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IkhlcmUncyB0aGUgcmVzdWx0IG9mIHJ1bm5pbmcgYGNhdCAtbmAgb24gc2VydmljZXMvYWdvcmFfc2VydmljZS5nbzpcbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcImZtdFwiXG4gICAgIDZcdFx0XCJsb2dcIlxuICAgICA3XHRcdFwic3RyY29udlwiXG4gICAgIDhcdFx0XCJ0aW1lXCJcbiAgICAgOVx0XG4gICAgMTBcdFx0XCJnaXRodWIuY29tL0Fnb3JhSU8vVG9vbHMvRHluYW1pY0tleS9BZ29yYUR5bmFtaWNLZXkvZ28vc3JjL3J0Y3Rva2VuYnVpbGRlcjJcIlxuICAgIDExXHQpXG4gICAgMTJcdFxuICAgIDEzXHQvLyBBZ29yYVNlcnZpY2UgaGFuZGxlcyBBZ29yYSBSVEMgb3BlcmF0aW9uc1xuICAgIDE0XHR0eXBlIEFnb3JhU2VydmljZSBzdHJ1Y3Qge1xuICAgIDE1XHRcdGNvbmZpZyAqY29uZmlnLkNvbmZpZ1xuICAgIDE2XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY29udGFpbjpcbiAgICAxN1x0XHQvLyAtIEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAxOFx0XHQvLyAtIE1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDE5XHRcdC8vIC0gQWN0aXZlIGNvbm5lY3Rpb25zIG1hcFxuICAgIDIwXHRcdC8vIC0gQXVkaW8vVmlkZW8gb2JzZXJ2ZXJzXG4gICAgMjFcdH1cbiAgICAyMlx0XG4gICAgMjNcdC8vIFRva2VuUmVxdWVzdCByZXByZXNlbnRzIGEgcmVxdWVzdCBmb3IgQWdvcmEgdG9rZW5cbiAgICAyNFx0dHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcbiAgICAyNVx0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICAyNlx0XHRVc2VySUQgICAgICBzdHJpbmcgYGpzb246XCJ1c2VyX2lkXCJgXG4gICAgMjdcdFx0Um9sZSAgICAgICAgaW50ICAgIGBqc29uOlwicm9sZVwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxuICAgIDI4XHR9XG4gICAgMjlcdFxuICAgIDMwXHQvLyBUb2tlblJlc3BvbnNlIHJlcHJlc2VudHMgQWdvcmEgdG9rZW4gcmVzcG9uc2VcbiAgICAzMVx0dHlwZSBUb2tlblJlc3BvbnNlIHN0cnVjdCB7XG4gICAgMzJcdFx0VG9rZW4gICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAgMzNcdFx0RXhwaXJlc0F0IGludDY0ICBganNvbjpcImV4cGlyZXNfYXRcImBcbiAgICAzNFx0fVxuICAgIDM1XHRcbiAgICAzNlx0Ly8gTmV3QWdvcmFTZXJ2aWNlIGNyZWF0ZXMgYSBuZXcgQWdvcmEgc2VydmljZSBpbnN0YW5jZVxuICAgIDM3XHRmdW5jIE5ld0Fnb3JhU2VydmljZShjZmcgKmNvbmZpZy5Db25maWcpICpBZ29yYVNlcnZpY2Uge1xuICAgIDM4XHRcdHNlcnZpY2UgOj0gJkFnb3JhU2VydmljZXtcbiAgICAzOVx0XHRcdGNvbmZpZzogY2ZnLFxuICAgIDQwXHRcdH1cbiAgICA0MVx0XG4gICAgNDJcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgaW5pdGlhbGl6ZSBBZ29yYSBTREsgaGVyZTpcbiAgICA0M1x0XHQvLyAxLiBJbml0aWFsaXplIEFnb3JhIHNlcnZpY2Ugd2l0aCBhcHAgSURcbiAgICA0NFx0XHQvLyAyLiBDcmVhdGUgbWVkaWEgbm9kZSBmYWN0b3J5XG4gICAgNDVcdFx0Ly8gMy4gU2V0IHVwIGF1ZGlvL3ZpZGVvIHByb2Nlc3NpbmcgY2FsbGJhY2tzXG4gICAgNDZcdFxuICAgIDQ3XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIHNlcnZpY2UgaW5pdGlhbGl6ZWQgd2l0aCBBcHAgSUQ6ICVzXCIsIGNmZy5BZ29yYS5BcHBJRClcbiAgICA0OFx0XHRyZXR1cm4gc2VydmljZVxuICAgIDQ5XHR9XG4gICAgNTBcdFxuICAgIDUxXHQvLyBHZW5lcmF0ZVRva2VuIGdlbmVyYXRlcyBhbiBBZ29yYSBSVEMgdG9rZW4gZm9yIGNoYW5uZWwgYWNjZXNzXG4gICAgNTJcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEdlbmVyYXRlVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50KSAoKlRva2VuUmVzcG9uc2UsIGVycm9yKSB7XG4gICAgNTNcdFx0bG9nLlByaW50ZihcIvCflJEgR2VuZXJhdGluZyBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXMsIHVzZXI6ICVzLCByb2xlOiAlZFwiLCBjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDU0XHRcbiAgICA1NVx0XHQvLyBUb2tlbiBleHBpcmVzIGluIDI0IGhvdXJzXG4gICAgNTZcdFx0ZXhwaXJhdGlvblRpbWUgOj0gdGltZS5Ob3coKS5BZGQoMjQgKiB0aW1lLkhvdXIpLlVuaXgoKVxuICAgIDU3XHRcbiAgICA1OFx0XHR2YXIgdG9rZW4gc3RyaW5nXG4gICAgNTlcdFx0dmFyIGVyciBlcnJvclxuICAgIDYwXHRcbiAgICA2MVx0XHRpZiBhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUgIT0gXCJcIiB7XG4gICAgNjJcdFx0XHQvLyBHZW5lcmF0ZSByZWFsIHRva2VuIHdpdGggb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDYzXHRcdFx0dG9rZW4sIGVyciA9IGFzLmdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHVpbnQzMihleHBpcmF0aW9uVGltZSkpXG4gICAgNjRcdFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2NVx0XHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlbjogJXdcIiwgZXJyKVxuICAgIDY2XHRcdFx0fVxuICAgIDY3XHRcdFx0bG9nLlByaW50ZihcIuKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgNjhcdFx0fSBlbHNlIHtcbiAgICA2OVx0XHRcdC8vIEdlbmVyYXRlIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAgNzBcdFx0XHR0b2tlbiA9IGFzLmdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNzFcdFx0XHRsb2cuUHJpbnRmKFwi4pqg77iPIFtNT0NLXSBVc2luZyBtb2NrIHRva2VuIChubyBhcHAgY2VydGlmaWNhdGUgcHJvdmlkZWQpXCIpXG4gICAgNzJcdFx0fVxuICAgIDczXHRcbiAgICA3NFx0XHRyZXNwb25zZSA6PSAmVG9rZW5SZXNwb25zZXtcbiAgICA3NVx0XHRcdFRva2VuOiAgICAgdG9rZW4sXG4gICAgNzZcdFx0XHRFeHBpcmVzQXQ6IGV4cGlyYXRpb25UaW1lLFxuICAgIDc3XHRcdH1cbiAgICA3OFx0XG4gICAgNzlcdFx0bG9nLlByaW50ZihcIuKchSBUb2tlbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5IGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgICA4MFx0XHRyZXR1cm4gcmVzcG9uc2UsIG5pbFxuICAgIDgxXHR9XG4gICAgODJcdFxuICAgIDgzXHQvLyBnZW5lcmF0ZVJlYWxUb2tlbiBnZW5lcmF0ZXMgYSByZWFsIEFnb3JhIHRva2VuIHVzaW5nIHRoZSBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAgODRcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCwgZXhwaXJhdGlvbiB1aW50MzIpIChzdHJpbmcsIGVycm9yKSB7XG4gICAgODVcdFx0Ly8gQ29udmVydCB1c2VySUQgdG8gdWludDMyIChBZ29yYSBleHBlY3RzIG51bWVyaWMgVUlEKVxuICAgIDg2XHRcdHZhciB1aWQgdWludDMyXG4gICAgODdcdFx0aWYgdXNlcklEICE9IFwiXCIge1xuICAgIDg4XHRcdFx0aWYgcGFyc2VkVUlELCBlcnIgOj0gc3RyY29udi5QYXJzZVVpbnQodXNlcklELCAxMCwgMzIpOyBlcnIgPT0gbmlsIHtcbiAgICA4OVx0XHRcdFx0dWlkID0gdWludDMyKHBhcnNlZFVJRClcbiAgICA5MFx0XHRcdH0gZWxzZSB7XG4gICAgOTFcdFx0XHRcdC8vIElmIHVzZXJJRCBpcyBub3QgbnVtZXJpYywgZ2VuZXJhdGUgYSBoYXNoLWJhc2VkIFVJRFxuICAgIDkyXHRcdFx0XHR1aWQgPSB1aW50MzIobGVuKHVzZXJJRCkpIC8vIFNpbXBsZSBmYWxsYmFjaywgaW4gcHJvZHVjdGlvbiB1c2UgYSBwcm9wZXIgaGFzaFxuICAgIDkzXHRcdFx0fVxuICAgIDk0XHRcdH0gZWxzZSB7XG4gICAgOTVcdFx0XHR1aWQgPSAwIC8vIExldCBBZ29yYSBhc3NpZ24gYSBVSURcbiAgICA5Nlx0XHR9XG4gICAgOTdcdFxuICAgIDk4XHRcdC8vIENvbnZlcnQgcm9sZSB0byBBZ29yYSByb2xlIHR5cGVcbiAgICA5OVx0XHR2YXIgYWdvcmFSb2xlIHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVxuICAgMTAwXHRcdGlmIHJvbGUgPT0gMSB7XG4gICAxMDFcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVQdWJsaXNoZXJcbiAgIDEwMlx0XHR9IGVsc2Uge1xuICAgMTAzXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlU3Vic2NyaWJlclxuICAgMTA0XHRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdFx0Ly8gR2VuZXJhdGUgdG9rZW4gdXNpbmcgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgMTA3XHRcdHRva2VuLCBlcnIgOj0gcnRjdG9rZW5idWlsZGVyMi5CdWlsZFRva2VuV2l0aFVpZChcbiAgIDEwOFx0XHRcdGFzLmNvbmZpZy5BZ29yYS5BcHBJRCxcbiAgIDEwOVx0XHRcdGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSxcbiAgIDExMFx0XHRcdGNoYW5uZWxOYW1lLFxuICAgMTExXHRcdFx0dWlkLFxuICAgMTEyXHRcdFx0YWdvcmFSb2xlLFxuICAgMTEzXHRcdFx0ZXhwaXJhdGlvbixcbiAgIDExNFx0XHRcdGV4cGlyYXRpb24sIC8vIFByaXZpbGVnZSBleHBpcmVkIHRpbWVzdGFtcCAoc2FtZSBhcyB0b2tlbiBleHBpcmF0aW9uKVxuICAgMTE1XHRcdClcbiAgIDExNlx0XG4gICAxMTdcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxMThcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byBidWlsZCBBZ29yYSB0b2tlbjogJXdcIiwgZXJyKVxuICAgMTE5XHRcdH1cbiAgIDEyMFx0XG4gICAxMjFcdFx0cmV0dXJuIHRva2VuLCBuaWxcbiAgIDEyMlx0fVxuICAgMTIzXHRcbiAgIDEyNFx0Ly8gZ2VuZXJhdGVNb2NrVG9rZW4gZ2VuZXJhdGVzIGEgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgIDEyNVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2VuZXJhdGVNb2NrVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50KSBzdHJpbmcge1xuICAgMTI2XHRcdHRpbWVzdGFtcCA6PSB0aW1lLk5vdygpLlVuaXgoKVxuICAgMTI3XHRcdHJldHVybiBmbXQuU3ByaW50ZihcIm1vY2tfdG9rZW5fJXNfJXNfJWRfJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSwgdGltZXN0YW1wKVxuICAgMTI4XHR9XG4gICAxMjlcdFxuICAgMTMwXHQvLyBKb2luQ2hhbm5lbCBqb2lucyBhbiBBZ29yYSBjaGFubmVsIGZvciBBSSBwcm9jZXNzaW5nXG4gICAxMzFcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEpvaW5DaGFubmVsKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nKSBlcnJvciB7XG4gICAxMzJcdFx0bG9nLlByaW50ZihcIvCflJcgW01PQ0tdIEFJIGpvaW5pbmcgQWdvcmEgY2hhbm5lbDogJXMgYXMgdXNlcjogJXNcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRClcbiAgIDEzM1x0XG4gICAxMzRcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDEzNVx0XHQvLyAxLiBDcmVhdGUgUlRDIGNvbm5lY3Rpb25cbiAgIDEzNlx0XHQvLyAyLiBTZXQgdXAgbG9jYWwgdXNlclxuICAgMTM3XHRcdC8vIDMuIENyZWF0ZSBhdWRpbyB0cmFjayBmb3IgQUkgdm9pY2VcbiAgIDEzOFx0XHQvLyA0LiBSZWdpc3RlciBhdWRpbyBmcmFtZSBvYnNlcnZlcnMgZm9yIHByb2Nlc3NpbmcgaW5jb21pbmcgYXVkaW9cbiAgIDEzOVx0XHQvLyA1LiBKb2luIHRoZSBjaGFubmVsXG4gICAxNDBcdFx0Ly8gNi4gU3RhcnQgYXVkaW8gcHJvY2Vzc2luZyBwaXBlbGluZVxuICAgMTQxXHRcbiAgIDE0Mlx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uXG4gICAxNDNcdFx0dGltZS5TbGVlcCg1MDAgKiB0aW1lLk1pbGxpc2Vjb25kKSAvLyBTaW11bGF0ZSBjb25uZWN0aW9uIHRpbWVcbiAgIDE0NFx0XG4gICAxNDVcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQUkgc3VjY2Vzc2Z1bGx5IGpvaW5lZCBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE0Nlx0XHRyZXR1cm4gbmlsXG4gICAxNDdcdH1cbiAgIDE0OFx0XG4gICAxNDlcdC8vIExlYXZlQ2hhbm5lbCBsZWF2ZXMgYW4gQWdvcmEgY2hhbm5lbFxuICAgMTUwXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBMZWF2ZUNoYW5uZWwoY2FsbElEIHN0cmluZykgZXJyb3Ige1xuICAgMTUxXHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDE1Mlx0XHRsb2cuUHJpbnRmKFwi8J+RiyBbTU9DS10gQUkgbGVhdmluZyBBZ29yYSBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE1M1x0XG4gICAxNTRcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDE1NVx0XHQvLyAxLiBTdG9wIGF1ZGlvIHByb2Nlc3NpbmdcbiAgIDE1Nlx0XHQvLyAyLiBVbnB1Ymxpc2ggYXVkaW8gdHJhY2tzXG4gICAxNTdcdFx0Ly8gMy4gRGlzY29ubmVjdCBmcm9tIGNoYW5uZWxcbiAgIDE1OFx0XHQvLyA0LiBDbGVhbiB1cCByZXNvdXJjZXNcbiAgIDE1OVx0XG4gICAxNjBcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvblxuICAgMTYxXHRcdHRpbWUuU2xlZXAoMjAwICogdGltZS5NaWxsaXNlY29uZCkgLy8gU2ltdWxhdGUgZGlzY29ubmVjdGlvbiB0aW1lXG4gICAxNjJcdFxuICAgMTYzXHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEFJIHN1Y2Nlc3NmdWxseSBsZWZ0IGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTY0XHRcdHJldHVybiBuaWxcbiAgIDE2NVx0fVxuICAgMTY2XHRcbiAgIDE2N1x0Ly8gU2VuZEF1ZGlvVG9DaGFubmVsIHNlbmRzIEFJLWdlbmVyYXRlZCBhdWRpbyB0byB0aGUgY2hhbm5lbFxuICAgMTY4XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBTZW5kQXVkaW9Ub0NoYW5uZWwoY2FsbElEIHN0cmluZywgYXVkaW9EYXRhIFtdYnl0ZSkgZXJyb3Ige1xuICAgMTY5XHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDE3MFx0XHRsb2cuUHJpbnRmKFwi8J+UiiBbTU9DS10gU2VuZGluZyAlZCBieXRlcyBvZiBhdWRpbyB0byBjaGFubmVsOiAlc1wiLCBsZW4oYXVkaW9EYXRhKSwgY2hhbm5lbE5hbWUpXG4gICAxNzFcdFxuICAgMTcyXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxNzNcdFx0Ly8gMS4gQ29udmVydCBhdWRpbyBkYXRhIHRvIHJlcXVpcmVkIGZvcm1hdCAoUENNKVxuICAgMTc0XHRcdC8vIDIuIFB1c2ggYXVkaW8gZnJhbWVzIHRvIEFnb3JhIGF1ZGlvIHRyYWNrXG4gICAxNzVcdFx0Ly8gMy4gSGFuZGxlIGF1ZGlvIGVuY29kaW5nIGFuZCB0cmFuc21pc3Npb25cbiAgIDE3Nlx0XG4gICAxNzdcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvbiAtIHNpbXVsYXRlIGF1ZGlvIHRyYW5zbWlzc2lvbiB0aW1lXG4gICAxNzhcdFx0dHJhbnNtaXNzaW9uVGltZSA6PSB0aW1lLkR1cmF0aW9uKGxlbihhdWRpb0RhdGEpLzEwMDApICogdGltZS5NaWxsaXNlY29uZFxuICAgMTc5XHRcdGlmIHRyYW5zbWlzc2lvblRpbWUgPiAxMDAqdGltZS5NaWxsaXNlY29uZCB7XG4gICAxODBcdFx0XHR0cmFuc21pc3Npb25UaW1lID0gMTAwICogdGltZS5NaWxsaXNlY29uZFxuICAgMTgxXHRcdH1cbiAgIDE4Mlx0XHR0aW1lLlNsZWVwKHRyYW5zbWlzc2lvblRpbWUpXG4gICAxODNcdFxuICAgMTg0XHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEF1ZGlvIHNlbnQgc3VjY2Vzc2Z1bGx5IHRvIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTg1XHRcdHJldHVybiBuaWxcbiAgIDE4Nlx0fVxuICAgMTg3XHRcbiAgIDE4OFx0Ly8gU2V0dXBBdWRpb1Byb2Nlc3Npbmcgc2V0cyB1cCBhdWRpbyBwcm9jZXNzaW5nIGNhbGxiYWNrc1xuICAgMTg5XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBTZXR1cEF1ZGlvUHJvY2Vzc2luZyhjYWxsSUQgc3RyaW5nLCBhdWRpb0NhbGxiYWNrIGZ1bmMoW11ieXRlKSBlcnJvcikgZXJyb3Ige1xuICAgMTkwXHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDE5MVx0XHRsb2cuUHJpbnRmKFwi8J+Ome+4jyBbTU9DS10gU2V0dGluZyB1cCBhdWRpbyBwcm9jZXNzaW5nIGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE5Mlx0XG4gICAxOTNcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDE5NFx0XHQvLyAxLiBSZWdpc3RlciBhdWRpbyBmcmFtZSBvYnNlcnZlclxuICAgMTk1XHRcdC8vIDIuIFNldCB1cCBWQUQgKFZvaWNlIEFjdGl2aXR5IERldGVjdGlvbilcbiAgIDE5Nlx0XHQvLyAzLiBDb25maWd1cmUgYXVkaW8gZm9ybWF0IChzYW1wbGUgcmF0ZSwgY2hhbm5lbHMsIGV0Yy4pXG4gICAxOTdcdFx0Ly8gNC4gU3RhcnQgcHJvY2Vzc2luZyBpbmNvbWluZyBhdWRpbyBmcmFtZXNcbiAgIDE5OFx0XG4gICAxOTlcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvbiAtIHNpbXVsYXRlIHJlY2VpdmluZyBhdWRpbyBwZXJpb2RpY2FsbHlcbiAgIDIwMFx0XHRnbyBhcy5zaW11bGF0ZUluY29taW5nQXVkaW8oY2FsbElELCBhdWRpb0NhbGxiYWNrKVxuICAgMjAxXHRcbiAgIDIwMlx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBdWRpbyBwcm9jZXNzaW5nIHNldHVwIGNvbXBsZXRlIGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDIwM1x0XHRyZXR1cm4gbmlsXG4gICAyMDRcdH1cbiAgIDIwNVx0XG4gICAyMDZcdC8vIHNpbXVsYXRlSW5jb21pbmdBdWRpbyBzaW11bGF0ZXMgcmVjZWl2aW5nIGF1ZGlvIGZyb20gdGhlIGN1c3RvbWVyXG4gICAyMDdcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIHNpbXVsYXRlSW5jb21pbmdBdWRpbyhjYWxsSUQgc3RyaW5nLCBhdWRpb0NhbGxiYWNrIGZ1bmMoW11ieXRlKSBlcnJvcikge1xuICAgMjA4XHRcdGxvZy5QcmludGYoXCLwn46tIFtNT0NLXSBTdGFydGluZyBhdWRpbyBzaW11bGF0aW9uIGZvciBjYWxsOiAlc1wiLCBjYWxsSUQpXG4gICAyMDlcdFxuICAgMjEwXHRcdC8vIFNpbXVsYXRlIGN1c3RvbWVyIHNwZWFraW5nIGV2ZXJ5IDUtMTAgc2Vjb25kc1xuICAgMjExXHRcdGZvciBpIDo9IDA7IGkgPCA1OyBpKysgeyAvLyBTaW11bGF0ZSA1IGN1c3RvbWVyIG1lc3NhZ2VzXG4gICAyMTJcdFx0XHR0aW1lLlNsZWVwKHRpbWUuRHVyYXRpb24oNStpKjIpICogdGltZS5TZWNvbmQpXG4gICAyMTNcdFxuICAgMjE0XHRcdFx0Ly8gR2VuZXJhdGUgbW9jayBhdWRpbyBkYXRhXG4gICAyMTVcdFx0XHRtb2NrQXVkaW9EYXRhIDo9IG1ha2UoW11ieXRlLCAxNjAwKSAvLyBTaW11bGF0ZSAxMDBtcyBvZiAxNmtIeiBtb25vIGF1ZGlvXG4gICAyMTZcdFx0XHRmb3IgaiA6PSByYW5nZSBtb2NrQXVkaW9EYXRhIHtcbiAgIDIxN1x0XHRcdFx0bW9ja0F1ZGlvRGF0YVtqXSA9IGJ5dGUoKGkqMTAwICsgaikgJSAyNTYpXG4gICAyMThcdFx0XHR9XG4gICAyMTlcdFxuICAgMjIwXHRcdFx0bG9nLlByaW50ZihcIvCfjqQgW01PQ0tdIFNpbXVsYXRpbmcgaW5jb21pbmcgYXVkaW8gZm9yIGNhbGwgJXMgKG1lc3NhZ2UgJWQpXCIsIGNhbGxJRCwgaSsxKVxuICAgMjIxXHRcbiAgIDIyMlx0XHRcdGlmIGVyciA6PSBhdWRpb0NhbGxiYWNrKG1vY2tBdWRpb0RhdGEpOyBlcnIgIT0gbmlsIHtcbiAgIDIyM1x0XHRcdFx0bG9nLlByaW50ZihcIkVycm9yIHByb2Nlc3Npbmcgc2ltdWxhdGVkIGF1ZGlvOiAldlwiLCBlcnIpXG4gICAyMjRcdFx0XHRcdGJyZWFrXG4gICAyMjVcdFx0XHR9XG4gICAyMjZcdFx0fVxuICAgMjI3XHRcbiAgIDIyOFx0XHRsb2cuUHJpbnRmKFwi8J+OrSBbTU9DS10gQXVkaW8gc2ltdWxhdGlvbiBjb21wbGV0ZWQgZm9yIGNhbGw6ICVzXCIsIGNhbGxJRClcbiAgIDIyOVx0fVxuICAgMjMwXHRcbiAgIDIzMVx0Ly8gR2V0Q2hhbm5lbFN0YXRzIHJldHVybnMgY2hhbm5lbCBzdGF0aXN0aWNzXG4gICAyMzJcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEdldENoYW5uZWxTdGF0cyhjYWxsSUQgc3RyaW5nKSAobWFwW3N0cmluZ11pbnRlcmZhY2V7fSwgZXJyb3IpIHtcbiAgIDIzM1x0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAyMzRcdFx0bG9nLlByaW50ZihcIvCfk4ogW01PQ0tdIEdldHRpbmcgY2hhbm5lbCBzdGF0cyBmb3I6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMjM1XHRcbiAgIDIzNlx0XHQvLyBNb2NrIHN0YXRpc3RpY3NcbiAgIDIzN1x0XHRzdGF0cyA6PSBtYXBbc3RyaW5nXWludGVyZmFjZXt9e1xuICAgMjM4XHRcdFx0XCJjaGFubmVsX25hbWVcIjogICAgIGNoYW5uZWxOYW1lLFxuICAgMjM5XHRcdFx0XCJjb25uZWN0ZWRfdXNlcnNcIjogIDIsIC8vIEFJICsgQ3VzdG9tZXJcbiAgIDI0MFx0XHRcdFwiYXVkaW9fYml0cmF0ZVwiOiAgICA2NDAwMCxcbiAgIDI0MVx0XHRcdFwicGFja2V0X2xvc3NfcmF0ZVwiOiAwLjAxLFxuICAgMjQyXHRcdFx0XCJydHRcIjogICAgICAgICAgICAgNDUsIC8vIFJvdW5kIHRyaXAgdGltZSBpbiBtc1xuICAgMjQzXHRcdFx0XCJhdWRpb19xdWFsaXR5XCI6ICAgIFwiZ29vZFwiLFxuICAgMjQ0XHRcdFx0XCJjb25uZWN0aW9uX3N0YXRlXCI6IFwiY29ubmVjdGVkXCIsXG4gICAyNDVcdFx0fVxuICAgMjQ2XHRcbiAgIDI0N1x0XHRyZXR1cm4gc3RhdHMsIG5pbFxuICAgMjQ4XHR9XG4gICAyNDlcdFxuICAgMjUwXHQvLyBnZXRDaGFubmVsTmFtZSBnZW5lcmF0ZXMgY2hhbm5lbCBuYW1lIGZyb20gY2FsbCBJRFxuICAgMjUxXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZXRDaGFubmVsTmFtZShjYWxsSUQgc3RyaW5nKSBzdHJpbmcge1xuICAgMjUyXHRcdHJldHVybiBhcy5jb25maWcuQWdvcmEuQ2hhbm5lbFByZWZpeCArIGNhbGxJRFxuICAgMjUzXHR9XG4gICAyNTRcdFxuICAgMjU1XHQvLyBWYWxpZGF0ZUNoYW5uZWwgY2hlY2tzIGlmIGEgY2hhbm5lbCBleGlzdHMgYW5kIGlzIGFjY2Vzc2libGVcbiAgIDI1Nlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgVmFsaWRhdGVDaGFubmVsKGNoYW5uZWxOYW1lIHN0cmluZykgZXJyb3Ige1xuICAgMjU3XHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIENoYW5uZWwgdmFsaWRhdGlvbiBwYXNzZWQgZm9yOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDI1OFx0XHRyZXR1cm4gbmlsXG4gICAyNTlcdH1cbiAgIDI2MFx0XG4gICAyNjFcdC8vIEdldEFjdGl2ZUNoYW5uZWxzIHJldHVybnMgbGlzdCBvZiBhY3RpdmUgY2hhbm5lbHNcbiAgIDI2Mlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgR2V0QWN0aXZlQ2hhbm5lbHMoKSAoW11zdHJpbmcsIGVycm9yKSB7XG4gICAyNjNcdFx0Ly8gTW9jayBhY3RpdmUgY2hhbm5lbHNcbiAgIDI2NFx0XHRjaGFubmVscyA6PSBbXXN0cmluZ3tcbiAgIDI2NVx0XHRcdGFzLmNvbmZpZy5BZ29yYS5DaGFubmVsUHJlZml4ICsgXCJjYWxsXzFcIixcbiAgIDI2Nlx0XHRcdGFzLmNvbmZpZy5BZ29yYS5DaGFubmVsUHJlZml4ICsgXCJjYWxsXzJcIixcbiAgIDI2N1x0XHR9XG4gICAyNjhcdFxuICAgMjY5XHRcdGxvZy5QcmludGYoXCLwn5OLIFtNT0NLXSBBY3RpdmUgY2hhbm5lbHM6ICV2XCIsIGNoYW5uZWxzKVxuICAgMjcwXHRcdHJldHVybiBjaGFubmVscywgbmlsXG4gICAyNzFcdH1cbiAgIDI3Mlx0XG4gICAyNzNcdC8vIENsZWFudXAgcGVyZm9ybXMgY2xlYW51cCB3aGVuIHNodXR0aW5nIGRvd25cbiAgIDI3NFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgQ2xlYW51cCgpIGVycm9yIHtcbiAgIDI3NVx0XHRsb2cuUHJpbnRmKFwi8J+nuSBbTU9DS10gQ2xlYW5pbmcgdXAgQWdvcmEgc2VydmljZVwiKVxuICAgMjc2XHRcbiAgIDI3N1x0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMjc4XHRcdC8vIDEuIExlYXZlIGFsbCBhY3RpdmUgY2hhbm5lbHNcbiAgIDI3OVx0XHQvLyAyLiBSZWxlYXNlIG1lZGlhIG5vZGUgZmFjdG9yeVxuICAgMjgwXHRcdC8vIDMuIFJlbGVhc2UgQWdvcmEgc2VydmljZVxuICAgMjgxXHRcdC8vIDQuIENsZWFuIHVwIGFsbCByZXNvdXJjZXNcbiAgIDI4Mlx0XG4gICAyODNcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQWdvcmEgc2VydmljZSBjbGVhbnVwIGNvbXBsZXRlZFwiKVxuICAgMjg0XHRcdHJldHVybiBuaWxcbiAgIDI4NVx0fVxuICAgMjg2XHRcblRvdGFsIGxpbmVzIGluIGZpbGU6IDI4NlxuIiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiMTdiNGNlMjQtOTdiNC00NWVjLWE3MmItMWUwN2ZkY2ExM2I1O3Rvb2x1X3ZydHhfMDFKUGFzeWltQjZKdThxQzlMOGc1ZEdoIjp7InJlcXVlc3RJZCI6IjE3YjRjZTI0LTk3YjQtNDVlYy1hNzJiLTFlMDdmZGNhMTNiNSIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFKUGFzeWltQjZKdThxQzlMOGc1ZEdoIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogY29uZmlnL2NvbmZpZy5nb1xuICAgICAxXHRwYWNrYWdlIGNvbmZpZ1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcImxvZ1wiXG4gICAgIDVcdFx0XCJvc1wiXG4gICAgIDZcdFx0XCJzdHJjb252XCJcbiAgICAgN1x0XG4gICAgIDhcdFx0XCJnaXRodWIuY29tL2pvaG8vZ29kb3RlbnZcIlxuICAgICA5XHQpXG4gICAgMTBcdFxuICAgIDExXHR0eXBlIENvbmZpZyBzdHJ1Y3Qge1xuICAgIDEyXHRcdFNlcnZlciBTZXJ2ZXJDb25maWdcbiAgICAxM1x0XHRBZ29yYSAgQWdvcmFDb25maWdcbiAgICAxNFx0XHRBSSAgICAgQUlDb25maWdcbiAgICAxNVx0fVxuICAgIDE2XHRcbiAgICAxN1x0dHlwZSBTZXJ2ZXJDb25maWcgc3RydWN0IHtcbiAgICAxOFx0XHRQb3J0IHN0cmluZ1xuICAgIDE5XHRcdEhvc3Qgc3RyaW5nXG4gICAgMjBcdH1cbiAgICAyMVx0XG4gICAgMjJcdHR5cGUgQWdvcmFDb25maWcgc3RydWN0IHtcbiAgICAyM1x0XHRBcHBJRCAgICAgICAgICBzdHJpbmdcbiAgICAyNFx0XHRBcHBDZXJ0aWZpY2F0ZSBzdHJpbmdcbiAgICAyNVx0XHRDaGFubmVsUHJlZml4ICBzdHJpbmdcbiAgICAyNlx0fVxuICAgIDI3XHRcbiAgICAyOFx0dHlwZSBBSUNvbmZpZyBzdHJ1Y3Qge1xuICAgIDI5XHRcdE9wZW5BSUFQSUtleSAgICBzdHJpbmdcbiAgICAzMFx0XHRPcGVuQUlNb2RlbCAgICAgc3RyaW5nXG4gICAgMzFcdFx0U1RUUHJvdmlkZXIgICAgIHN0cmluZ1xuICAgIDMyXHRcdFRUU1Byb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzM1x0XHRWb2ljZUlEICAgICAgICAgc3RyaW5nXG4gICAgMzRcdFx0RW5hYmxlVkFEICAgICAgIGJvb2xcbiAgICAzNVx0XHRWQURTZW5zaXRpdml0eSAgZmxvYXQ2NFxuICAgIDM2XHR9XG4gICAgMzdcdFxuICAgIDM4XHR2YXIgQXBwQ29uZmlnICpDb25maWdcbiAgICAzOVx0XG4gICAgNDBcdGZ1bmMgTG9hZENvbmZpZygpIHtcbiAgICA0MVx0XHQvLyBMb2FkIC5lbnYgZmlsZSBpZiBpdCBleGlzdHNcbiAgICA0Mlx0XHRpZiBlcnIgOj0gZ29kb3RlbnYuTG9hZCgpOyBlcnIgIT0gbmlsIHtcbiAgICA0M1x0XHRcdGxvZy5QcmludGxuKFwiTm8gLmVudiBmaWxlIGZvdW5kLCB1c2luZyBlbnZpcm9ubWVudCB2YXJpYWJsZXNcIilcbiAgICA0NFx0XHR9XG4gICAgNDVcdFxuICAgIDQ2XHRcdEFwcENvbmZpZyA9ICZDb25maWd7XG4gICAgNDdcdFx0XHRTZXJ2ZXI6IFNlcnZlckNvbmZpZ3tcbiAgICA0OFx0XHRcdFx0UG9ydDogZ2V0RW52KFwiU0VSVkVSX1BPUlRcIiwgXCI4MDgwXCIpLFxuICAgIDQ5XHRcdFx0XHRIb3N0OiBnZXRFbnYoXCJTRVJWRVJfSE9TVFwiLCBcIjAuMC4wLjBcIiksXG4gICAgNTBcdFx0XHR9LFxuICAgIDUxXHRcdFx0QWdvcmE6IEFnb3JhQ29uZmlne1xuICAgIDUyXHRcdFx0XHRBcHBJRDogICAgICAgICAgZ2V0RW52KFwiQUdPUkFfQVBQX0lEXCIsIFwiXCIpLFxuICAgIDUzXHRcdFx0XHRBcHBDZXJ0aWZpY2F0ZTogZ2V0RW52KFwiQUdPUkFfQVBQX0NFUlRJRklDQVRFXCIsIFwiXCIpLFxuICAgIDU0XHRcdFx0XHRDaGFubmVsUHJlZml4OiAgZ2V0RW52KFwiQUdPUkFfQ0hBTk5FTF9QUkVGSVhcIiwgXCJhaV9zdXBwb3J0X1wiKSxcbiAgICA1NVx0XHRcdH0sXG4gICAgNTZcdFx0XHRBSTogQUlDb25maWd7XG4gICAgNTdcdFx0XHRcdE9wZW5BSUFQSUtleTogICAgZ2V0RW52KFwiT1BFTkFJX0FQSV9LRVlcIiwgXCJcIiksXG4gICAgNThcdFx0XHRcdE9wZW5BSU1vZGVsOiAgICAgZ2V0RW52KFwiT1BFTkFJX01PREVMXCIsIFwiZ3B0LTRcIiksXG4gICAgNTlcdFx0XHRcdFNUVFByb3ZpZGVyOiAgICAgZ2V0RW52KFwiU1RUX1BST1ZJREVSXCIsIFwib3BlbmFpXCIpLFxuICAgIDYwXHRcdFx0XHRUVFNQcm92aWRlcjogICAgIGdldEVudihcIlRUU19QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MVx0XHRcdFx0Vm9pY2VJRDogICAgICAgICBnZXRFbnYoXCJUVFNfVk9JQ0VfSURcIiwgXCJhbGxveVwiKSxcbiAgICA2Mlx0XHRcdFx0RW5hYmxlVkFEOiAgICAgICBnZXRFbnZCb29sKFwiRU5BQkxFX1ZBRFwiLCB0cnVlKSxcbiAgICA2M1x0XHRcdFx0VkFEU2Vuc2l0aXZpdHk6ICBnZXRFbnZGbG9hdChcIlZBRF9TRU5TSVRJVklUWVwiLCAwLjcpLFxuICAgIDY0XHRcdFx0fSxcbiAgICA2NVx0XHR9XG4gICAgNjZcdFxuICAgIDY3XHRcdC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgIDY4XHRcdGlmIEFwcENvbmZpZy5BZ29yYS5BcHBJRCA9PSBcIlwiIHtcbiAgICA2OVx0XHRcdGxvZy5GYXRhbChcIkFHT1JBX0FQUF9JRCBpcyByZXF1aXJlZFwiKVxuICAgIDcwXHRcdH1cbiAgICA3MVx0fVxuICAgIDcyXHRcbiAgICA3M1x0ZnVuYyBnZXRFbnYoa2V5LCBkZWZhdWx0VmFsdWUgc3RyaW5nKSBzdHJpbmcge1xuICAgIDc0XHRcdGlmIHZhbHVlIDo9IG9zLkdldGVudihrZXkpOyB2YWx1ZSAhPSBcIlwiIHtcbiAgICA3NVx0XHRcdHJldHVybiB2YWx1ZVxuICAgIDc2XHRcdH1cbiAgICA3N1x0XHRyZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgNzhcdH1cbiAgICA3OVx0XG4gICAgODBcdGZ1bmMgZ2V0RW52Qm9vbChrZXkgc3RyaW5nLCBkZWZhdWx0VmFsdWUgYm9vbCkgYm9vbCB7XG4gICAgODFcdFx0aWYgdmFsdWUgOj0gb3MuR2V0ZW52KGtleSk7IHZhbHVlICE9IFwiXCIge1xuICAgIDgyXHRcdFx0aWYgcGFyc2VkLCBlcnIgOj0gc3RyY29udi5QYXJzZUJvb2wodmFsdWUpOyBlcnIgPT0gbmlsIHtcbiAgICA4M1x0XHRcdFx0cmV0dXJuIHBhcnNlZFxuICAgIDg0XHRcdFx0fVxuICAgIDg1XHRcdH1cbiAgICA4Nlx0XHRyZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgODdcdH1cbiAgICA4OFx0XG4gICAgODlcdGZ1bmMgZ2V0RW52RmxvYXQoa2V5IHN0cmluZywgZGVmYXVsdFZhbHVlIGZsb2F0NjQpIGZsb2F0NjQge1xuICAgIDkwXHRcdGlmIHZhbHVlIDo9IG9zLkdldGVudihrZXkpOyB2YWx1ZSAhPSBcIlwiIHtcbiAgICA5MVx0XHRcdGlmIHBhcnNlZCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VGbG9hdCh2YWx1ZSwgNjQpOyBlcnIgPT0gbmlsIHtcbiAgICA5Mlx0XHRcdFx0cmV0dXJuIHBhcnNlZFxuICAgIDkzXHRcdFx0fVxuICAgIDk0XHRcdH1cbiAgICA5NVx0XHRyZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgOTZcdH1cbi4uLlxuUGF0aDogLmVudlxuICAgICAxXHQjIFNlcnZlciBDb25maWd1cmF0aW9uXG4gICAgIDJcdFNFUlZFUl9QT1JUPTgwODBcbiAgICAgM1x0U0VSVkVSX0hPU1Q9MC4wLjAuMFxuICAgICA0XHRcbiAgICAgNVx0IyBBZ29yYSBDb25maWd1cmF0aW9uXG4gICAgIDZcdEFHT1JBX0FQUF9JRD0wMTQ1MjM0ODBkOTU0NGJlODAwYTAxNGYzNWJkY2ZhY1xuICAgICA3XHRBR09SQV9BUFBfQ0VSVElGSUNBVEU9ZTdmMzdlNjdkODJiNGFmZjg0NjMxNzc0YzFiNWRlNjFcbiAgICAgOFx0QUdPUkFfQ0hBTk5FTF9QUkVGSVg9YWlfc3VwcG9ydF9cbiAgICAgOVx0XG4gICAgMTBcdCMgQUkgQ29uZmlndXJhdGlvblxuICAgIDExXHRPUEVOQUlfQVBJX0tFWT1rLXByb2otengzVFdYUi1MS2NNUFRtMkx3cTB3MmdvcHV6Vk9mNHR1aWd4UUg1aXBpT3lSQ0s5ekZPT2dFTUJqbXFmMEE3UVZkQzFIRzcxa01UM0JsYmtGSjJMY2dpQkF1cGw1VTliQjVyRVR5UE1SbEVWXzFHWi1rWnNXczFKY1RwQllTLWxoa2FFRERNQXZFcWZudFlRQVVkTEtlblAzNlVBXG4gICAgMTJcdE9QRU5BSV9NT0RFTD1ncHQtNFxuICAgIDEzXHRTVFRfUFJPVklERVI9b3BlbmFpXG4gICAgMTRcdFRUU19QUk9WSURFUj1vcGVuYWlcbiAgICAxNVx0VFRTX1ZPSUNFX0lEPWFsbG95XG4gICAgMTZcdEVOQUJMRV9WQUQ9dHJ1ZVxuICAgIDE3XHRWQURfU0VOU0lUSVZJVFk9MC43XG4uLi5cblBhdGg6IFJFQURNRS5tZFxuLi4uXG4gICAgMzBcdOKUgiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJgg4pSCICAgIOKUgiDilIzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJAg4pSCICAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmFxuICAgIDMxXHTilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJggICAg4pSCIOKUgiBOb3RpZmljYXRpb27ilIIg4pSCICAgIOKUjOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUkFxuICAgIDMyXHQgICAgICAgICAgICAgICAgICAgICAgIOKUgiDilIIgU2VydmljZSAgICAg4pSCIOKUguKXhOKUgOKUgOKWuuKUgiBQdXNoL1NNUy9FbWFpbCAg4pSCXG4gICAgMzNcdCAgICAgICAgICAgICAgICAgICAgICAg4pSCIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmCDilIIgICAg4pSCIFNlcnZpY2VzICAgICAgICDilIJcbiAgICAzNFx0ICAgICAgICAgICAgICAgICAgICAgICDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJggICAg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYXG4gICAgMzVcdGBgYFxuICAgIDM2XHRcbiAgICAzN1x0IyMg8J+TiyBQcmVyZXF1aXNpdGVzXG4gICAgMzhcdFxuICAgIDM5XHQtIEdvIDEuMjIgb3IgaGlnaGVyXG4gICAgNDBcdC0gQWdvcmEuaW8gYWNjb3VudCBhbmQgQXBwIElEXG4gICAgNDFcdC0gT3BlbkFJIEFQSSBrZXkgKG9wdGlvbmFsLCB1c2VzIG1vY2sgYnkgZGVmYXVsdClcbiAgICA0Mlx0XG4gICAgNDNcdCMjIPCfm6DvuI8gU2V0dXBcbiAgICA0NFx0XG4gICAgNDVcdDEuICoqQ2xvbmUgYW5kIHNldHVwKio6XG4gICAgNDZcdCAgIGBgYGJhc2hcbiAgICA0N1x0ICAgZ2l0IGNsb25lIDxyZXBvc2l0b3J5PlxuICAgIDQ4XHQgICBjZCBCYWNrZW5kXG4gICAgNDlcdCAgIGBgYFxuICAgIDUwXHRcbiAgICA1MVx0Mi4gKipJbnN0YWxsIGRlcGVuZGVuY2llcyoqOlxuICAgIDUyXHQgICBgYGBiYXNoXG4gICAgNTNcdCAgIGdvIG1vZCB0aWR5XG4gICAgNTRcdCAgIGBgYFxuICAgIDU1XHRcbiAgICA1Nlx0My4gKipDb25maWd1cmUgZW52aXJvbm1lbnQqKjpcbiAgICA1N1x0ICAgYGBgYmFzaFxuICAgIDU4XHQgICBjcCAuZW52IC5lbnZcbiAgICA1OVx0ICAgIyBFZGl0IC5lbnYgd2l0aCB5b3VyIGNvbmZpZ3VyYXRpb25cbiAgICA2MFx0ICAgYGBgXG4gICAgNjFcdFxuICAgIDYyXHQ0LiAqKlJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlcyoqOlxuICAgIDYzXHQgICBgYGBlbnZcbiAgICA2NFx0ICAgQUdPUkFfQVBQX0lEPXlvdXJfYWdvcmFfYXBwX2lkX2hlcmVcbiAgICA2NVx0ICAgQUdPUkFfQVBQX0NFUlRJRklDQVRFPXlvdXJfYWdvcmFfYXBwX2NlcnRpZmljYXRlX2hlcmVcbiAgICA2Nlx0ICAgT1BFTkFJX0FQSV9LRVk9eW91cl9vcGVuYWlfYXBpX2tleV9oZXJlICAjIE9wdGlvbmFsXG4gICAgNjdcdCAgIGBgYFxuICAgIDY4XHRcbiAgICA2OVx0NS4gKipSdW4gdGhlIHNlcnZlcioqOlxuICAgIDcwXHQgICBgYGBiYXNoXG4gICAgNzFcdCAgIGdvIHJ1biBtYWluLmdvXG4gICAgNzJcdCAgIGBgYFxuICAgIDczXHRcbiAgICA3NFx0IyMg8J+UjCBBUEkgRW5kcG9pbnRzXG4gICAgNzVcdFxuICAgIDc2XHQjIyMgQ29yZSBFbmRwb2ludHNcbiAgICA3N1x0XG4gICAgNzhcdHwgTWV0aG9kIHwgRW5kcG9pbnQgfCBEZXNjcmlwdGlvbiB8XG4gICAgNzlcdHwtLS0tLS0tLXwtLS0tLS0tLS0tfC0tLS0tLS0tLS0tLS18XG4gICAgODBcdHwgYEdFVGAgfCBgL2AgfCBXZWxjb21lIG1lc3NhZ2UgYW5kIEFQSSBvdmVydmlldyB8XG4gICAgODFcdHwgYEdFVGAgfCBgL2hlYWx0aGAgfCBIZWFsdGggY2hlY2sgYW5kIHN5c3RlbSBzdGF0dXMgfFxuICAgIDgyXHRcbiAgICA4M1x0IyMjIENhbGwgTWFuYWdlbWVudFxuLi4uXG4gICAyMzRcdFxuICAgMjM1XHQjIyDwn5SnIENvbmZpZ3VyYXRpb25cbiAgIDIzNlx0XG4gICAyMzdcdCMjIyBTZXJ2ZXIgU2V0dGluZ3NcbiAgIDIzOFx0YGBgZW52XG4gICAyMzlcdFNFUlZFUl9QT1JUPTgwODBcbiAgIDI0MFx0U0VSVkVSX0hPU1Q9MC4wLjAuMFxuICAgMjQxXHRgYGBcbiAgIDI0Mlx0XG4gICAyNDNcdCMjIyBBZ29yYSBTZXR0aW5nc1xuICAgMjQ0XHRgYGBlbnZcbiAgIDI0NVx0QUdPUkFfQVBQX0lEPXlvdXJfYXBwX2lkXG4gICAyNDZcdEFHT1JBX0FQUF9DRVJUSUZJQ0FURT15b3VyX2NlcnRpZmljYXRlXG4gICAyNDdcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAyNDhcdGBgYFxuICAgMjQ5XHRcbiAgIDI1MFx0IyMjIEFJIFNldHRpbmdzXG4gICAyNTFcdGBgYGVudlxuICAgMjUyXHRPUEVOQUlfQVBJX0tFWT15b3VyX2tleVxuICAgMjUzXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgIDI1NFx0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgMjU1XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAyNTZcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgMjU3XHRFTkFCTEVfVkFEPXRydWVcbiAgIDI1OFx0VkFEX1NFTlNJVElWSVRZPTAuN1xuICAgMjU5XHRgYGBcbiAgIDI2MFx0XG4gICAyNjFcdCMjIPCfp6ogRGV2ZWxvcG1lbnRcbiAgIDI2Mlx0XG4gICAyNjNcdCMjIyBNb2NrIFNlcnZpY2VzXG4gICAyNjRcdFxuICAgMjY1XHRUaGUgYXBwbGljYXRpb24gaW5jbHVkZXMgbW9jayBpbXBsZW1lbnRhdGlvbnMgZm9yOlxuICAgMjY2XHQtICoqTm90aWZpY2F0aW9uIFNlcnZpY2UqKjogU2ltdWxhdGVzIHB1c2gvU01TL2VtYWlsIG5vdGlmaWNhdGlvbnNcbiAgIDI2N1x0LSAqKkFJIFNlcnZpY2UqKjogTW9jayBTVFQsIExMTSwgYW5kIFRUUyByZXNwb25zZXNcbiAgIDI2OFx0LSAqKkFnb3JhIFNlcnZpY2UqKjogTW9jayB0b2tlbiBnZW5lcmF0aW9uIGFuZCBjaGFubmVsIG1hbmFnZW1lbnRcbiAgIDI2OVx0XG4gICAyNzBcdCMjIyBUZXN0aW5nXG4gICAyNzFcdFxuICAgMjcyXHRgYGBiYXNoXG4gICAyNzNcdCMgVGVzdCBoZWFsdGggZW5kcG9pbnRcbiAgIDI3NFx0Y3VybCBodHRwOi8vbG9jYWxob3N0OjgwODAvaGVhbHRoXG4uLi5cblBhdGg6IG1haW4uZ29cbi4uLlxuICAgIDE3XHRcbiAgICAxOFx0ZnVuYyBtYWluKCkge1xuICAgIDE5XHRcdC8vIExvYWQgY29uZmlndXJhdGlvblxuICAgIDIwXHRcdGNvbmZpZy5Mb2FkQ29uZmlnKClcbiAgICAyMVx0XHRjZmcgOj0gY29uZmlnLkFwcENvbmZpZ1xuICAgIDIyXHRcbiAgICAyM1x0XHRsb2cuUHJpbnRmKFwi8J+agCBTdGFydGluZyBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIilcbiAgICAyNFx0XHRsb2cuUHJpbnRmKFwi8J+TiyBTZXJ2ZXI6ICVzOiVzXCIsIGNmZy5TZXJ2ZXIuSG9zdCwgY2ZnLlNlcnZlci5Qb3J0KVxuICAgIDI1XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIEFwcCBJRDogJXNcIiwgY2ZnLkFnb3JhLkFwcElEKVxuICAgIDI2XHRcbiAgICAyN1x0XHQvLyBJbml0aWFsaXplIHNlcnZpY2VzXG4gICAgMjhcdFx0bm90aWZpY2F0aW9uU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdOb3RpZmljYXRpb25TZXJ2aWNlKClcbiAgICAyOVx0XHRhZ29yYVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QWdvcmFTZXJ2aWNlKGNmZylcbiAgICAzMFx0XHRhaVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QUlTZXJ2aWNlKGNmZylcbiAgICAzMVx0XHRjYWxsTWFuYWdlciA6PSBzZXJ2aWNlcy5OZXdDYWxsTWFuYWdlcihub3RpZmljYXRpb25TZXJ2aWNlLCBhaVNlcnZpY2UsIGFnb3JhU2VydmljZSlcbiAgICAzMlx0XG4gICAgMzNcdFx0Ly8gSW5pdGlhbGl6ZSBoYW5kbGVyc1xuICAgIDM0XHRcdGNhbGxIYW5kbGVyIDo9IGhhbmRsZXJzLk5ld0NhbGxIYW5kbGVyKGNhbGxNYW5hZ2VyLCBhZ29yYVNlcnZpY2UpXG4gICAgMzVcdFxuICAgIDM2XHRcdC8vIFNldHVwIEdpbiByb3V0ZXJcbiAgICAzN1x0XHRyb3V0ZXIgOj0gc2V0dXBSb3V0ZXIoY2FsbEhhbmRsZXIpXG4gICAgMzhcdFxuICAgIDM5XHRcdC8vIFNldHVwIGdyYWNlZnVsIHNodXRkb3duXG4gICAgNDBcdFx0c2V0dXBHcmFjZWZ1bFNodXRkb3duKGFnb3JhU2VydmljZSlcbi4uLlxuUGF0aDogc2VydmljZXMvYWdvcmFfc2VydmljZS5nb1xuICAgICAxXHRwYWNrYWdlIHNlcnZpY2VzXG4gICAgIDJcdFxuICAgICAzXHRpbXBvcnQgKFxuICAgICA0XHRcdFwiQmFja2VuZC9jb25maWdcIlxuICAgICA1XHRcdFwiZm10XCJcbiAgICAgNlx0XHRcImxvZ1wiXG4gICAgIDdcdFx0XCJzdHJjb252XCJcbiAgICAgOFx0XHRcInRpbWVcIlxuICAgICA5XHRcbiAgICAxMFx0XHRcImdpdGh1Yi5jb20vQWdvcmFJTy9Ub29scy9EeW5hbWljS2V5L0Fnb3JhRHluYW1pY0tleS9nby9zcmMvcnRjdG9rZW5idWlsZGVyMlwiXG4gICAgMTFcdClcbiAgICAxMlx0XG4gICAgMTNcdC8vIEFnb3JhU2VydmljZSBoYW5kbGVzIEFnb3JhIFJUQyBvcGVyYXRpb25zXG4gICAgMTRcdHR5cGUgQWdvcmFTZXJ2aWNlIHN0cnVjdCB7XG4gICAgMTVcdFx0Y29uZmlnICpjb25maWcuQ29uZmlnXG4gICAgMTZcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjb250YWluOlxuICAgIDE3XHRcdC8vIC0gQWdvcmEgc2VydmljZSBpbnN0YW5jZVxuICAgIDE4XHRcdC8vIC0gTWVkaWEgbm9kZSBmYWN0b3J5XG4gICAgMTlcdFx0Ly8gLSBBY3RpdmUgY29ubmVjdGlvbnMgbWFwXG4gICAgMjBcdFx0Ly8gLSBBdWRpby9WaWRlbyBvYnNlcnZlcnNcbiAgICAyMVx0fVxuICAgIDIyXHRcbiAgICAyM1x0Ly8gVG9rZW5SZXF1ZXN0IHJlcHJlc2VudHMgYSByZXF1ZXN0IGZvciBBZ29yYSB0b2tlblxuICAgIDI0XHR0eXBlIFRva2VuUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDI1XHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgIDI2XHRcdFVzZXJJRCAgICAgIHN0cmluZyBganNvbjpcInVzZXJfaWRcImBcbiAgICAyN1x0XHRSb2xlICAgICAgICBpbnQgICAgYGpzb246XCJyb2xlXCJgIC8vIDEgPSBwdWJsaXNoZXIsIDIgPSBzdWJzY3JpYmVyXG4gICAgMjhcdH1cbiAgICAyOVx0XG4gICAgMzBcdC8vIFRva2VuUmVzcG9uc2UgcmVwcmVzZW50cyBBZ29yYSB0b2tlbiByZXNwb25zZVxuICAgIDMxXHR0eXBlIFRva2VuUmVzcG9uc2Ugc3RydWN0IHtcbiAgICAzMlx0XHRUb2tlbiAgICAgc3RyaW5nIGBqc29uOlwidG9rZW5cImBcbiAgICAzM1x0XHRFeHBpcmVzQXQgaW50NjQgIGBqc29uOlwiZXhwaXJlc19hdFwiYFxuICAgIDM0XHR9XG4gICAgMzVcdFxuICAgIDM2XHQvLyBOZXdBZ29yYVNlcnZpY2UgY3JlYXRlcyBhIG5ldyBBZ29yYSBzZXJ2aWNlIGluc3RhbmNlXG4gICAgMzdcdGZ1bmMgTmV3QWdvcmFTZXJ2aWNlKGNmZyAqY29uZmlnLkNvbmZpZykgKkFnb3JhU2VydmljZSB7XG4gICAgMzhcdFx0c2VydmljZSA6PSAmQWdvcmFTZXJ2aWNle1xuICAgIDM5XHRcdFx0Y29uZmlnOiBjZmcsXG4gICAgNDBcdFx0fVxuICAgIDQxXHRcbiAgICA0Mlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCBpbml0aWFsaXplIEFnb3JhIFNESyBoZXJlOlxuICAgIDQzXHRcdC8vIDEuIEluaXRpYWxpemUgQWdvcmEgc2VydmljZSB3aXRoIGFwcCBJRFxuICAgIDQ0XHRcdC8vIDIuIENyZWF0ZSBtZWRpYSBub2RlIGZhY3RvcnlcbiAgICA0NVx0XHQvLyAzLiBTZXQgdXAgYXVkaW8vdmlkZW8gcHJvY2Vzc2luZyBjYWxsYmFja3NcbiAgICA0Nlx0XG4gICAgNDdcdFx0bG9nLlByaW50ZihcIvCfjq8gQWdvcmEgc2VydmljZSBpbml0aWFsaXplZCB3aXRoIEFwcCBJRDogJXNcIiwgY2ZnLkFnb3JhLkFwcElEKVxuICAgIDQ4XHRcdHJldHVybiBzZXJ2aWNlXG4gICAgNDlcdH1cbiAgICA1MFx0XG4gICAgNTFcdC8vIEdlbmVyYXRlVG9rZW4gZ2VuZXJhdGVzIGFuIEFnb3JhIFJUQyB0b2tlbiBmb3IgY2hhbm5lbCBhY2Nlc3NcbiAgICA1Mlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgR2VuZXJhdGVUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQpICgqVG9rZW5SZXNwb25zZSwgZXJyb3IpIHtcbiAgICA1M1x0XHRsb2cuUHJpbnRmKFwi8J+UkSBHZW5lcmF0aW5nIEFnb3JhIHRva2VuIGZvciBjaGFubmVsOiAlcywgdXNlcjogJXMsIHJvbGU6ICVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNTRcdFxuICAgIDU1XHRcdC8vIFRva2VuIGV4cGlyZXMgaW4gMjQgaG91cnNcbiAgICA1Nlx0XHRleHBpcmF0aW9uVGltZSA6PSB0aW1lLk5vdygpLkFkZCgyNCAqIHRpbWUuSG91cikuVW5peCgpXG4gICAgNTdcdFxuICAgIDU4XHRcdHZhciB0b2tlbiBzdHJpbmdcbiAgICA1OVx0XHR2YXIgZXJyIGVycm9yXG4gICAgNjBcdFxuICAgIDYxXHRcdGlmIGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSAhPSBcIlwiIHtcbiAgICA2Mlx0XHRcdC8vIEdlbmVyYXRlIHJlYWwgdG9rZW4gd2l0aCBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAgNjNcdFx0XHR0b2tlbiwgZXJyID0gYXMuZ2VuZXJhdGVSZWFsVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSwgdWludDMyKGV4cGlyYXRpb25UaW1lKSlcbiAgICA2NFx0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgIDY1XHRcdFx0XHRyZXR1cm4gbmlsLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGdlbmVyYXRlIHRva2VuOiAld1wiLCBlcnIpXG4gICAgNjZcdFx0XHR9XG4gICAgNjdcdFx0XHRsb2cuUHJpbnRmKFwi4pyFIEdlbmVyYXRlZCByZWFsIEFnb3JhIHRva2VuIGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgICA2OFx0XHR9IGVsc2Uge1xuICAgIDY5XHRcdFx0Ly8gR2VuZXJhdGUgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgICA3MFx0XHRcdHRva2VuID0gYXMuZ2VuZXJhdGVNb2NrVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA3MVx0XHRcdGxvZy5QcmludGYoXCLimqDvuI8gW01PQ0tdIFVzaW5nIG1vY2sgdG9rZW4gKG5vIGFwcCBjZXJ0aWZpY2F0ZSBwcm92aWRlZClcIilcbiAgICA3Mlx0XHR9XG4gICAgNzNcdFxuICAgIDc0XHRcdHJlc3BvbnNlIDo9ICZUb2tlblJlc3BvbnNle1xuICAgIDc1XHRcdFx0VG9rZW46ICAgICB0b2tlbixcbiAgICA3Nlx0XHRcdEV4cGlyZXNBdDogZXhwaXJhdGlvblRpbWUsXG4gICAgNzdcdFx0fVxuICAgIDc4XHRcbiAgICA3OVx0XHRsb2cuUHJpbnRmKFwi4pyFIFRva2VuIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgIDgwXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAgODFcdH1cbiAgICA4Mlx0XG4gICAgODNcdC8vIGdlbmVyYXRlUmVhbFRva2VuIGdlbmVyYXRlcyBhIHJlYWwgQWdvcmEgdG9rZW4gdXNpbmcgdGhlIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA4NFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2VuZXJhdGVSZWFsVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50LCBleHBpcmF0aW9uIHVpbnQzMikgKHN0cmluZywgZXJyb3IpIHtcbiAgICA4NVx0XHQvLyBDb252ZXJ0IHVzZXJJRCB0byB1aW50MzIgKEFnb3JhIGV4cGVjdHMgbnVtZXJpYyBVSUQpXG4gICAgODZcdFx0dmFyIHVpZCB1aW50MzJcbiAgICA4N1x0XHRpZiB1c2VySUQgIT0gXCJcIiB7XG4gICAgODhcdFx0XHRpZiBwYXJzZWRVSUQsIGVyciA6PSBzdHJjb252LlBhcnNlVWludCh1c2VySUQsIDEwLCAzMik7IGVyciA9PSBuaWwge1xuICAgIDg5XHRcdFx0XHR1aWQgPSB1aW50MzIocGFyc2VkVUlEKVxuICAgIDkwXHRcdFx0fSBlbHNlIHtcbiAgICA5MVx0XHRcdFx0Ly8gSWYgdXNlcklEIGlzIG5vdCBudW1lcmljLCBnZW5lcmF0ZSBhIGhhc2gtYmFzZWQgVUlEXG4gICAgOTJcdFx0XHRcdHVpZCA9IHVpbnQzMihsZW4odXNlcklEKSkgLy8gU2ltcGxlIGZhbGxiYWNrLCBpbiBwcm9kdWN0aW9uIHVzZSBhIHByb3BlciBoYXNoXG4gICAgOTNcdFx0XHR9XG4gICAgOTRcdFx0fSBlbHNlIHtcbiAgICA5NVx0XHRcdHVpZCA9IDAgLy8gTGV0IEFnb3JhIGFzc2lnbiBhIFVJRFxuICAgIDk2XHRcdH1cbiAgICA5N1x0XG4gICAgOThcdFx0Ly8gQ29udmVydCByb2xlIHRvIEFnb3JhIHJvbGUgdHlwZVxuICAgIDk5XHRcdHZhciBhZ29yYVJvbGUgcnRjdG9rZW5idWlsZGVyMi5Sb2xlXG4gICAxMDBcdFx0aWYgcm9sZSA9PSAxIHtcbiAgIDEwMVx0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVB1Ymxpc2hlclxuICAgMTAyXHRcdH0gZWxzZSB7XG4gICAxMDNcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVTdWJzY3JpYmVyXG4gICAxMDRcdFx0fVxuICAgMTA1XHRcbiAgIDEwNlx0XHQvLyBHZW5lcmF0ZSB0b2tlbiB1c2luZyBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAxMDdcdFx0dG9rZW4sIGVyciA6PSBydGN0b2tlbmJ1aWxkZXIyLkJ1aWxkVG9rZW5XaXRoVWlkKFxuICAgMTA4XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcElELFxuICAgMTA5XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlLFxuICAgMTEwXHRcdFx0Y2hhbm5lbE5hbWUsXG4gICAxMTFcdFx0XHR1aWQsXG4gICAxMTJcdFx0XHRhZ29yYVJvbGUsXG4gICAxMTNcdFx0XHRleHBpcmF0aW9uLFxuICAgMTE0XHRcdFx0ZXhwaXJhdGlvbiwgLy8gUHJpdmlsZWdlIGV4cGlyZWQgdGltZXN0YW1wIChzYW1lIGFzIHRva2VuIGV4cGlyYXRpb24pXG4gICAxMTVcdFx0KVxuICAgMTE2XHRcbiAgIDExN1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDExOFx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGJ1aWxkIEFnb3JhIHRva2VuOiAld1wiLCBlcnIpXG4gICAxMTlcdFx0fVxuICAgMTIwXHRcbiAgIDEyMVx0XHRyZXR1cm4gdG9rZW4sIG5pbFxuICAgMTIyXHR9XG4gICAxMjNcdFxuICAgMTI0XHQvLyBnZW5lcmF0ZU1vY2tUb2tlbiBnZW5lcmF0ZXMgYSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgMTI1XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQpIHN0cmluZyB7XG4gICAxMjZcdFx0dGltZXN0YW1wIDo9IHRpbWUuTm93KCkuVW5peCgpXG4gICAxMjdcdFx0cmV0dXJuIGZtdC5TcHJpbnRmKFwibW9ja190b2tlbl8lc18lc18lZF8lZFwiLCBjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB0aW1lc3RhbXApXG4gICAxMjhcdH1cbiAgIDEyOVx0XG4gICAxMzBcdC8vIEpvaW5DaGFubmVsIGpvaW5zIGFuIEFnb3JhIGNoYW5uZWwgZm9yIEFJIHByb2Nlc3NpbmdcbiAgIDEzMVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgSm9pbkNoYW5uZWwoY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcpIGVycm9yIHtcbiAgIDEzMlx0XHRsb2cuUHJpbnRmKFwi8J+UlyBbTU9DS10gQUkgam9pbmluZyBBZ29yYSBjaGFubmVsOiAlcyBhcyB1c2VyOiAlc1wiLCBjaGFubmVsTmFtZSwgdXNlcklEKVxuICAgMTMzXHRcbiAgIDEzNFx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTM1XHRcdC8vIDEuIENyZWF0ZSBSVEMgY29ubmVjdGlvblxuICAgMTM2XHRcdC8vIDIuIFNldCB1cCBsb2NhbCB1c2VyXG4gICAxMzdcdFx0Ly8gMy4gQ3JlYXRlIGF1ZGlvIHRyYWNrIGZvciBBSSB2b2ljZVxuICAgMTM4XHRcdC8vIDQuIFJlZ2lzdGVyIGF1ZGlvIGZyYW1lIG9ic2VydmVycyBmb3IgcHJvY2Vzc2luZyBpbmNvbWluZyBhdWRpb1xuICAgMTM5XHRcdC8vIDUuIEpvaW4gdGhlIGNoYW5uZWxcbiAgIDE0MFx0XHQvLyA2LiBTdGFydCBhdWRpbyBwcm9jZXNzaW5nIHBpcGVsaW5lXG4gICAxNDFcdFxuICAgMTQyXHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb25cbiAgIDE0M1x0XHR0aW1lLlNsZWVwKDUwMCAqIHRpbWUuTWlsbGlzZWNvbmQpIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gdGltZVxuICAgMTQ0XHRcbiAgIDE0NVx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgam9pbmVkIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTQ2XHRcdHJldHVybiBuaWxcbiAgIDE0N1x0fVxuICAgMTQ4XHRcbiAgIDE0OVx0Ly8gTGVhdmVDaGFubmVsIGxlYXZlcyBhbiBBZ29yYSBjaGFubmVsXG4gICAxNTBcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIExlYXZlQ2hhbm5lbChjYWxsSUQgc3RyaW5nKSBlcnJvciB7XG4gICAxNTFcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMTUyXHRcdGxvZy5QcmludGYoXCLwn5GLIFtNT0NLXSBBSSBsZWF2aW5nIEFnb3JhIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTUzXHRcbiAgIDE1NFx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTU1XHRcdC8vIDEuIFN0b3AgYXVkaW8gcHJvY2Vzc2luZ1xuICAgMTU2XHRcdC8vIDIuIFVucHVibGlzaCBhdWRpbyB0cmFja3NcbiAgIDE1N1x0XHQvLyAzLiBEaXNjb25uZWN0IGZyb20gY2hhbm5lbFxuICAgMTU4XHRcdC8vIDQuIENsZWFuIHVwIHJlc291cmNlc1xuICAgMTU5XHRcbiAgIDE2MFx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uXG4gICAxNjFcdFx0dGltZS5TbGVlcCgyMDAgKiB0aW1lLk1pbGxpc2Vjb25kKSAvLyBTaW11bGF0ZSBkaXNjb25uZWN0aW9uIHRpbWVcbiAgIDE2Mlx0XG4gICAxNjNcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQUkgc3VjY2Vzc2Z1bGx5IGxlZnQgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNjRcdFx0cmV0dXJuIG5pbFxuICAgMTY1XHR9XG4gICAxNjZcdFxuICAgMTY3XHQvLyBTZW5kQXVkaW9Ub0NoYW5uZWwgc2VuZHMgQUktZ2VuZXJhdGVkIGF1ZGlvIHRvIHRoZSBjaGFubmVsXG4gICAxNjhcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIFNlbmRBdWRpb1RvQ2hhbm5lbChjYWxsSUQgc3RyaW5nLCBhdWRpb0RhdGEgW11ieXRlKSBlcnJvciB7XG4gICAxNjlcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMTcwXHRcdGxvZy5QcmludGYoXCLwn5SKIFtNT0NLXSBTZW5kaW5nICVkIGJ5dGVzIG9mIGF1ZGlvIHRvIGNoYW5uZWw6ICVzXCIsIGxlbihhdWRpb0RhdGEpLCBjaGFubmVsTmFtZSlcbiAgIDE3MVx0XG4gICAxNzJcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDE3M1x0XHQvLyAxLiBDb252ZXJ0IGF1ZGlvIGRhdGEgdG8gcmVxdWlyZWQgZm9ybWF0IChQQ00pXG4gICAxNzRcdFx0Ly8gMi4gUHVzaCBhdWRpbyBmcmFtZXMgdG8gQWdvcmEgYXVkaW8gdHJhY2tcbiAgIDE3NVx0XHQvLyAzLiBIYW5kbGUgYXVkaW8gZW5jb2RpbmcgYW5kIHRyYW5zbWlzc2lvblxuICAgMTc2XHRcbiAgIDE3N1x0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uIC0gc2ltdWxhdGUgYXVkaW8gdHJhbnNtaXNzaW9uIHRpbWVcbiAgIDE3OFx0XHR0cmFuc21pc3Npb25UaW1lIDo9IHRpbWUuRHVyYXRpb24obGVuKGF1ZGlvRGF0YSkvMTAwMCkgKiB0aW1lLk1pbGxpc2Vjb25kXG4gICAxNzlcdFx0aWYgdHJhbnNtaXNzaW9uVGltZSA+IDEwMCp0aW1lLk1pbGxpc2Vjb25kIHtcbiAgIDE4MFx0XHRcdHRyYW5zbWlzc2lvblRpbWUgPSAxMDAgKiB0aW1lLk1pbGxpc2Vjb25kXG4gICAxODFcdFx0fVxuICAgMTgyXHRcdHRpbWUuU2xlZXAodHJhbnNtaXNzaW9uVGltZSlcbiAgIDE4M1x0XG4gICAxODRcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQXVkaW8gc2VudCBzdWNjZXNzZnVsbHkgdG8gY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxODVcdFx0cmV0dXJuIG5pbFxuICAgMTg2XHR9XG4gICAxODdcdFxuICAgMTg4XHQvLyBTZXR1cEF1ZGlvUHJvY2Vzc2luZyBzZXRzIHVwIGF1ZGlvIHByb2Nlc3NpbmcgY2FsbGJhY2tzXG4gICAxODlcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIFNldHVwQXVkaW9Qcm9jZXNzaW5nKGNhbGxJRCBzdHJpbmcsIGF1ZGlvQ2FsbGJhY2sgZnVuYyhbXWJ5dGUpIGVycm9yKSBlcnJvciB7XG4gICAxOTBcdFx0Y2hhbm5lbE5hbWUgOj0gYXMuZ2V0Q2hhbm5lbE5hbWUoY2FsbElEKVxuICAgMTkxXHRcdGxvZy5QcmludGYoXCLwn46Z77iPIFtNT0NLXSBTZXR0aW5nIHVwIGF1ZGlvIHByb2Nlc3NpbmcgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTkyXHRcbiAgIDE5M1x0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTk0XHRcdC8vIDEuIFJlZ2lzdGVyIGF1ZGlvIGZyYW1lIG9ic2VydmVyXG4gICAxOTVcdFx0Ly8gMi4gU2V0IHVwIFZBRCAoVm9pY2UgQWN0aXZpdHkgRGV0ZWN0aW9uKVxuICAgMTk2XHRcdC8vIDMuIENvbmZpZ3VyZSBhdWRpbyBmb3JtYXQgKHNhbXBsZSByYXRlLCBjaGFubmVscywgZXRjLilcbiAgIDE5N1x0XHQvLyA0LiBTdGFydCBwcm9jZXNzaW5nIGluY29taW5nIGF1ZGlvIGZyYW1lc1xuICAgMTk4XHRcbiAgIDE5OVx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uIC0gc2ltdWxhdGUgcmVjZWl2aW5nIGF1ZGlvIHBlcmlvZGljYWxseVxuICAgMjAwXHRcdGdvIGFzLnNpbXVsYXRlSW5jb21pbmdBdWRpbyhjYWxsSUQsIGF1ZGlvQ2FsbGJhY2spXG4gICAyMDFcdFxuICAgMjAyXHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEF1ZGlvIHByb2Nlc3Npbmcgc2V0dXAgY29tcGxldGUgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMjAzXHRcdHJldHVybiBuaWxcbiAgIDIwNFx0fVxuICAgMjA1XHRcbiAgIDIwNlx0Ly8gc2ltdWxhdGVJbmNvbWluZ0F1ZGlvIHNpbXVsYXRlcyByZWNlaXZpbmcgYXVkaW8gZnJvbSB0aGUgY3VzdG9tZXJcbiAgIDIwN1x0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgc2ltdWxhdGVJbmNvbWluZ0F1ZGlvKGNhbGxJRCBzdHJpbmcsIGF1ZGlvQ2FsbGJhY2sgZnVuYyhbXWJ5dGUpIGVycm9yKSB7XG4gICAyMDhcdFx0bG9nLlByaW50ZihcIvCfjq0gW01PQ0tdIFN0YXJ0aW5nIGF1ZGlvIHNpbXVsYXRpb24gZm9yIGNhbGw6ICVzXCIsIGNhbGxJRClcbiAgIDIwOVx0XG4gICAyMTBcdFx0Ly8gU2ltdWxhdGUgY3VzdG9tZXIgc3BlYWtpbmcgZXZlcnkgNS0xMCBzZWNvbmRzXG4gICAyMTFcdFx0Zm9yIGkgOj0gMDsgaSA8IDU7IGkrKyB7IC8vIFNpbXVsYXRlIDUgY3VzdG9tZXIgbWVzc2FnZXNcbiAgIDIxMlx0XHRcdHRpbWUuU2xlZXAodGltZS5EdXJhdGlvbig1K2kqMikgKiB0aW1lLlNlY29uZClcbiAgIDIxM1x0XG4gICAyMTRcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIGF1ZGlvIGRhdGFcbiAgIDIxNVx0XHRcdG1vY2tBdWRpb0RhdGEgOj0gbWFrZShbXWJ5dGUsIDE2MDApIC8vIFNpbXVsYXRlIDEwMG1zIG9mIDE2a0h6IG1vbm8gYXVkaW9cbiAgIDIxNlx0XHRcdGZvciBqIDo9IHJhbmdlIG1vY2tBdWRpb0RhdGEge1xuICAgMjE3XHRcdFx0XHRtb2NrQXVkaW9EYXRhW2pdID0gYnl0ZSgoaSoxMDAgKyBqKSAlIDI1NilcbiAgIDIxOFx0XHRcdH1cbiAgIDIxOVx0XG4gICAyMjBcdFx0XHRsb2cuUHJpbnRmKFwi8J+OpCBbTU9DS10gU2ltdWxhdGluZyBpbmNvbWluZyBhdWRpbyBmb3IgY2FsbCAlcyAobWVzc2FnZSAlZClcIiwgY2FsbElELCBpKzEpXG4gICAyMjFcdFxuICAgMjIyXHRcdFx0aWYgZXJyIDo9IGF1ZGlvQ2FsbGJhY2sobW9ja0F1ZGlvRGF0YSk7IGVyciAhPSBuaWwge1xuICAgMjIzXHRcdFx0XHRsb2cuUHJpbnRmKFwiRXJyb3IgcHJvY2Vzc2luZyBzaW11bGF0ZWQgYXVkaW86ICV2XCIsIGVycilcbiAgIDIyNFx0XHRcdFx0YnJlYWtcbiAgIDIyNVx0XHRcdH1cbiAgIDIyNlx0XHR9XG4gICAyMjdcdFxuICAgMjI4XHRcdGxvZy5QcmludGYoXCLwn46tIFtNT0NLXSBBdWRpbyBzaW11bGF0aW9uIGNvbXBsZXRlZCBmb3IgY2FsbDogJXNcIiwgY2FsbElEKVxuICAgMjI5XHR9XG4gICAyMzBcdFxuICAgMjMxXHQvLyBHZXRDaGFubmVsU3RhdHMgcmV0dXJucyBjaGFubmVsIHN0YXRpc3RpY3NcbiAgIDIzMlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgR2V0Q2hhbm5lbFN0YXRzKGNhbGxJRCBzdHJpbmcpIChtYXBbc3RyaW5nXWludGVyZmFjZXt9LCBlcnJvcikge1xuICAgMjMzXHRcdGNoYW5uZWxOYW1lIDo9IGFzLmdldENoYW5uZWxOYW1lKGNhbGxJRClcbiAgIDIzNFx0XHRsb2cuUHJpbnRmKFwi8J+TiiBbTU9DS10gR2V0dGluZyBjaGFubmVsIHN0YXRzIGZvcjogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAyMzVcdFxuICAgMjM2XHRcdC8vIE1vY2sgc3RhdGlzdGljc1xuICAgMjM3XHRcdHN0YXRzIDo9IG1hcFtzdHJpbmddaW50ZXJmYWNle317XG4gICAyMzhcdFx0XHRcImNoYW5uZWxfbmFtZVwiOiAgICAgY2hhbm5lbE5hbWUsXG4gICAyMzlcdFx0XHRcImNvbm5lY3RlZF91c2Vyc1wiOiAgMiwgLy8gQUkgKyBDdXN0b21lclxuICAgMjQwXHRcdFx0XCJhdWRpb19iaXRyYXRlXCI6ICAgIDY0MDAwLFxuICAgMjQxXHRcdFx0XCJwYWNrZXRfbG9zc19yYXRlXCI6IDAuMDEsXG4gICAyNDJcdFx0XHRcInJ0dFwiOiAgICAgICAgICAgICA0NSwgLy8gUm91bmQgdHJpcCB0aW1lIGluIG1zXG4gICAyNDNcdFx0XHRcImF1ZGlvX3F1YWxpdHlcIjogICAgXCJnb29kXCIsXG4gICAyNDRcdFx0XHRcImNvbm5lY3Rpb25fc3RhdGVcIjogXCJjb25uZWN0ZWRcIixcbiAgIDI0NVx0XHR9XG4gICAyNDZcdFxuICAgMjQ3XHRcdHJldHVybiBzdGF0cywgbmlsXG4gICAyNDhcdH1cbiAgIDI0OVx0XG4gICAyNTBcdC8vIGdldENoYW5uZWxOYW1lIGdlbmVyYXRlcyBjaGFubmVsIG5hbWUgZnJvbSBjYWxsIElEXG4gICAyNTFcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdldENoYW5uZWxOYW1lKGNhbGxJRCBzdHJpbmcpIHN0cmluZyB7XG4gICAyNTJcdFx0cmV0dXJuIGFzLmNvbmZpZy5BZ29yYS5DaGFubmVsUHJlZml4ICsgY2FsbElEXG4gICAyNTNcdH1cbiAgIDI1NFx0XG4gICAyNTVcdC8vIFZhbGlkYXRlQ2hhbm5lbCBjaGVja3MgaWYgYSBjaGFubmVsIGV4aXN0cyBhbmQgaXMgYWNjZXNzaWJsZVxuICAgMjU2XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBWYWxpZGF0ZUNoYW5uZWwoY2hhbm5lbE5hbWUgc3RyaW5nKSBlcnJvciB7XG4gICAyNTdcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQ2hhbm5lbCB2YWxpZGF0aW9uIHBhc3NlZCBmb3I6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMjU4XHRcdHJldHVybiBuaWxcbiAgIDI1OVx0fVxuICAgMjYwXHRcbiAgIDI2MVx0Ly8gR2V0QWN0aXZlQ2hhbm5lbHMgcmV0dXJucyBsaXN0IG9mIGFjdGl2ZSBjaGFubmVsc1xuICAgMjYyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZXRBY3RpdmVDaGFubmVscygpIChbXXN0cmluZywgZXJyb3IpIHtcbiAgIDI2M1x0XHQvLyBNb2NrIGFjdGl2ZSBjaGFubmVsc1xuICAgMjY0XHRcdGNoYW5uZWxzIDo9IFtdc3RyaW5ne1xuICAgMjY1XHRcdFx0YXMuY29uZmlnLkFnb3JhLkNoYW5uZWxQcmVmaXggKyBcImNhbGxfMVwiLFxuICAgMjY2XHRcdFx0YXMuY29uZmlnLkFnb3JhLkNoYW5uZWxQcmVmaXggKyBcImNhbGxfMlwiLFxuICAgMjY3XHRcdH1cbiAgIDI2OFx0XG4gICAyNjlcdFx0bG9nLlByaW50ZihcIvCfk4sgW01PQ0tdIEFjdGl2ZSBjaGFubmVsczogJXZcIiwgY2hhbm5lbHMpXG4gICAyNzBcdFx0cmV0dXJuIGNoYW5uZWxzLCBuaWxcbiAgIDI3MVx0fVxuICAgMjcyXHRcbiAgIDI3M1x0Ly8gQ2xlYW51cCBwZXJmb3JtcyBjbGVhbnVwIHdoZW4gc2h1dHRpbmcgZG93blxuICAgMjc0XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBDbGVhbnVwKCkgZXJyb3Ige1xuICAgMjc1XHRcdGxvZy5QcmludGYoXCLwn6e5IFtNT0NLXSBDbGVhbmluZyB1cCBBZ29yYSBzZXJ2aWNlXCIpXG4gICAyNzZcdFxuICAgMjc3XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAyNzhcdFx0Ly8gMS4gTGVhdmUgYWxsIGFjdGl2ZSBjaGFubmVsc1xuICAgMjc5XHRcdC8vIDIuIFJlbGVhc2UgbWVkaWEgbm9kZSBmYWN0b3J5XG4gICAyODBcdFx0Ly8gMy4gUmVsZWFzZSBBZ29yYSBzZXJ2aWNlXG4gICAyODFcdFx0Ly8gNC4gQ2xlYW4gdXAgYWxsIHJlc291cmNlc1xuICAgMjgyXHRcbiAgIDI4M1x0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBZ29yYSBzZXJ2aWNlIGNsZWFudXAgY29tcGxldGVkXCIpXG4gICAyODRcdFx0cmV0dXJuIG5pbFxuICAgMjg1XHR9XG4uLi5cblBhdGg6IHNlcnZpY2VzL2FpX3NlcnZpY2UuZ29cbi4uLlxuICAgMjMzXHRcbiAgIDIzNFx0Ly8gQ29udmVydFRleHRUb1NwZWVjaCBjb252ZXJ0cyB0ZXh0IHRvIHNwZWVjaCBhdWRpb1xuICAgMjM1XHRmdW5jIChhaSAqQUlTZXJ2aWNlKSBDb252ZXJ0VGV4dFRvU3BlZWNoKHRleHQgc3RyaW5nKSAoW11ieXRlLCBlcnJvcikge1xuICAgMjM2XHRcdGxvZy5QcmludGYoXCLwn5SKIFtNT0NLXSBDb252ZXJ0aW5nIHRleHQgdG8gc3BlZWNoOiAnJXMnXCIsIHRleHQpXG4gICAyMzdcdFx0XG4gICAyMzhcdFx0Ly8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMjM5XHRcdC8vIDEuIENhbGwgT3BlbkFJIFRUUyBBUEkgb3Igb3RoZXIgVFRTIHNlcnZpY2VcbiAgIDI0MFx0XHQvLyAyLiBSZXR1cm4gYXVkaW8gYnl0ZXMgaW4gdGhlIHJlcXVpcmVkIGZvcm1hdFxuICAgMjQxXHRcdC8vIDMuIEhhbmRsZSBkaWZmZXJlbnQgdm9pY2VzIGFuZCBsYW5ndWFnZXNcbiAgIDI0Mlx0XHRcbiAgIDI0M1x0XHQvLyBTaW11bGF0ZSBwcm9jZXNzaW5nIHRpbWUgYmFzZWQgb24gdGV4dCBsZW5ndGhcbiAgIDI0NFx0XHRwcm9jZXNzaW5nVGltZSA6PSB0aW1lLkR1cmF0aW9uKGxlbih0ZXh0KSoxMCkgKiB0aW1lLk1pbGxpc2Vjb25kXG4gICAyNDVcdFx0aWYgcHJvY2Vzc2luZ1RpbWUgPiAyKnRpbWUuU2Vjb25kIHtcbiAgIDI0Nlx0XHRcdHByb2Nlc3NpbmdUaW1lID0gMiAqIHRpbWUuU2Vjb25kXG4gICAyNDdcdFx0fVxuICAgMjQ4XHRcdHRpbWUuU2xlZXAocHJvY2Vzc2luZ1RpbWUpXG4gICAyNDlcdFx0XG4gICAyNTBcdFx0Ly8gTW9jayBhdWRpbyBkYXRhIC0gaW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBiZSBhY3R1YWwgYXVkaW8gYnl0ZXNcbiAgIDI1MVx0XHRtb2NrQXVkaW9EYXRhIDo9IG1ha2UoW11ieXRlLCBsZW4odGV4dCkqMTAwKSAvLyBTaW11bGF0ZSBhdWRpbyBkYXRhIHNpemVcbiAgIDI1Mlx0XHRmb3IgaSA6PSByYW5nZSBtb2NrQXVkaW9EYXRhIHtcbiAgIDI1M1x0XHRcdG1vY2tBdWRpb0RhdGFbaV0gPSBieXRlKGkgJSAyNTYpXG4gICAyNTRcdFx0fVxuICAgMjU1XHRcdFxuICAgMjU2XHRcdGxvZy5QcmludGYoXCLwn461IFtNT0NLXSBHZW5lcmF0ZWQgJWQgYnl0ZXMgb2YgYXVkaW8gZGF0YVwiLCBsZW4obW9ja0F1ZGlvRGF0YSkpXG4gICAyNTdcdFx0cmV0dXJuIG1vY2tBdWRpb0RhdGEsIG5pbFxuICAgMjU4XHR9XG4uLi5cbiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sImViMDU5MWEzLTM4ZTQtNGRlOC04NDQ2LTNmYzA4YjVlYzBlMTt0b29sdV92cnR4XzAxTWZFazhVU1cxNDJld016NWgzWUxCMiI6eyJyZXF1ZXN0SWQiOiJlYjA1OTFhMy0zOGU0LTRkZTgtODQ0Ni0zZmMwOGI1ZWMwZTEiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxTWZFazhVU1cxNDJld016NWgzWUxCMiIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IGhhbmRsZXJzL2NhbGxfaGFuZGxlci5nb1xuLi4uXG4gICAgMjZcdFxuICAgIDI3XHQvLyBJbml0aWF0ZUNhbGwgaGFuZGxlcyBQT1NUIC9hcGkvY2FsbHMvaW5pdGlhdGVcbiAgICAyOFx0ZnVuYyAoY2ggKkNhbGxIYW5kbGVyKSBJbml0aWF0ZUNhbGwoYyAqZ2luLkNvbnRleHQpIHtcbiAgICAyOVx0XHR2YXIgcmVxdWVzdCBtb2RlbHMuSW5pdGlhdGVDYWxsUmVxdWVzdFxuICAgIDMwXHRcdGlmIGVyciA6PSBjLlNob3VsZEJpbmRKU09OKCZyZXF1ZXN0KTsgZXJyICE9IG5pbCB7XG4gICAgMzFcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgICAzMlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiSW52YWxpZCByZXF1ZXN0IGZvcm1hdFwiLFxuICAgIDMzXHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgMzRcdFx0XHR9KVxuICAgIDM1XHRcdFx0cmV0dXJuXG4gICAgMzZcdFx0fVxuICAgIDM3XHRcbiAgICAzOFx0XHQvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICAzOVx0XHRpZiByZXF1ZXN0LkN1c3RvbWVySUQgPT0gXCJcIiB7XG4gICAgNDBcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgICA0MVx0XHRcdFx0XCJlcnJvclwiOiBcImN1c3RvbWVyX2lkIGlzIHJlcXVpcmVkXCIsXG4gICAgNDJcdFx0XHR9KVxuICAgIDQzXHRcdFx0cmV0dXJuXG4gICAgNDRcdFx0fVxuICAgIDQ1XHRcbiAgICA0Nlx0XHRpZiByZXF1ZXN0LlB1cnBvc2UgPT0gXCJcIiB7XG4gICAgNDdcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgICA0OFx0XHRcdFx0XCJlcnJvclwiOiBcInB1cnBvc2UgaXMgcmVxdWlyZWRcIixcbiAgICA0OVx0XHRcdH0pXG4gICAgNTBcdFx0XHRyZXR1cm5cbiAgICA1MVx0XHR9XG4gICAgNTJcdFxuICAgIDUzXHRcdC8vIEluaXRpYXRlIHRoZSBjYWxsXG4gICAgNTRcdFx0Y2FsbCwgZXJyIDo9IGNoLmNhbGxNYW5hZ2VyLkluaXRpYXRlQ2FsbChyZXF1ZXN0KVxuICAgIDU1XHRcdGlmIGVyciAhPSBuaWwge1xuICAgIDU2XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgNTdcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBpbml0aWF0ZSBjYWxsXCIsXG4gICAgNThcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA1OVx0XHRcdH0pXG4gICAgNjBcdFx0XHRyZXR1cm5cbiAgICA2MVx0XHR9XG4gICAgNjJcdFxuICAgIDYzXHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY2hhbm5lbFxuICAgIDY0XHRcdHRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgXCJhaV9hZ2VudFwiLCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgICA2NVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2Nlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDY3XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgQWdvcmEgdG9rZW5cIixcbiAgICA2OFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDY5XHRcdFx0fSlcbiAgICA3MFx0XHRcdHJldHVyblxuICAgIDcxXHRcdH1cbiAgICA3Mlx0XG4gICAgNzNcdFx0Ly8gSm9pbiB0aGUgY2hhbm5lbCBhcyBBSSBhZ2VudFxuICAgIDc0XHRcdGlmIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuSm9pbkNoYW5uZWwoY2FsbC5DaGFubmVsTmFtZSwgXCJhaV9hZ2VudFwiKTsgZXJyICE9IG5pbCB7XG4gICAgNzVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgICA3Nlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGpvaW4gQWdvcmEgY2hhbm5lbFwiLFxuICAgIDc3XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgNzhcdFx0XHR9KVxuICAgIDc5XHRcdFx0cmV0dXJuXG4gICAgODBcdFx0fVxuICAgIDgxXHRcbiAgICA4Mlx0XHQvLyBTZXQgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgdGhpcyBjYWxsXG4gICAgODNcdFx0YXVkaW9DYWxsYmFjayA6PSBmdW5jKGF1ZGlvRGF0YSBbXWJ5dGUpIGVycm9yIHtcbiAgICA4NFx0XHRcdHJldHVybiBjaC5jYWxsTWFuYWdlci5Qcm9jZXNzQXVkaW9NZXNzYWdlKGNhbGwuSUQsIGF1ZGlvRGF0YSlcbiAgICA4NVx0XHR9XG4gICAgODZcdFxuICAgIDg3XHRcdGlmIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuU2V0dXBBdWRpb1Byb2Nlc3NpbmcoY2FsbC5JRCwgYXVkaW9DYWxsYmFjayk7IGVyciAhPSBuaWwge1xuICAgIDg4XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgODlcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBzZXR1cCBhdWRpbyBwcm9jZXNzaW5nXCIsXG4gICAgOTBcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA5MVx0XHRcdH0pXG4gICAgOTJcdFx0XHRyZXR1cm5cbiAgICA5M1x0XHR9XG4gICAgOTRcdFxuICAgIDk1XHRcdHJlc3BvbnNlIDo9IG1vZGVscy5Jbml0aWF0ZUNhbGxSZXNwb25zZXtcbiAgICA5Nlx0XHRcdENhbGxJRDogICAgICBjYWxsLklELFxuICAgIDk3XHRcdFx0Q2hhbm5lbE5hbWU6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAgOThcdFx0XHRUb2tlbjogICAgICAgdG9rZW4uVG9rZW4sXG4gICAgOTlcdFx0XHRTdGF0dXM6ICAgICAgc3RyaW5nKGNhbGwuU3RhdHVzKSxcbiAgIDEwMFx0XHRcdE1lc3NhZ2U6ICAgICBcIkNhbGwgaW5pdGlhdGVkIHN1Y2Nlc3NmdWxseS4gTm90aWZpY2F0aW9uIHNlbnQgdG8gY3VzdG9tZXIuXCIsXG4gICAxMDFcdFx0fVxuICAgMTAyXHRcbiAgIDEwM1x0XHRjLkpTT04oaHR0cC5TdGF0dXNDcmVhdGVkLCByZXNwb25zZSlcbiAgIDEwNFx0fVxuICAgMTA1XHRcbiAgIDEwNlx0Ly8gSW5pdGlhdGVDdXN0b21lckNhbGwgaGFuZGxlcyBQT1NUIC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGVcbiAgIDEwN1x0ZnVuYyAoY2ggKkNhbGxIYW5kbGVyKSBJbml0aWF0ZUN1c3RvbWVyQ2FsbChjICpnaW4uQ29udGV4dCkge1xuICAgMTA4XHRcdHZhciByZXF1ZXN0IG1vZGVscy5Jbml0aWF0ZUN1c3RvbWVyQ2FsbFJlcXVlc3RcbiAgIDEwOVx0XHRpZiBlcnIgOj0gYy5TaG91bGRCaW5kSlNPTigmcmVxdWVzdCk7IGVyciAhPSBuaWwge1xuICAgMTEwXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAxMTFcdFx0XHRcdFwiZXJyb3JcIjogICBcIkludmFsaWQgcmVxdWVzdCBmb3JtYXRcIixcbiAgIDExMlx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMTEzXHRcdFx0fSlcbiAgIDExNFx0XHRcdHJldHVyblxuICAgMTE1XHRcdH1cbiAgIDExNlx0XG4gICAxMTdcdFx0Ly8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAxMThcdFx0aWYgcmVxdWVzdC5DdXN0b21lcklEID09IFwiXCIge1xuICAgMTE5XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAxMjBcdFx0XHRcdFwiZXJyb3JcIjogXCJjdXN0b21lcl9pZCBpcyByZXF1aXJlZFwiLFxuICAgMTIxXHRcdFx0fSlcbiAgIDEyMlx0XHRcdHJldHVyblxuICAgMTIzXHRcdH1cbiAgIDEyNFx0XG4gICAxMjVcdFx0aWYgcmVxdWVzdC5QdXJwb3NlID09IFwiXCIge1xuICAgMTI2XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAxMjdcdFx0XHRcdFwiZXJyb3JcIjogXCJwdXJwb3NlIGlzIHJlcXVpcmVkXCIsXG4gICAxMjhcdFx0XHR9KVxuICAgMTI5XHRcdFx0cmV0dXJuXG4gICAxMzBcdFx0fVxuICAgMTMxXHRcbiAgIDEzMlx0XHQvLyBJbml0aWF0ZSB0aGUgY3VzdG9tZXItdG8tQUkgY2FsbFxuICAgMTMzXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Jbml0aWF0ZUN1c3RvbWVyQ2FsbChyZXF1ZXN0KVxuICAgMTM0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTM1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAxMzZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBpbml0aWF0ZSBjdXN0b21lciBjYWxsXCIsXG4gICAxMzdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDEzOFx0XHRcdH0pXG4gICAxMzlcdFx0XHRyZXR1cm5cbiAgIDE0MFx0XHR9XG4gICAxNDFcdFxuICAgMTQyXHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY3VzdG9tZXJcbiAgIDE0M1x0XHRjdXN0b21lclRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgcmVxdWVzdC5DdXN0b21lcklELCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgIDE0NFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE0NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMTQ2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgY3VzdG9tZXIgQWdvcmEgdG9rZW5cIixcbiAgIDE0N1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMTQ4XHRcdFx0fSlcbiAgIDE0OVx0XHRcdHJldHVyblxuICAgMTUwXHRcdH1cbi4uLlxuICAgMTczXHRcbiAgIDE3NFx0XHQvLyBBZGQgaW5pdGlhbCBBSSBncmVldGluZ1xuICAgMTc1XHRcdGdyZWV0aW5nIDo9IGNoLmdlbmVyYXRlQ3VzdG9tZXJDYWxsR3JlZXRpbmcoY2FsbClcbiAgIDE3Nlx0XHRjYWxsLkFkZE1lc3NhZ2UoXCJhaVwiLCBncmVldGluZywgXCJ0ZXh0XCIpXG4gICAxNzdcdFxuICAgMTc4XHRcdC8vIFByZXBhcmUgQUkgaW5mb1xuICAgMTc5XHRcdGFpSW5mbyA6PSBtb2RlbHMuQUlJbmZve1xuICAgMTgwXHRcdFx0TmFtZTogICAgICAgIFwiQUkgU3VwcG9ydCBBc3Npc3RhbnRcIixcbiAgIDE4MVx0XHRcdFBlcnNvbmFsaXR5OiBjYWxsLk1ldGFkYXRhLkFJUGVyc29uYWxpdHksXG4gICAxODJcdFx0XHRMYW5ndWFnZTogICAgY2FsbC5NZXRhZGF0YS5MYW5ndWFnZSxcbiAgIDE4M1x0XHRcdENhcGFiaWxpdGllczogW11zdHJpbmd7XG4gICAxODRcdFx0XHRcdFwiYmlsbGluZ19zdXBwb3J0XCIsXG4gICAxODVcdFx0XHRcdFwidGVjaG5pY2FsX3N1cHBvcnRcIixcbiAgIDE4Nlx0XHRcdFx0XCJhY2NvdW50X21hbmFnZW1lbnRcIixcbiAgIDE4N1x0XHRcdFx0XCJnZW5lcmFsX2lucXVpcmllc1wiLFxuICAgMTg4XHRcdFx0XHRcInBhc3N3b3JkX3Jlc2V0XCIsXG4gICAxODlcdFx0XHRcdFwic3Vic2NyaXB0aW9uX21hbmFnZW1lbnRcIixcbiAgIDE5MFx0XHRcdH0sXG4gICAxOTFcdFx0fVxuICAgMTkyXHRcbiAgIDE5M1x0XHRyZXNwb25zZSA6PSBtb2RlbHMuSW5pdGlhdGVDdXN0b21lckNhbGxSZXNwb25zZXtcbiAgIDE5NFx0XHRcdENhbGxJRDogICAgICBjYWxsLklELFxuICAgMTk1XHRcdFx0Q2hhbm5lbE5hbWU6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAxOTZcdFx0XHRUb2tlbjogICAgICAgY3VzdG9tZXJUb2tlbi5Ub2tlbixcbiAgIDE5N1x0XHRcdFN0YXR1czogICAgICBzdHJpbmcoY2FsbC5TdGF0dXMpLFxuICAgMTk4XHRcdFx0TWVzc2FnZTogICAgIFwiQ3VzdG9tZXIgY2FsbCBpbml0aWF0ZWQgc3VjY2Vzc2Z1bGx5LiBBSSBpcyByZWFkeSB0byBhc3Npc3QuXCIsXG4gICAxOTlcdFx0XHRBSUluZm86ICAgICAgYWlJbmZvLFxuICAgMjAwXHRcdH1cbiAgIDIwMVx0XG4gICAyMDJcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQ3JlYXRlZCwgcmVzcG9uc2UpXG4gICAyMDNcdH1cbi4uLlxuICAgMjQ4XHRcbiAgIDI0OVx0XHQvLyBKb2luIHRoZSBjYWxsXG4gICAyNTBcdFx0Y2FsbCwgZXJyIDo9IGNoLmNhbGxNYW5hZ2VyLkpvaW5DYWxsKHJlcXVlc3QuQ2FsbElELCByZXF1ZXN0LkN1c3RvbWVySUQpXG4gICAyNTFcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAyNTJcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgIDI1M1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGpvaW4gY2FsbFwiLFxuICAgMjU0XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAyNTVcdFx0XHR9KVxuICAgMjU2XHRcdFx0cmV0dXJuXG4gICAyNTdcdFx0fVxuICAgMjU4XHRcbiAgIDI1OVx0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGN1c3RvbWVyXG4gICAyNjBcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCByZXF1ZXN0LkN1c3RvbWVySUQsIDEpIC8vIFJvbGUgMSA9IHB1Ymxpc2hlclxuICAgMjYxXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMjYyXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAyNjNcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSBBZ29yYSB0b2tlblwiLFxuICAgMjY0XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAyNjVcdFx0XHR9KVxuICAgMjY2XHRcdFx0cmV0dXJuXG4gICAyNjdcdFx0fVxuICAgMjY4XHRcbiAgIDI2OVx0XHRyZXNwb25zZSA6PSBtb2RlbHMuSm9pbkNhbGxSZXNwb25zZXtcbiAgIDI3MFx0XHRcdENoYW5uZWxOYW1lOiBjYWxsLkNoYW5uZWxOYW1lLFxuICAgMjcxXHRcdFx0VG9rZW46ICAgICAgIHRva2VuLlRva2VuLFxuICAgMjcyXHRcdFx0Q2FsbEluZm86ICAgICpjYWxsLFxuICAgMjczXHRcdH1cbiAgIDI3NFx0XG4gICAyNzVcdFx0Yy5KU09OKGh0dHAuU3RhdHVzT0ssIHJlc3BvbnNlKVxuICAgMjc2XHR9XG4uLi5cbiAgIDM2OVx0XG4gICAzNzBcdFx0Yy5KU09OKGh0dHAuU3RhdHVzT0ssIHN0YXRzKVxuICAgMzcxXHR9XG4gICAzNzJcdFxuICAgMzczXHQvLyBHZW5lcmF0ZVRva2VuIGhhbmRsZXMgUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3Rva2VuXG4gICAzNzRcdGZ1bmMgKGNoICpDYWxsSGFuZGxlcikgR2VuZXJhdGVUb2tlbihjICpnaW4uQ29udGV4dCkge1xuICAgMzc1XHRcdGNhbGxJRCA6PSBjLlBhcmFtKFwiY2FsbF9pZFwiKVxuICAgMzc2XHRcdGlmIGNhbGxJRCA9PSBcIlwiIHtcbiAgIDM3N1x0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMzc4XHRcdFx0XHRcImVycm9yXCI6IFwiY2FsbF9pZCBpcyByZXF1aXJlZFwiLFxuICAgMzc5XHRcdFx0fSlcbiAgIDM4MFx0XHRcdHJldHVyblxuICAgMzgxXHRcdH1cbiAgIDM4Mlx0XG4gICAzODNcdFx0dmFyIHJlcXVlc3Qgc3RydWN0IHtcbiAgIDM4NFx0XHRcdFVzZXJJRCBzdHJpbmcgYGpzb246XCJ1c2VyX2lkXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAzODVcdFx0XHRSb2xlICAgaW50ICAgIGBqc29uOlwicm9sZVwiYFxuICAgMzg2XHRcdH1cbiAgIDM4N1x0XG4gICAzODhcdFx0aWYgZXJyIDo9IGMuU2hvdWxkQmluZEpTT04oJnJlcXVlc3QpOyBlcnIgIT0gbmlsIHtcbiAgIDM4OVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMzkwXHRcdFx0XHRcImVycm9yXCI6ICAgXCJJbnZhbGlkIHJlcXVlc3QgZm9ybWF0XCIsXG4gICAzOTFcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDM5Mlx0XHRcdH0pXG4gICAzOTNcdFx0XHRyZXR1cm5cbiAgIDM5NFx0XHR9XG4gICAzOTVcdFxuICAgMzk2XHRcdC8vIERlZmF1bHQgcm9sZSB0byBwdWJsaXNoZXIgaWYgbm90IHNwZWNpZmllZFxuICAgMzk3XHRcdGlmIHJlcXVlc3QuUm9sZSA9PSAwIHtcbiAgIDM5OFx0XHRcdHJlcXVlc3QuUm9sZSA9IDFcbiAgIDM5OVx0XHR9XG4gICA0MDBcdFxuICAgNDAxXHRcdC8vIEdldCBjYWxsIHRvIHZhbGlkYXRlIGl0IGV4aXN0c1xuICAgNDAyXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5HZXRDYWxsKGNhbGxJRClcbiAgIDQwM1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDQwNFx0XHRcdGMuSlNPTihodHRwLlN0YXR1c05vdEZvdW5kLCBnaW4uSHtcbiAgIDQwNVx0XHRcdFx0XCJlcnJvclwiOiAgIFwiQ2FsbCBub3QgZm91bmRcIixcbiAgIDQwNlx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgNDA3XHRcdFx0fSlcbiAgIDQwOFx0XHRcdHJldHVyblxuICAgNDA5XHRcdH1cbiAgIDQxMFx0XG4gICA0MTFcdFx0Ly8gR2VuZXJhdGUgdG9rZW5cbiAgIDQxMlx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIHJlcXVlc3QuVXNlcklELCByZXF1ZXN0LlJvbGUpXG4gICA0MTNcdFx0aWYgZXJyICE9IG5pbCB7XG4gICA0MTRcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDQxNVx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIHRva2VuXCIsXG4gICA0MTZcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDQxN1x0XHRcdH0pXG4gICA0MThcdFx0XHRyZXR1cm5cbiAgIDQxOVx0XHR9XG4gICA0MjBcdFxuICAgNDIxXHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBnaW4uSHtcbiAgIDQyMlx0XHRcdFwidG9rZW5cIjogICAgICAgIHRva2VuLlRva2VuLFxuICAgNDIzXHRcdFx0XCJleHBpcmVzX2F0XCI6ICAgdG9rZW4uRXhwaXJlc0F0LFxuICAgNDI0XHRcdFx0XCJjaGFubmVsX25hbWVcIjogY2FsbC5DaGFubmVsTmFtZSxcbiAgIDQyNVx0XHRcdFwidXNlcl9pZFwiOiAgICAgIHJlcXVlc3QuVXNlcklELFxuICAgNDI2XHRcdFx0XCJyb2xlXCI6ICAgICAgICAgcmVxdWVzdC5Sb2xlLFxuICAgNDI3XHRcdH0pXG4gICA0MjhcdH1cbi4uLlxuUGF0aDogbWFpbi5nb1xuLi4uXG4gICAgNjVcdFxuICAgIDY2XHRcdC8vIEFQSSByb3V0ZXNcbiAgICA2N1x0XHRhcGkgOj0gcm91dGVyLkdyb3VwKFwiL2FwaVwiKVxuICAgIDY4XHRcdHtcbiAgICA2OVx0XHRcdC8vIENhbGwgbWFuYWdlbWVudCByb3V0ZXNcbiAgICA3MFx0XHRcdGNhbGxzIDo9IGFwaS5Hcm91cChcIi9jYWxsc1wiKVxuICAgIDcxXHRcdFx0e1xuICAgIDcyXHRcdFx0XHRjYWxscy5QT1NUKFwiL2luaXRpYXRlXCIsIGNhbGxIYW5kbGVyLkluaXRpYXRlQ2FsbCkgICAgICAgICAgICAgICAgICAgIC8vIEFJIGNhbGxzIGN1c3RvbWVyXG4gICAgNzNcdFx0XHRcdGNhbGxzLlBPU1QoXCIvY3VzdG9tZXItaW5pdGlhdGVcIiwgY2FsbEhhbmRsZXIuSW5pdGlhdGVDdXN0b21lckNhbGwpICAgLy8gQ3VzdG9tZXIgY2FsbHMgQUlcbiAgICA3NFx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC9qb2luXCIsIGNhbGxIYW5kbGVyLkpvaW5DYWxsKVxuICAgIDc1XHRcdFx0XHRjYWxscy5HRVQoXCIvOmNhbGxfaWRcIiwgY2FsbEhhbmRsZXIuR2V0Q2FsbClcbiAgICA3Nlx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC9lbmRcIiwgY2FsbEhhbmRsZXIuRW5kQ2FsbClcbiAgICA3N1x0XHRcdFx0Y2FsbHMuR0VUKFwiL2FjdGl2ZVwiLCBjYWxsSGFuZGxlci5HZXRBY3RpdmVDYWxscylcbiAgICA3OFx0XHRcdFx0Y2FsbHMuR0VUKFwiLzpjYWxsX2lkL3N0YXRzXCIsIGNhbGxIYW5kbGVyLkdldENhbGxTdGF0cylcbiAgICA3OVx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC90b2tlblwiLCBjYWxsSGFuZGxlci5HZW5lcmF0ZVRva2VuKVxuICAgIDgwXHRcdFx0fVxuICAgIDgxXHRcdH1cbiAgICA4Mlx0XG4gICAgODNcdFx0Ly8gV2VsY29tZSByb3V0ZVxuICAgIDg0XHRcdHJvdXRlci5HRVQoXCIvXCIsIGZ1bmMoYyAqZ2luLkNvbnRleHQpIHtcbiAgICA4NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBnaW4uSHtcbiAgICA4Nlx0XHRcdFx0XCJtZXNzYWdlXCI6IFwi8J+kliBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIixcbiAgICA4N1x0XHRcdFx0XCJ2ZXJzaW9uXCI6IFwiMS4wLjBcIixcbiAgICA4OFx0XHRcdFx0XCJzdGF0dXNcIjogIFwicnVubmluZ1wiLFxuICAgIDg5XHRcdFx0XHRcImVuZHBvaW50c1wiOiBnaW4uSHtcbiAgICA5MFx0XHRcdFx0XHRcImhlYWx0aFwiOiAgICAgICAgICAgICAgICBcIkdFVCAvaGVhbHRoXCIsXG4gICAgOTFcdFx0XHRcdFx0XCJpbml0aWF0ZV9jYWxsXCI6ICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvaW5pdGlhdGVcIixcbiAgICA5Mlx0XHRcdFx0XHRcImN1c3RvbWVyX2luaXRpYXRlXCI6ICAgICBcIlBPU1QgL2FwaS9jYWxscy9jdXN0b21lci1pbml0aWF0ZVwiLFxuICAgIDkzXHRcdFx0XHRcdFwiam9pbl9jYWxsXCI6ICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2pvaW5cIixcbiAgICA5NFx0XHRcdFx0XHRcImdldF9jYWxsXCI6ICAgICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkXCIsXG4gICAgOTVcdFx0XHRcdFx0XCJlbmRfY2FsbFwiOiAgICAgICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvZW5kXCIsXG4gICAgOTZcdFx0XHRcdFx0XCJhY3RpdmVfY2FsbHNcIjogICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy9hY3RpdmVcIixcbiAgICA5N1x0XHRcdFx0XHRcImNhbGxfc3RhdHNcIjogICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3N0YXRzXCIsXG4gICAgOThcdFx0XHRcdFx0XCJnZW5lcmF0ZV90b2tlblwiOiAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvdG9rZW5cIixcbiAgICA5OVx0XHRcdFx0fSxcbiAgIDEwMFx0XHRcdH0pXG4gICAxMDFcdFx0fSlcbiAgIDEwMlx0XG4gICAxMDNcdFx0bG9nLlByaW50ZihcIvCfm6PvuI8gUm91dGVzIGNvbmZpZ3VyZWQgc3VjY2Vzc2Z1bGx5XCIpXG4gICAxMDRcdFx0cmV0dXJuIHJvdXRlclxuICAgMTA1XHR9XG4uLi5cblBhdGg6IHNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcImZtdFwiXG4gICAgIDZcdFx0XCJsb2dcIlxuICAgICA3XHRcdFwic3RyY29udlwiXG4gICAgIDhcdFx0XCJ0aW1lXCJcbiAgICAgOVx0XG4gICAgMTBcdFx0XCJnaXRodWIuY29tL0Fnb3JhSU8vVG9vbHMvRHluYW1pY0tleS9BZ29yYUR5bmFtaWNLZXkvZ28vc3JjL3J0Y3Rva2VuYnVpbGRlcjJcIlxuICAgIDExXHQpXG4gICAgMTJcdFxuICAgIDEzXHQvLyBBZ29yYVNlcnZpY2UgaGFuZGxlcyBBZ29yYSBSVEMgb3BlcmF0aW9uc1xuICAgIDE0XHR0eXBlIEFnb3JhU2VydmljZSBzdHJ1Y3Qge1xuICAgIDE1XHRcdGNvbmZpZyAqY29uZmlnLkNvbmZpZ1xuICAgIDE2XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY29udGFpbjpcbiAgICAxN1x0XHQvLyAtIEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAxOFx0XHQvLyAtIE1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDE5XHRcdC8vIC0gQWN0aXZlIGNvbm5lY3Rpb25zIG1hcFxuICAgIDIwXHRcdC8vIC0gQXVkaW8vVmlkZW8gb2JzZXJ2ZXJzXG4gICAgMjFcdH1cbiAgICAyMlx0XG4gICAgMjNcdC8vIFRva2VuUmVxdWVzdCByZXByZXNlbnRzIGEgcmVxdWVzdCBmb3IgQWdvcmEgdG9rZW5cbiAgICAyNFx0dHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcbiAgICAyNVx0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICAyNlx0XHRVc2VySUQgICAgICBzdHJpbmcgYGpzb246XCJ1c2VyX2lkXCJgXG4gICAgMjdcdFx0Um9sZSAgICAgICAgaW50ICAgIGBqc29uOlwicm9sZVwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxuICAgIDI4XHR9XG4gICAgMjlcdFxuICAgIDMwXHQvLyBUb2tlblJlc3BvbnNlIHJlcHJlc2VudHMgQWdvcmEgdG9rZW4gcmVzcG9uc2VcbiAgICAzMVx0dHlwZSBUb2tlblJlc3BvbnNlIHN0cnVjdCB7XG4gICAgMzJcdFx0VG9rZW4gICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAgMzNcdFx0RXhwaXJlc0F0IGludDY0ICBganNvbjpcImV4cGlyZXNfYXRcImBcbiAgICAzNFx0fVxuICAgIDM1XHRcbiAgICAzNlx0Ly8gTmV3QWdvcmFTZXJ2aWNlIGNyZWF0ZXMgYSBuZXcgQWdvcmEgc2VydmljZSBpbnN0YW5jZVxuICAgIDM3XHRmdW5jIE5ld0Fnb3JhU2VydmljZShjZmcgKmNvbmZpZy5Db25maWcpICpBZ29yYVNlcnZpY2Uge1xuICAgIDM4XHRcdHNlcnZpY2UgOj0gJkFnb3JhU2VydmljZXtcbiAgICAzOVx0XHRcdGNvbmZpZzogY2ZnLFxuICAgIDQwXHRcdH1cbiAgICA0MVx0XG4gICAgNDJcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgaW5pdGlhbGl6ZSBBZ29yYSBTREsgaGVyZTpcbiAgICA0M1x0XHQvLyAxLiBJbml0aWFsaXplIEFnb3JhIHNlcnZpY2Ugd2l0aCBhcHAgSURcbiAgICA0NFx0XHQvLyAyLiBDcmVhdGUgbWVkaWEgbm9kZSBmYWN0b3J5XG4gICAgNDVcdFx0Ly8gMy4gU2V0IHVwIGF1ZGlvL3ZpZGVvIHByb2Nlc3NpbmcgY2FsbGJhY2tzXG4gICAgNDZcdFxuICAgIDQ3XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIHNlcnZpY2UgaW5pdGlhbGl6ZWQgd2l0aCBBcHAgSUQ6ICVzXCIsIGNmZy5BZ29yYS5BcHBJRClcbiAgICA0OFx0XHRyZXR1cm4gc2VydmljZVxuICAgIDQ5XHR9XG4gICAgNTBcdFxuICAgIDUxXHQvLyBHZW5lcmF0ZVRva2VuIGdlbmVyYXRlcyBhbiBBZ29yYSBSVEMgdG9rZW4gZm9yIGNoYW5uZWwgYWNjZXNzXG4gICAgNTJcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEdlbmVyYXRlVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50KSAoKlRva2VuUmVzcG9uc2UsIGVycm9yKSB7XG4gICAgNTNcdFx0bG9nLlByaW50ZihcIvCflJEgR2VuZXJhdGluZyBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXMsIHVzZXI6ICVzLCByb2xlOiAlZFwiLCBjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDU0XHRcbiAgICA1NVx0XHQvLyBUb2tlbiBleHBpcmVzIGluIDI0IGhvdXJzXG4gICAgNTZcdFx0ZXhwaXJhdGlvblRpbWUgOj0gdGltZS5Ob3coKS5BZGQoMjQgKiB0aW1lLkhvdXIpLlVuaXgoKVxuICAgIDU3XHRcbiAgICA1OFx0XHR2YXIgdG9rZW4gc3RyaW5nXG4gICAgNTlcdFx0dmFyIGVyciBlcnJvclxuICAgIDYwXHRcbiAgICA2MVx0XHRpZiBhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUgIT0gXCJcIiB7XG4gICAgNjJcdFx0XHQvLyBHZW5lcmF0ZSByZWFsIHRva2VuIHdpdGggb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDYzXHRcdFx0dG9rZW4sIGVyciA9IGFzLmdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHVpbnQzMihleHBpcmF0aW9uVGltZSkpXG4gICAgNjRcdFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2NVx0XHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlbjogJXdcIiwgZXJyKVxuICAgIDY2XHRcdFx0fVxuICAgIDY3XHRcdFx0bG9nLlByaW50ZihcIuKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgNjhcdFx0fSBlbHNlIHtcbiAgICA2OVx0XHRcdC8vIEdlbmVyYXRlIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAgNzBcdFx0XHR0b2tlbiA9IGFzLmdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNzFcdFx0XHRsb2cuUHJpbnRmKFwi4pqg77iPIFtNT0NLXSBVc2luZyBtb2NrIHRva2VuIChubyBhcHAgY2VydGlmaWNhdGUgcHJvdmlkZWQpXCIpXG4gICAgNzJcdFx0fVxuICAgIDczXHRcbiAgICA3NFx0XHRyZXNwb25zZSA6PSAmVG9rZW5SZXNwb25zZXtcbiAgICA3NVx0XHRcdFRva2VuOiAgICAgdG9rZW4sXG4gICAgNzZcdFx0XHRFeHBpcmVzQXQ6IGV4cGlyYXRpb25UaW1lLFxuICAgIDc3XHRcdH1cbiAgICA3OFx0XG4gICAgNzlcdFx0bG9nLlByaW50ZihcIuKchSBUb2tlbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5IGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgICA4MFx0XHRyZXR1cm4gcmVzcG9uc2UsIG5pbFxuICAgIDgxXHR9XG4gICAgODJcdFxuICAgIDgzXHQvLyBnZW5lcmF0ZVJlYWxUb2tlbiBnZW5lcmF0ZXMgYSByZWFsIEFnb3JhIHRva2VuIHVzaW5nIHRoZSBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAgODRcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCwgZXhwaXJhdGlvbiB1aW50MzIpIChzdHJpbmcsIGVycm9yKSB7XG4gICAgODVcdFx0Ly8gQ29udmVydCB1c2VySUQgdG8gdWludDMyIChBZ29yYSBleHBlY3RzIG51bWVyaWMgVUlEKVxuICAgIDg2XHRcdHZhciB1aWQgdWludDMyXG4gICAgODdcdFx0aWYgdXNlcklEICE9IFwiXCIge1xuICAgIDg4XHRcdFx0aWYgcGFyc2VkVUlELCBlcnIgOj0gc3RyY29udi5QYXJzZVVpbnQodXNlcklELCAxMCwgMzIpOyBlcnIgPT0gbmlsIHtcbiAgICA4OVx0XHRcdFx0dWlkID0gdWludDMyKHBhcnNlZFVJRClcbiAgICA5MFx0XHRcdH0gZWxzZSB7XG4gICAgOTFcdFx0XHRcdC8vIElmIHVzZXJJRCBpcyBub3QgbnVtZXJpYywgZ2VuZXJhdGUgYSBoYXNoLWJhc2VkIFVJRFxuICAgIDkyXHRcdFx0XHR1aWQgPSB1aW50MzIobGVuKHVzZXJJRCkpIC8vIFNpbXBsZSBmYWxsYmFjaywgaW4gcHJvZHVjdGlvbiB1c2UgYSBwcm9wZXIgaGFzaFxuICAgIDkzXHRcdFx0fVxuICAgIDk0XHRcdH0gZWxzZSB7XG4gICAgOTVcdFx0XHR1aWQgPSAwIC8vIExldCBBZ29yYSBhc3NpZ24gYSBVSURcbiAgICA5Nlx0XHR9XG4gICAgOTdcdFxuICAgIDk4XHRcdC8vIENvbnZlcnQgcm9sZSB0byBBZ29yYSByb2xlIHR5cGVcbiAgICA5OVx0XHR2YXIgYWdvcmFSb2xlIHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVxuICAgMTAwXHRcdGlmIHJvbGUgPT0gMSB7XG4gICAxMDFcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVQdWJsaXNoZXJcbiAgIDEwMlx0XHR9IGVsc2Uge1xuICAgMTAzXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlU3Vic2NyaWJlclxuICAgMTA0XHRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdFx0Ly8gR2VuZXJhdGUgdG9rZW4gdXNpbmcgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgMTA3XHRcdHRva2VuLCBlcnIgOj0gcnRjdG9rZW5idWlsZGVyMi5CdWlsZFRva2VuV2l0aFVpZChcbiAgIDEwOFx0XHRcdGFzLmNvbmZpZy5BZ29yYS5BcHBJRCxcbiAgIDEwOVx0XHRcdGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSxcbiAgIDExMFx0XHRcdGNoYW5uZWxOYW1lLFxuICAgMTExXHRcdFx0dWlkLFxuICAgMTEyXHRcdFx0YWdvcmFSb2xlLFxuICAgMTEzXHRcdFx0ZXhwaXJhdGlvbixcbiAgIDExNFx0XHRcdGV4cGlyYXRpb24sIC8vIFByaXZpbGVnZSBleHBpcmVkIHRpbWVzdGFtcCAoc2FtZSBhcyB0b2tlbiBleHBpcmF0aW9uKVxuICAgMTE1XHRcdClcbiAgIDExNlx0XG4gICAxMTdcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxMThcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byBidWlsZCBBZ29yYSB0b2tlbjogJXdcIiwgZXJyKVxuICAgMTE5XHRcdH1cbiAgIDEyMFx0XG4gICAxMjFcdFx0cmV0dXJuIHRva2VuLCBuaWxcbiAgIDEyMlx0fVxuICAgMTIzXHRcbiAgIDEyNFx0Ly8gZ2VuZXJhdGVNb2NrVG9rZW4gZ2VuZXJhdGVzIGEgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgIDEyNVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2VuZXJhdGVNb2NrVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50KSBzdHJpbmcge1xuICAgMTI2XHRcdHRpbWVzdGFtcCA6PSB0aW1lLk5vdygpLlVuaXgoKVxuICAgMTI3XHRcdHJldHVybiBmbXQuU3ByaW50ZihcIm1vY2tfdG9rZW5fJXNfJXNfJWRfJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSwgdGltZXN0YW1wKVxuICAgMTI4XHR9XG4uLi5cblBhdGg6IG1vZGVscy9jYWxsLmdvXG4uLi5cbiAgICAyN1x0XG4gICAgMjhcdC8vIENhbGwgcmVwcmVzZW50cyBhIGN1c3RvbWVyIHN1cHBvcnQgY2FsbCBzZXNzaW9uXG4gICAgMjlcdHR5cGUgQ2FsbCBzdHJ1Y3Qge1xuICAgIDMwXHRcdElEICAgICAgICAgIHN0cmluZyAgICAgICBganNvbjpcImlkXCJgXG4gICAgMzFcdFx0Q3VzdG9tZXJJRCAgc3RyaW5nICAgICAgIGBqc29uOlwiY3VzdG9tZXJfaWRcImBcbiAgICAzMlx0XHRDaGFubmVsTmFtZSBzdHJpbmcgICAgICAgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICAzM1x0XHRTdGF0dXMgICAgICBDYWxsU3RhdHVzICAgYGpzb246XCJzdGF0dXNcImBcbiAgICAzNFx0XHRUeXBlICAgICAgICBDYWxsVHlwZSAgICAgYGpzb246XCJ0eXBlXCJgXG4gICAgMzVcdFx0U3RhcnRUaW1lICAgdGltZS5UaW1lICAgIGBqc29uOlwic3RhcnRfdGltZVwiYFxuICAgIDM2XHRcdEVuZFRpbWUgICAgICp0aW1lLlRpbWUgICBganNvbjpcImVuZF90aW1lLG9taXRlbXB0eVwiYFxuICAgIDM3XHRcdER1cmF0aW9uICAgIGludDY0ICAgICAgICBganNvbjpcImR1cmF0aW9uXCJgIC8vIGluIHNlY29uZHNcbiAgICAzOFx0XHRUcmFuc2NyaXB0ICBbXU1lc3NhZ2UgICAgYGpzb246XCJ0cmFuc2NyaXB0XCJgXG4gICAgMzlcdFx0TWV0YWRhdGEgICAgQ2FsbE1ldGFkYXRhIGBqc29uOlwibWV0YWRhdGFcImBcbiAgICA0MFx0fVxuICAgIDQxXHRcbiAgICA0Mlx0Ly8gTWVzc2FnZSByZXByZXNlbnRzIGEgc2luZ2xlIG1lc3NhZ2UgaW4gdGhlIGNvbnZlcnNhdGlvblxuICAgIDQzXHR0eXBlIE1lc3NhZ2Ugc3RydWN0IHtcbiAgICA0NFx0XHRJRCAgICAgICAgc3RyaW5nICAgIGBqc29uOlwiaWRcImBcbiAgICA0NVx0XHRTcGVha2VyICAgc3RyaW5nICAgIGBqc29uOlwic3BlYWtlclwiYCAvLyBcImFpXCIgb3IgXCJjdXN0b21lclwiXG4gICAgNDZcdFx0Q29udGVudCAgIHN0cmluZyAgICBganNvbjpcImNvbnRlbnRcImBcbiAgICA0N1x0XHRUaW1lc3RhbXAgdGltZS5UaW1lIGBqc29uOlwidGltZXN0YW1wXCJgXG4gICAgNDhcdFx0VHlwZSAgICAgIHN0cmluZyAgICBganNvbjpcInR5cGVcImAgLy8gXCJ0ZXh0XCIsIFwiYXVkaW9cIlxuICAgIDQ5XHR9XG4gICAgNTBcdFxuICAgIDUxXHQvLyBDYWxsTWV0YWRhdGEgY29udGFpbnMgYWRkaXRpb25hbCBpbmZvcm1hdGlvbiBhYm91dCB0aGUgY2FsbFxuICAgIDUyXHR0eXBlIENhbGxNZXRhZGF0YSBzdHJ1Y3Qge1xuICAgIDUzXHRcdFB1cnBvc2UgICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJwdXJwb3NlXCJgXG4gICAgNTRcdFx0Q3VzdG9tZXJJbmZvICBDdXN0b21lckluZm8gICAgICBganNvbjpcImN1c3RvbWVyX2luZm9cImBcbiAgICA1NVx0XHRBSVBlcnNvbmFsaXR5IHN0cmluZyAgICAgICAgICAgIGBqc29uOlwiYWlfcGVyc29uYWxpdHlcImBcbiAgICA1Nlx0XHRMYW5ndWFnZSAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwibGFuZ3VhZ2VcImBcbiAgICA1N1x0XHRUYWdzICAgICAgICAgIFtdc3RyaW5nICAgICAgICAgIGBqc29uOlwidGFnc1wiYFxuICAgIDU4XHRcdEN1c3RvbUZpZWxkcyAgbWFwW3N0cmluZ11zdHJpbmcgYGpzb246XCJjdXN0b21fZmllbGRzXCJgXG4gICAgNTlcdH1cbiAgICA2MFx0XG4gICAgNjFcdC8vIEN1c3RvbWVySW5mbyBjb250YWlucyBjdXN0b21lciBkZXRhaWxzXG4gICAgNjJcdHR5cGUgQ3VzdG9tZXJJbmZvIHN0cnVjdCB7XG4gICAgNjNcdFx0TmFtZSAgICAgICAgc3RyaW5nIGBqc29uOlwibmFtZVwiYFxuICAgIDY0XHRcdEVtYWlsICAgICAgIHN0cmluZyBganNvbjpcImVtYWlsXCJgXG4gICAgNjVcdFx0UGhvbmUgICAgICAgc3RyaW5nIGBqc29uOlwicGhvbmVcImBcbiAgICA2Nlx0XHRBY2NvdW50SUQgICBzdHJpbmcgYGpzb246XCJhY2NvdW50X2lkXCJgXG4gICAgNjdcdFx0UHJlZmVyZW5jZXMgbWFwW3N0cmluZ11zdHJpbmcgYGpzb246XCJwcmVmZXJlbmNlc1wiYFxuICAgIDY4XHR9XG4gICAgNjlcdFxuICAgIDcwXHQvLyBJbml0aWF0ZUNhbGxSZXF1ZXN0IHJlcHJlc2VudHMgdGhlIHJlcXVlc3QgdG8gc3RhcnQgYSBuZXcgY2FsbFxuICAgIDcxXHR0eXBlIEluaXRpYXRlQ2FsbFJlcXVlc3Qgc3RydWN0IHtcbiAgICA3Mlx0XHRDdXN0b21lcklEICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwiY3VzdG9tZXJfaWRcIiBiaW5kaW5nOlwicmVxdWlyZWRcImBcbiAgICA3M1x0XHRQdXJwb3NlICAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwicHVycG9zZVwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgIDc0XHRcdEN1c3RvbWVySW5mbyAgQ3VzdG9tZXJJbmZvICAgICAgYGpzb246XCJjdXN0b21lcl9pbmZvXCJgXG4gICAgNzVcdFx0QUlQZXJzb25hbGl0eSBzdHJpbmcgICAgICAgICAgICBganNvbjpcImFpX3BlcnNvbmFsaXR5XCJgXG4gICAgNzZcdFx0TGFuZ3VhZ2UgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcImxhbmd1YWdlXCJgXG4gICAgNzdcdFx0Q3VzdG9tRmllbGRzICBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcImN1c3RvbV9maWVsZHNcImBcbiAgICA3OFx0fVxuICAgIDc5XHRcbiAgICA4MFx0Ly8gSW5pdGlhdGVDYWxsUmVzcG9uc2UgcmVwcmVzZW50cyB0aGUgcmVzcG9uc2Ugd2hlbiBpbml0aWF0aW5nIGEgY2FsbFxuICAgIDgxXHR0eXBlIEluaXRpYXRlQ2FsbFJlc3BvbnNlIHN0cnVjdCB7XG4gICAgODJcdFx0Q2FsbElEICAgICAgc3RyaW5nIGBqc29uOlwiY2FsbF9pZFwiYFxuICAgIDgzXHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgIDg0XHRcdFRva2VuICAgICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAgODVcdFx0U3RhdHVzICAgICAgc3RyaW5nIGBqc29uOlwic3RhdHVzXCJgXG4gICAgODZcdFx0TWVzc2FnZSAgICAgc3RyaW5nIGBqc29uOlwibWVzc2FnZVwiYFxuICAgIDg3XHR9XG4gICAgODhcdFxuICAgIDg5XHQvLyBKb2luQ2FsbFJlcXVlc3QgcmVwcmVzZW50cyB0aGUgcmVxdWVzdCB3aGVuIGEgdXNlciBqb2lucyBhIGNhbGxcbiAgICA5MFx0dHlwZSBKb2luQ2FsbFJlcXVlc3Qgc3RydWN0IHtcbiAgICA5MVx0XHRDYWxsSUQgICAgIHN0cmluZyBganNvbjpcImNhbGxfaWRcImBcbiAgICA5Mlx0XHRDdXN0b21lcklEIHN0cmluZyBganNvbjpcImN1c3RvbWVyX2lkXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAgOTNcdH1cbiAgICA5NFx0XG4gICAgOTVcdC8vIEpvaW5DYWxsUmVzcG9uc2UgcmVwcmVzZW50cyB0aGUgcmVzcG9uc2Ugd2hlbiBqb2luaW5nIGEgY2FsbFxuICAgIDk2XHR0eXBlIEpvaW5DYWxsUmVzcG9uc2Ugc3RydWN0IHtcbiAgICA5N1x0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICA5OFx0XHRUb2tlbiAgICAgICBzdHJpbmcgYGpzb246XCJ0b2tlblwiYFxuICAgIDk5XHRcdENhbGxJbmZvICAgIENhbGwgICBganNvbjpcImNhbGxfaW5mb1wiYFxuICAgMTAwXHR9XG4uLi5cbiAgIDExMlx0XG4gICAxMTNcdC8vIEluaXRpYXRlQ3VzdG9tZXJDYWxsUmVzcG9uc2UgcmVwcmVzZW50cyB0aGUgcmVzcG9uc2Ugd2hlbiBjdXN0b21lciBpbml0aWF0ZXMgY2FsbCB0byBBSVxuICAgMTE0XHR0eXBlIEluaXRpYXRlQ3VzdG9tZXJDYWxsUmVzcG9uc2Ugc3RydWN0IHtcbiAgIDExNVx0XHRDYWxsSUQgICAgICBzdHJpbmcgYGpzb246XCJjYWxsX2lkXCJgXG4gICAxMTZcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAxMTdcdFx0VG9rZW4gICAgICAgc3RyaW5nIGBqc29uOlwidG9rZW5cImBcbiAgIDExOFx0XHRTdGF0dXMgICAgICBzdHJpbmcgYGpzb246XCJzdGF0dXNcImBcbiAgIDExOVx0XHRNZXNzYWdlICAgICBzdHJpbmcgYGpzb246XCJtZXNzYWdlXCJgXG4gICAxMjBcdFx0QUlJbmZvICAgICAgQUlJbmZvIGBqc29uOlwiYWlfaW5mb1wiYFxuICAgMTIxXHR9XG4gICAxMjJcdFxuICAgMTIzXHQvLyBBSUluZm8gY29udGFpbnMgaW5mb3JtYXRpb24gYWJvdXQgdGhlIEFJIGFnZW50XG4gICAxMjRcdHR5cGUgQUlJbmZvIHN0cnVjdCB7XG4gICAxMjVcdFx0TmFtZSAgICAgICAgc3RyaW5nIGBqc29uOlwibmFtZVwiYFxuICAgMTI2XHRcdFBlcnNvbmFsaXR5IHN0cmluZyBganNvbjpcInBlcnNvbmFsaXR5XCJgXG4gICAxMjdcdFx0TGFuZ3VhZ2UgICAgc3RyaW5nIGBqc29uOlwibGFuZ3VhZ2VcImBcbiAgIDEyOFx0XHRDYXBhYmlsaXRpZXMgW11zdHJpbmcgYGpzb246XCJjYXBhYmlsaXRpZXNcImBcbiAgIDEyOVx0fVxuLi4uXG4gICAxMzhcdFxuICAgMTM5XHQvLyBOZXdDYWxsIGNyZWF0ZXMgYSBuZXcgY2FsbCBpbnN0YW5jZVxuICAgMTQwXHRmdW5jIE5ld0NhbGwoY3VzdG9tZXJJRCwgcHVycG9zZSBzdHJpbmcsIGN1c3RvbWVySW5mbyBDdXN0b21lckluZm8pICpDYWxsIHtcbiAgIDE0MVx0XHRjYWxsSUQgOj0gdXVpZC5OZXcoKS5TdHJpbmcoKVxuICAgMTQyXHRcdGNoYW5uZWxOYW1lIDo9IFwiYWlfc3VwcG9ydF9cIiArIGNhbGxJRFxuICAgMTQzXHRcbiAgIDE0NFx0XHRyZXR1cm4gJkNhbGx7XG4gICAxNDVcdFx0XHRJRDogICAgICAgICAgY2FsbElELFxuICAgMTQ2XHRcdFx0Q3VzdG9tZXJJRDogIGN1c3RvbWVySUQsXG4gICAxNDdcdFx0XHRDaGFubmVsTmFtZTogY2hhbm5lbE5hbWUsXG4gICAxNDhcdFx0XHRTdGF0dXM6ICAgICAgQ2FsbFN0YXR1c1BlbmRpbmcsXG4gICAxNDlcdFx0XHRUeXBlOiAgICAgICAgQ2FsbFR5cGVBSVRvQ3VzdG9tZXIsIC8vIERlZmF1bHQgdG8gQUkgY2FsbGluZyBjdXN0b21lclxuICAgMTUwXHRcdFx0U3RhcnRUaW1lOiAgIHRpbWUuTm93KCksXG4gICAxNTFcdFx0XHRUcmFuc2NyaXB0OiAgbWFrZShbXU1lc3NhZ2UsIDApLFxuICAgMTUyXHRcdFx0TWV0YWRhdGE6IENhbGxNZXRhZGF0YXtcbiAgIDE1M1x0XHRcdFx0UHVycG9zZTogICAgICAgcHVycG9zZSxcbiAgIDE1NFx0XHRcdFx0Q3VzdG9tZXJJbmZvOiAgY3VzdG9tZXJJbmZvLFxuICAgMTU1XHRcdFx0XHRBSVBlcnNvbmFsaXR5OiBcImhlbHBmdWxcIixcbiAgIDE1Nlx0XHRcdFx0TGFuZ3VhZ2U6ICAgICAgXCJlblwiLFxuICAgMTU3XHRcdFx0XHRUYWdzOiAgICAgICAgICBtYWtlKFtdc3RyaW5nLCAwKSxcbiAgIDE1OFx0XHRcdFx0Q3VzdG9tRmllbGRzOiAgbWFrZShtYXBbc3RyaW5nXXN0cmluZyksXG4gICAxNTlcdFx0XHR9LFxuICAgMTYwXHRcdH1cbiAgIDE2MVx0fVxuICAgMTYyXHRcbiAgIDE2M1x0Ly8gTmV3Q3VzdG9tZXJDYWxsIGNyZWF0ZXMgYSBuZXcgY2FsbCBpbnN0YW5jZSB3aGVyZSBjdXN0b21lciBjYWxscyBBSVxuICAgMTY0XHRmdW5jIE5ld0N1c3RvbWVyQ2FsbChjdXN0b21lcklELCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTY1XHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNjZcdFx0Y2hhbm5lbE5hbWUgOj0gXCJjdXN0b21lcl9haV9cIiArIGNhbGxJRFxuICAgMTY3XHRcbiAgIDE2OFx0XHRyZXR1cm4gJkNhbGx7XG4gICAxNjlcdFx0XHRJRDogICAgICAgICAgY2FsbElELFxuICAgMTcwXHRcdFx0Q3VzdG9tZXJJRDogIGN1c3RvbWVySUQsXG4gICAxNzFcdFx0XHRDaGFubmVsTmFtZTogY2hhbm5lbE5hbWUsXG4gICAxNzJcdFx0XHRTdGF0dXM6ICAgICAgQ2FsbFN0YXR1c1BlbmRpbmcsXG4gICAxNzNcdFx0XHRUeXBlOiAgICAgICAgQ2FsbFR5cGVDdXN0b21lclRvQUksIC8vIEN1c3RvbWVyIGNhbGxpbmcgQUlcbiAgIDE3NFx0XHRcdFN0YXJ0VGltZTogICB0aW1lLk5vdygpLFxuICAgMTc1XHRcdFx0VHJhbnNjcmlwdDogIG1ha2UoW11NZXNzYWdlLCAwKSxcbiAgIDE3Nlx0XHRcdE1ldGFkYXRhOiBDYWxsTWV0YWRhdGF7XG4gICAxNzdcdFx0XHRcdFB1cnBvc2U6ICAgICAgIHB1cnBvc2UsXG4gICAxNzhcdFx0XHRcdEN1c3RvbWVySW5mbzogIGN1c3RvbWVySW5mbyxcbiAgIDE3OVx0XHRcdFx0QUlQZXJzb25hbGl0eTogXCJoZWxwZnVsXCIsXG4gICAxODBcdFx0XHRcdExhbmd1YWdlOiAgICAgIFwiZW5cIixcbiAgIDE4MVx0XHRcdFx0VGFnczogICAgICAgICAgbWFrZShbXXN0cmluZywgMCksXG4gICAxODJcdFx0XHRcdEN1c3RvbUZpZWxkczogIG1ha2UobWEuLi4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJlNzVkNDRjNi0xZWNmLTQyMzctYmM4My0xNGYzZmI4OGY1OWE7dG9vbHVfdnJ0eF8wMVE4QjNFUk5zNUZ2Y3Q2ckR1eFVwNnoiOnsicmVxdWVzdElkIjoiZTc1ZDQ0YzYtMWVjZi00MjM3LWJjODMtMTRmM2ZiODhmNTlhIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVE4QjNFUk5zNUZ2Y3Q2ckR1eFVwNnoiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJQcm9jZXNzIGxhdW5jaGVkIHdpdGggUElEIDI4IiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiNzdhZTc4NDUtZTdjOC00YjMxLTlmZmYtNzE5YmEwZWIzOTUzO3Rvb2x1X3ZydHhfMDFDQVVqaGhMYkpKYlNiUVJzWHQyeXdaIjp7InJlcXVlc3RJZCI6Ijc3YWU3ODQ1LWU3YzgtNGIzMS05ZmZmLTcxOWJhMGViMzk1MyIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFDQVVqaGhMYkpKYlNiUVJzWHQyeXdaIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBpcyB0aGUgb3V0cHV0IGZyb20gdGVybWluYWwgMjggKHN0YXR1czogY29tcGxldGVkKTpcbjxvdXRwdXQ+MjAyNS8wNS8yNiAxMzo1MjowMSDwn5qAIFN0YXJ0aW5nIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFxyXG4yMDI1LzA1LzI2IDEzOjUyOjAxIPCfk4sgU2VydmVyOiAwLjAuMC4wOjgwODBcclxuMjAyNS8wNS8yNiAxMzo1MjowMSDwn46vIEFnb3JhIEFwcCBJRDogMDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcclxuMjAyNS8wNS8yNiAxMzo1MjowMSDwn46vIEFnb3JhIHNlcnZpY2UgaW5pdGlhbGl6ZWQgd2l0aCBBcHAgSUQ6IDAxNDUyMzQ4MGQ5NTQ0YmU4MDBhMDE0ZjM1YmRjZmFjXHJcbjIwMjUvMDUvMjYgMTM6NTI6MDEg8J+bo++4jyBSb3V0ZXMgY29uZmlndXJlZCBzdWNjZXNzZnVsbHlcclxuMjAyNS8wNS8yNiAxMzo1MjowMSDinIUgU2VydmVyIHN0YXJ0aW5nIG9uIDAuMC4wLjA6ODA4MFxyXG4yMDI1LzA1LzI2IDEzOjUyOjAxIOKdjCBGYWlsZWQgdG8gc3RhcnQgc2VydmVyOiBsaXN0ZW4gdGNwIDAuMC4wLjA6ODA4MDogYmluZDogYWRkcmVzcyBhbHJlYWR5IGluIHVzZVxyXG5leGl0IHN0YXR1cyAxXHJcbjwvb3V0cHV0PlxuPHJldHVybi1jb2RlPlxuMVxuPC9yZXR1cm4tY29kZT5cbiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjIzNWUzZGU4LThjNzUtNDE5Yy1iYTQyLTVkMDBlYzUyNmQyZjt0b29sdV92cnR4XzAxRW9CM21OUENIaldna1g0SzdCbWlBZSI6eyJyZXF1ZXN0SWQiOiIyMzVlM2RlOC04Yzc1LTQxOWMtYmE0Mi01ZDAwZWM1MjZkMmYiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxRW9CM21OUENIaldna1g0SzdCbWlBZSIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IkhlcmUgYXJlIHRoZSByZXN1bHRzIGZyb20gZXhlY3V0aW5nIHRoZSBjb21tYW5kLlxuPHJldHVybi1jb2RlPlxuMFxuPC9yZXR1cm4tY29kZT5cbjxzdGRvdXQ+XG5cbjwvc3Rkb3V0PlxuPHN0ZGVycj5cblxuPC9zdGRlcnI+IiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiOGIwOWMyZmUtYjdhNy00NjMwLThhZjYtMzIxOTMzY2Q0NzI1O3Rvb2x1X3ZydHhfMDEzN3EzVHlrWUJRTWpLUEVQMkVCdzQ3Ijp7InJlcXVlc3RJZCI6IjhiMDljMmZlLWI3YTctNDYzMC04YWY2LTMyMTkzM2NkNDcyNSIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDEzN3EzVHlrWUJRTWpLUEVQMkVCdzQ3IiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiUHJvY2VzcyBsYXVuY2hlZCB3aXRoIFBJRCAzMCIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjI5NTdhYTZlLTg1NzItNGNlMS04ZTM1LWE3NTc4M2E5Zjc4Yjt0b29sdV92cnR4XzAxVmRYc2o5aHhkYm5xN1U0M25aYkVkUiI6eyJyZXF1ZXN0SWQiOiIyOTU3YWE2ZS04NTcyLTRjZTEtOGUzNS1hNzU3ODNhOWY3OGIiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxVmRYc2o5aHhkYm5xN1U0M25aYkVkUiIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IkhlcmUgaXMgdGhlIG91dHB1dCBmcm9tIHRlcm1pbmFsIDMwIChzdGF0dXM6IHN0aWxsIHJ1bm5pbmcpOlxuPG91dHB1dD4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfmoAgU3RhcnRpbmcgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXHJcbjIwMjUvMDUvMjYgMTM6NTQ6MDAg8J+TiyBTZXJ2ZXI6IDAuMC4wLjA6ODA4MFxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfjq8gQWdvcmEgQXBwIElEOiAwMTQ1MjM0ODBkOTU0NGJlODAwYTAxNGYzNWJkY2ZhY1xyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfjq8gQWdvcmEgc2VydmljZSBpbml0aWFsaXplZCB3aXRoIEFwcCBJRDogMDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcclxuMjAyNS8wNS8yNiAxMzo1NDowMCDwn5uj77iPIFJvdXRlcyBjb25maWd1cmVkIHN1Y2Nlc3NmdWxseVxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIOKchSBTZXJ2ZXIgc3RhcnRpbmcgb24gMC4wLjAuMDo4MDgwXHJcbjwvb3V0cHV0PlxuIiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiYjYzZjE0MGItNjJjMi00NzcwLTg2OGMtZDI4OTQ4NDVkZWFiO3Rvb2x1X3ZydHhfMDFQdTRkNUJnWDljTk55WnM5dE10c1kzIjp7InJlcXVlc3RJZCI6ImI2M2YxNDBiLTYyYzItNDc3MC04NjhjLWQyODk0ODQ1ZGVhYiIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFQdTRkNUJnWDljTk55WnM5dE10c1kzIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG48cmV0dXJuLWNvZGU+XG4wXG48L3JldHVybi1jb2RlPlxuPHN0ZG91dD5cbntcImNhbGxfaWRcIjpcIjg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlwiLFwiY2hhbm5lbF9uYW1lXCI6XCJjdXN0b21lcl9haV84ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcIixcInRva2VuXCI6XCIwMDdlSnhTWUxoZ1g4aWhLSFArMTgvZ1pRczV4WW9aVzlvMWo0dFkvSmh5cENNclRlN0ZXeTRGQmdOREUxTWpZeE1MZ3hSTFV4T1RwRlFMQTRORUEwT1ROR1BUcEpUa3RNUmtXd0dUakwySnBobnZMYUlZR0JrWUdWZ1lHQmxBZkNZd3lRd21XY0NrQVVOeWFYRkpmbTVxVVh4aVpyeUZoV1ZLU2xxeWthNWxxckdocm9sWnNybXVwYmxGb202S2tVbVNxV2x5VW9xRlpSSVRnNkU1SUFBQS8vODNXVEhTXCIsXCJzdGF0dXNcIjpcImNvbm5lY3RlZFwiLFwibWVzc2FnZVwiOlwiQ3VzdG9tZXIgY2FsbCBpbml0aWF0ZWQgc3VjY2Vzc2Z1bGx5LiBBSSBpcyByZWFkeSB0byBhc3Npc3QuXCIsXCJhaV9pbmZvXCI6e1wibmFtZVwiOlwiQUkgU3VwcG9ydCBBc3Npc3RhbnRcIixcInBlcnNvbmFsaXR5XCI6XCJoZWxwZnVsXCIsXCJsYW5ndWFnZVwiOlwiZW5cIixcImNhcGFiaWxpdGllc1wiOltcImJpbGxpbmdfc3VwcG9ydFwiLFwidGVjaG5pY2FsX3N1cHBvcnRcIixcImFjY291bnRfbWFuYWdlbWVudFwiLFwiZ2VuZXJhbF9pbnF1aXJpZXNcIixcInBhc3N3b3JkX3Jlc2V0XCIsXCJzdWJzY3JpcHRpb25fbWFuYWdlbWVudFwiXX19XG48L3N0ZG91dD5cbjxzdGRlcnI+XG5cbjwvc3RkZXJyPiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjFlN2Y0YThjLWVmZmItNGQwNS1hNTdiLWNkMDRlZjhlN2Y4NDt0b29sdV92cnR4XzAxOEdMaFBzR3JMNE1KTVVLQmhQNDltQSI6eyJyZXF1ZXN0SWQiOiIxZTdmNGE4Yy1lZmZiLTRkMDUtYTU3Yi1jZDA0ZWY4ZTdmODQiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxOEdMaFBzR3JMNE1KTVVLQmhQNDltQSIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IkhlcmUgaXMgdGhlIG91dHB1dCBmcm9tIHRlcm1pbmFsIDMwIChzdGF0dXM6IHN0aWxsIHJ1bm5pbmcpOlxuPG91dHB1dD4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfmoAgU3RhcnRpbmcgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXHJcbjIwMjUvMDUvMjYgMTM6NTQ6MDAg8J+TiyBTZXJ2ZXI6IDAuMC4wLjA6ODA4MFxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfjq8gQWdvcmEgQXBwIElEOiAwMTQ1MjM0ODBkOTU0NGJlODAwYTAxNGYzNWJkY2ZhY1xyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIPCfjq8gQWdvcmEgc2VydmljZSBpbml0aWFsaXplZCB3aXRoIEFwcCBJRDogMDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcclxuMjAyNS8wNS8yNiAxMzo1NDowMCDwn5uj77iPIFJvdXRlcyBjb25maWd1cmVkIHN1Y2Nlc3NmdWxseVxyXG4yMDI1LzA1LzI2IDEzOjU0OjAwIOKchSBTZXJ2ZXIgc3RhcnRpbmcgb24gMC4wLjAuMDo4MDgwXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg8J+TniBJbml0aWF0aW5nIG5ldyBjdXN0b21lci10by1BSSBjYWxsIGZvciBjdXN0b21lcjogdGVzdF9jdXN0b21lcl8xMjMsIHB1cnBvc2U6IGJpbGxpbmdfaW5xdWlyeVxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIOKchSBDdXN0b21lci10by1BSSBjYWxsIGluaXRpYXRlZCBzdWNjZXNzZnVsbHk6IDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIPCflJEgR2VuZXJhdGluZyBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliLCB1c2VyOiB0ZXN0X2N1c3RvbWVyXzEyMywgcm9sZTogMVxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIOKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg4pyFIFRva2VuIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgZm9yIGNoYW5uZWw6IGN1c3RvbWVyX2FpXzg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIPCflJcgW01PQ0tdIEFJIGpvaW5pbmcgQWdvcmEgY2hhbm5lbDogY3VzdG9tZXJfYWlfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliIGFzIHVzZXI6IGFpX2FnZW50XHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgam9pbmVkIGNoYW5uZWw6IGN1c3RvbWVyX2FpXzg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU0OjUzIPCfjpnvuI8gW01PQ0tdIFNldHRpbmcgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgY2hhbm5lbDogYWlfc3VwcG9ydF84ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcclxuMjAyNS8wNS8yNiAxMzo1NDo1MyDinIUgW01PQ0tdIEF1ZGlvIHByb2Nlc3Npbmcgc2V0dXAgY29tcGxldGUgZm9yIGNoYW5uZWw6IGFpX3N1cHBvcnRfODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTMg8J+OrSBbTU9DS10gU3RhcnRpbmcgYXVkaW8gc2ltdWxhdGlvbiBmb3IgY2FsbDogODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjo6MSAtIFsyNi9NYXkvMjAyNToxMzo1NDo1MyArMDcwMF0gXCJQT1NUIC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGUgSFRUUC8xLjEgMjAxIDUwMi44ODA2MjVtcyBcImN1cmwvOC43LjFcIiBcIlxyXG4yMDI1LzA1LzI2IDEzOjU0OjU4IPCfjqQgW01PQ0tdIFNpbXVsYXRpbmcgaW5jb21pbmcgYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliIChtZXNzYWdlIDEpXHJcbjIwMjUvMDUvMjYgMTM6NTQ6NTgg8J+OpCBQcm9jZXNzaW5nIGF1ZGlvIG1lc3NhZ2UgZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliICgxNjAwIGJ5dGVzKVxyXG4yMDI1LzA1LzI2IDEzOjU0OjU4IPCfjpnvuI8gW01PQ0tdIFZBRDogRW5lcmd5PTEwNS41MCwgVGhyZXNob2xkPTcwMC4wMCwgSXNTcGVlY2g9ZmFsc2VcclxuMjAyNS8wNS8yNiAxMzo1NDo1OCDwn5SHIE5vIHNwZWVjaCBkZXRlY3RlZCBpbiBhdWRpbyBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcclxuMjAyNS8wNS8yNiAxMzo1NTowNSDwn46kIFtNT0NLXSBTaW11bGF0aW5nIGluY29taW5nIGF1ZGlvIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAobWVzc2FnZSAyKVxyXG4yMDI1LzA1LzI2IDEzOjU1OjA1IPCfjqQgUHJvY2Vzc2luZyBhdWRpbyBtZXNzYWdlIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAoMTYwMCBieXRlcylcclxuMjAyNS8wNS8yNiAxMzo1NTowNSDwn46Z77iPIFtNT0NLXSBWQUQ6IEVuZXJneT0xMDUuMDIsIFRocmVzaG9sZD03MDAuMDAsIElzU3BlZWNoPWZhbHNlXHJcbjIwMjUvMDUvMjYgMTM6NTU6MDUg8J+UhyBObyBzcGVlY2ggZGV0ZWN0ZWQgaW4gYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTU6MTQg8J+OpCBbTU9DS10gU2ltdWxhdGluZyBpbmNvbWluZyBhdWRpbyBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWIgKG1lc3NhZ2UgMylcclxuMjAyNS8wNS8yNiAxMzo1NToxNCDwn46kIFByb2Nlc3NpbmcgYXVkaW8gbWVzc2FnZSBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWIgKDE2MDAgYnl0ZXMpXHJcbjIwMjUvMDUvMjYgMTM6NTU6MTQg8J+Ome+4jyBbTU9DS10gVkFEOiBFbmVyZ3k9MTA1LjE4LCBUaHJlc2hvbGQ9NzAwLjAwLCBJc1NwZWVjaD1mYWxzZVxyXG4yMDI1LzA1LzI2IDEzOjU1OjE0IPCflIcgTm8gc3BlZWNoIGRldGVjdGVkIGluIGF1ZGlvIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG4yMDI1LzA1LzI2IDEzOjU1OjI1IPCfjqQgW01PQ0tdIFNpbXVsYXRpbmcgaW5jb21pbmcgYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliIChtZXNzYWdlIDQpXHJcbjIwMjUvMDUvMjYgMTM6NTU6MjUg8J+OpCBQcm9jZXNzaW5nIGF1ZGlvIG1lc3NhZ2UgZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliICgxNjAwIGJ5dGVzKVxyXG4yMDI1LzA1LzI2IDEzOjU1OjI1IPCfjpnvuI8gW01PQ0tdIFZBRDogRW5lcmd5PTEwNS45OCwgVGhyZXNob2xkPTcwMC4wMCwgSXNTcGVlY2g9ZmFsc2VcclxuMjAyNS8wNS8yNiAxMzo1NToyNSDwn5SHIE5vIHNwZWVjaCBkZXRlY3RlZCBpbiBhdWRpbyBmb3IgY2FsbCA4ODlkZGZjMi05ZTMxLTQ2YzctOTc4YS1kMjRiNTVjYmQ4OWJcclxuMjAyNS8wNS8yNiAxMzo1NTozOCDwn46kIFtNT0NLXSBTaW11bGF0aW5nIGluY29taW5nIGF1ZGlvIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAobWVzc2FnZSA1KVxyXG4yMDI1LzA1LzI2IDEzOjU1OjM4IPCfjqQgUHJvY2Vzc2luZyBhdWRpbyBtZXNzYWdlIGZvciBjYWxsIDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YiAoMTYwMCBieXRlcylcclxuMjAyNS8wNS8yNiAxMzo1NTozOCDwn46Z77iPIFtNT0NLXSBWQUQ6IEVuZXJneT0xMDUuODIsIFRocmVzaG9sZD03MDAuMDAsIElzU3BlZWNoPWZhbHNlXHJcbjIwMjUvMDUvMjYgMTM6NTU6Mzgg8J+UhyBObyBzcGVlY2ggZGV0ZWN0ZWQgaW4gYXVkaW8gZm9yIGNhbGwgODg5ZGRmYzItOWUzMS00NmM3LTk3OGEtZDI0YjU1Y2JkODliXHJcbjIwMjUvMDUvMjYgMTM6NTU6Mzgg8J+OrSBbTU9DS10gQXVkaW8gc2ltdWxhdGlvbiBjb21wbGV0ZWQgZm9yIGNhbGw6IDg4OWRkZmMyLTllMzEtNDZjNy05NzhhLWQyNGI1NWNiZDg5YlxyXG48L291dHB1dD5cbiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjllZTQyMWNkLTQwNWItNGVhOS04ZTM0LTk1YzYwYTVkZDdmZTt0b29sdV92cnR4XzAxTDNObUttSG9hOHVZMUtRcmE5M1NxRCI6eyJyZXF1ZXN0SWQiOiI5ZWU0MjFjZC00MDViLTRlYTktOGUzNC05NWM2MGE1ZGQ3ZmUiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxTDNObUttSG9hOHVZMUtRcmE5M1NxRCIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IHNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcImZtdFwiXG4gICAgIDZcdFx0XCJsb2dcIlxuICAgICA3XHRcdFwic3RyY29udlwiXG4gICAgIDhcdFx0XCJ0aW1lXCJcbiAgICAgOVx0XG4gICAgMTBcdFx0XCJnaXRodWIuY29tL0Fnb3JhSU8vVG9vbHMvRHluYW1pY0tleS9BZ29yYUR5bmFtaWNLZXkvZ28vc3JjL3J0Y3Rva2VuYnVpbGRlcjJcIlxuICAgIDExXHQpXG4gICAgMTJcdFxuICAgIDEzXHQvLyBBZ29yYVNlcnZpY2UgaGFuZGxlcyBBZ29yYSBSVEMgb3BlcmF0aW9uc1xuICAgIDE0XHR0eXBlIEFnb3JhU2VydmljZSBzdHJ1Y3Qge1xuICAgIDE1XHRcdGNvbmZpZyAqY29uZmlnLkNvbmZpZ1xuICAgIDE2XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY29udGFpbjpcbiAgICAxN1x0XHQvLyAtIEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAxOFx0XHQvLyAtIE1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDE5XHRcdC8vIC0gQWN0aXZlIGNvbm5lY3Rpb25zIG1hcFxuICAgIDIwXHRcdC8vIC0gQXVkaW8vVmlkZW8gb2JzZXJ2ZXJzXG4gICAgMjFcdH1cbiAgICAyMlx0XG4gICAgMjNcdC8vIFRva2VuUmVxdWVzdCByZXByZXNlbnRzIGEgcmVxdWVzdCBmb3IgQWdvcmEgdG9rZW5cbiAgICAyNFx0dHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcbiAgICAyNVx0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICAyNlx0XHRVc2VySUQgICAgICBzdHJpbmcgYGpzb246XCJ1c2VyX2lkXCJgXG4gICAgMjdcdFx0Um9sZSAgICAgICAgaW50ICAgIGBqc29uOlwicm9sZVwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxuICAgIDI4XHR9XG4uLi5cbiAgICA1MFx0XG4gICAgNTFcdC8vIEdlbmVyYXRlVG9rZW4gZ2VuZXJhdGVzIGFuIEFnb3JhIFJUQyB0b2tlbiBmb3IgY2hhbm5lbCBhY2Nlc3NcbiAgICA1Mlx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgR2VuZXJhdGVUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQpICgqVG9rZW5SZXNwb25zZSwgZXJyb3IpIHtcbiAgICA1M1x0XHRsb2cuUHJpbnRmKFwi8J+UkSBHZW5lcmF0aW5nIEFnb3JhIHRva2VuIGZvciBjaGFubmVsOiAlcywgdXNlcjogJXMsIHJvbGU6ICVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNTRcdFxuICAgIDU1XHRcdC8vIFRva2VuIGV4cGlyZXMgaW4gMjQgaG91cnNcbiAgICA1Nlx0XHRleHBpcmF0aW9uVGltZSA6PSB0aW1lLk5vdygpLkFkZCgyNCAqIHRpbWUuSG91cikuVW5peCgpXG4gICAgNTdcdFxuICAgIDU4XHRcdHZhciB0b2tlbiBzdHJpbmdcbiAgICA1OVx0XHR2YXIgZXJyIGVycm9yXG4gICAgNjBcdFxuICAgIDYxXHRcdGlmIGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSAhPSBcIlwiIHtcbiAgICA2Mlx0XHRcdC8vIEdlbmVyYXRlIHJlYWwgdG9rZW4gd2l0aCBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAgNjNcdFx0XHR0b2tlbiwgZXJyID0gYXMuZ2VuZXJhdGVSZWFsVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSwgdWludDMyKGV4cGlyYXRpb25UaW1lKSlcbiAgICA2NFx0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgIDY1XHRcdFx0XHRyZXR1cm4gbmlsLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGdlbmVyYXRlIHRva2VuOiAld1wiLCBlcnIpXG4gICAgNjZcdFx0XHR9XG4gICAgNjdcdFx0XHRsb2cuUHJpbnRmKFwi4pyFIEdlbmVyYXRlZCByZWFsIEFnb3JhIHRva2VuIGZvciBjaGFubmVsOiAlcywgdG9rZW4gaXM6ICVzXCIsIGNoYW5uZWxOYW1lLCB0b2tlbilcbiAgICA2OFx0XHR9IGVsc2Uge1xuICAgIDY5XHRcdFx0Ly8gR2VuZXJhdGUgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgICA3MFx0XHRcdHRva2VuID0gYXMuZ2VuZXJhdGVNb2NrVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA3MVx0XHRcdGxvZy5QcmludGYoXCLimqDvuI8gW01PQ0tdIFVzaW5nIG1vY2sgdG9rZW4gKG5vIGFwcCBjZXJ0aWZpY2F0ZSBwcm92aWRlZClcIilcbiAgICA3Mlx0XHR9XG4gICAgNzNcdFxuICAgIDc0XHRcdHJlc3BvbnNlIDo9ICZUb2tlblJlc3BvbnNle1xuICAgIDc1XHRcdFx0VG9rZW46ICAgICB0b2tlbixcbiAgICA3Nlx0XHRcdEV4cGlyZXNBdDogZXhwaXJhdGlvblRpbWUsXG4gICAgNzdcdFx0fVxuICAgIDc4XHRcbiAgICA3OVx0XHRsb2cuUHJpbnRmKFwi4pyFIFRva2VuIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkgZm9yIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgIDgwXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAgODFcdH1cbiAgICA4Mlx0XG4gICAgODNcdC8vIGdlbmVyYXRlUmVhbFRva2VuIGdlbmVyYXRlcyBhIHJlYWwgQWdvcmEgdG9rZW4gdXNpbmcgdGhlIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA4NFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgZ2VuZXJhdGVSZWFsVG9rZW4oY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcsIHJvbGUgaW50LCBleHBpcmF0aW9uIHVpbnQzMikgKHN0cmluZywgZXJyb3IpIHtcbiAgICA4NVx0XHQvLyBDb252ZXJ0IHVzZXJJRCB0byB1aW50MzIgKEFnb3JhIGV4cGVjdHMgbnVtZXJpYyBVSUQpXG4gICAgODZcdFx0dmFyIHVpZCB1aW50MzJcbiAgICA4N1x0XHRpZiB1c2VySUQgIT0gXCJcIiB7XG4gICAgODhcdFx0XHRpZiBwYXJzZWRVSUQsIGVyciA6PSBzdHJjb252LlBhcnNlVWludCh1c2VySUQsIDEwLCAzMik7IGVyciA9PSBuaWwge1xuICAgIDg5XHRcdFx0XHR1aWQgPSB1aW50MzIocGFyc2VkVUlEKVxuICAgIDkwXHRcdFx0fSBlbHNlIHtcbiAgICA5MVx0XHRcdFx0Ly8gSWYgdXNlcklEIGlzIG5vdCBudW1lcmljLCBnZW5lcmF0ZSBhIGhhc2gtYmFzZWQgVUlEXG4gICAgOTJcdFx0XHRcdHVpZCA9IHVpbnQzMihsZW4odXNlcklEKSkgLy8gU2ltcGxlIGZhbGxiYWNrLCBpbiBwcm9kdWN0aW9uIHVzZSBhIHByb3BlciBoYXNoXG4gICAgOTNcdFx0XHR9XG4gICAgOTRcdFx0fSBlbHNlIHtcbiAgICA5NVx0XHRcdHVpZCA9IDAgLy8gTGV0IEFnb3JhIGFzc2lnbiBhIFVJRFxuICAgIDk2XHRcdH1cbiAgICA5N1x0XG4gICAgOThcdFx0Ly8gQ29udmVydCByb2xlIHRvIEFnb3JhIHJvbGUgdHlwZVxuICAgIDk5XHRcdHZhciBhZ29yYVJvbGUgcnRjdG9rZW5idWlsZGVyMi5Sb2xlXG4gICAxMDBcdFx0aWYgcm9sZSA9PSAxIHtcbiAgIDEwMVx0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVB1Ymxpc2hlclxuICAgMTAyXHRcdH0gZWxzZSB7XG4gICAxMDNcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVTdWJzY3JpYmVyXG4gICAxMDRcdFx0fVxuICAgMTA1XHRcbiAgIDEwNlx0XHQvLyBHZW5lcmF0ZSB0b2tlbiB1c2luZyBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5XG4gICAxMDdcdFx0dG9rZW4sIGVyciA6PSBydGN0b2tlbmJ1aWxkZXIyLkJ1aWxkVG9rZW5XaXRoVWlkKFxuICAgMTA4XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcElELFxuICAgMTA5XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlLFxuICAgMTEwXHRcdFx0Y2hhbm5lbE5hbWUsXG4gICAxMTFcdFx0XHR1aWQsXG4gICAxMTJcdFx0XHRhZ29yYVJvbGUsXG4gICAxMTNcdFx0XHRleHBpcmF0aW9uLFxuICAgMTE0XHRcdFx0ZXhwaXJhdGlvbiwgLy8gUHJpdmlsZWdlIGV4cGlyZWQgdGltZXN0YW1wIChzYW1lIGFzIHRva2VuIGV4cGlyYXRpb24pXG4gICAxMTVcdFx0KVxuICAgMTE2XHRcbiAgIDExN1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDExOFx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGJ1aWxkIEFnb3JhIHRva2VuOiAld1wiLCBlcnIpXG4gICAxMTlcdFx0fVxuICAgMTIwXHRcbiAgIDEyMVx0XHRyZXR1cm4gdG9rZW4sIG5pbFxuICAgMTIyXHR9XG4gICAxMjNcdFxuICAgMTI0XHQvLyBnZW5lcmF0ZU1vY2tUb2tlbiBnZW5lcmF0ZXMgYSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgMTI1XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQpIHN0cmluZyB7XG4gICAxMjZcdFx0dGltZXN0YW1wIDo9IHRpbWUuTm93KCkuVW5peCgpXG4gICAxMjdcdFx0cmV0dXJuIGZtdC5TcHJpbnRmKFwibW9ja190b2tlbl8lc18lc18lZF8lZFwiLCBjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB0aW1lc3RhbXApXG4gICAxMjhcdH1cbiAgIDEyOVx0XG4gICAxMzBcdC8vIEpvaW5DaGFubmVsIGpvaW5zIGFuIEFnb3JhIGNoYW5uZWwgZm9yIEFJIHByb2Nlc3NpbmdcbiAgIDEzMVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgSm9pbkNoYW5uZWwoY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcpIGVycm9yIHtcbiAgIDEzMlx0XHRsb2cuUHJpbnRmKFwi8J+UlyBbTU9DS10gQUkgam9pbmluZyBBZ29yYSBjaGFubmVsOiAlcyBhcyB1c2VyOiAlc1wiLCBjaGFubmVsTmFtZSwgdXNlcklEKVxuICAgMTMzXHRcbiAgIDEzNFx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTM1XHRcdC8vIDEuIENyZWF0ZSBSVEMgY29ubmVjdGlvblxuICAgMTM2XHRcdC8vIDIuIFNldCB1cCBsb2NhbCB1c2VyXG4gICAxMzdcdFx0Ly8gMy4gQ3JlYXRlIGF1ZGlvIHRyYWNrIGZvciBBSSB2b2ljZVxuICAgMTM4XHRcdC8vIDQuIFJlZ2lzdGVyIGF1ZGlvIGZyYW1lIG9ic2VydmVycyBmb3IgcHJvY2Vzc2luZyBpbmNvbWluZyBhdWRpb1xuICAgMTM5XHRcdC8vIDUuIEpvaW4gdGhlIGNoYW5uZWxcbiAgIDE0MFx0XHQvLyA2LiBTdGFydCBhdWRpbyBwcm9jZXNzaW5nIHBpcGVsaW5lXG4gICAxNDFcdFxuICAgMTQyXHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb25cbiAgIDE0M1x0XHR0aW1lLlNsZWVwKDUwMCAqIHRpbWUuTWlsbGlzZWNvbmQpIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gdGltZVxuICAgMTQ0XHRcbiAgIDE0NVx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgam9pbmVkIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMTQ2XHRcdHJldHVybiBuaWxcbiAgIDE0N1x0fVxuLi4uXG5QYXRoOiBoYW5kbGVycy9jYWxsX2hhbmRsZXIuZ29cbi4uLlxuICAgIDYyXHRcbiAgICA2M1x0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGNoYW5uZWxcbiAgICA2NFx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIFwiYWlfYWdlbnRcIiwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAgNjVcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjZcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgICA2N1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIEFnb3JhIHRva2VuXCIsXG4gICAgNjhcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA2OVx0XHRcdH0pXG4gICAgNzBcdFx0XHRyZXR1cm5cbiAgICA3MVx0XHR9XG4gICAgNzJcdFxuICAgIDczXHRcdC8vIEpvaW4gdGhlIGNoYW5uZWwgYXMgQUkgYWdlbnRcbiAgICA3NFx0XHRpZiBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkpvaW5DaGFubmVsKGNhbGwuQ2hhbm5lbE5hbWUsIFwiYWlfYWdlbnRcIik7IGVyciAhPSBuaWwge1xuICAgIDc1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgNzZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBqb2luIEFnb3JhIGNoYW5uZWxcIixcbiAgICA3N1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDc4XHRcdFx0fSlcbiAgICA3OVx0XHRcdHJldHVyblxuICAgIDgwXHRcdH1cbiAgICA4MVx0XG4gICAgODJcdFx0Ly8gU2V0IHVwIGF1ZGlvIHByb2Nlc3NpbmcgZm9yIHRoaXMgY2FsbFxuICAgIDgzXHRcdGF1ZGlvQ2FsbGJhY2sgOj0gZnVuYyhhdWRpb0RhdGEgW11ieXRlKSBlcnJvciB7XG4gICAgODRcdFx0XHRyZXR1cm4gY2guY2FsbE1hbmFnZXIuUHJvY2Vzc0F1ZGlvTWVzc2FnZShjYWxsLklELCBhdWRpb0RhdGEpXG4gICAgODVcdFx0fVxuLi4uXG4gICAyNDhcdFxuICAgMjQ5XHRcdC8vIEpvaW4gdGhlIGNhbGxcbiAgIDI1MFx0XHRjYWxsLCBlcnIgOj0gY2guY2FsbE1hbmFnZXIuSm9pbkNhbGwocmVxdWVzdC5DYWxsSUQsIHJlcXVlc3QuQ3VzdG9tZXJJRClcbiAgIDI1MVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDI1Mlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMjUzXHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gam9pbiBjYWxsXCIsXG4gICAyNTRcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDI1NVx0XHRcdH0pXG4gICAyNTZcdFx0XHRyZXR1cm5cbiAgIDI1N1x0XHR9XG4gICAyNThcdFxuICAgMjU5XHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY3VzdG9tZXJcbiAgIDI2MFx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIHJlcXVlc3QuQ3VzdG9tZXJJRCwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAyNjFcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAyNjJcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDI2M1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIEFnb3JhIHRva2VuXCIsXG4gICAyNjRcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDI2NVx0XHRcdH0pXG4gICAyNjZcdFx0XHRyZXR1cm5cbiAgIDI2N1x0XHR9XG4gICAyNjhcdFxuICAgMjY5XHRcdHJlc3BvbnNlIDo9IG1vZGVscy5Kb2luQ2FsbFJlc3BvbnNle1xuICAgMjcwXHRcdFx0Q2hhbm5lbE5hbWU6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAyNzFcdFx0XHRUb2tlbjogICAgICAgdG9rZW4uVG9rZW4sXG4gICAyNzJcdFx0XHRDYWxsSW5mbzogICAgKmNhbGwsXG4gICAyNzNcdFx0fVxuICAgMjc0XHRcbiAgIDI3NVx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgcmVzcG9uc2UpXG4gICAyNzZcdH1cbi4uLlxuICAgNDAwXHRcbiAgIDQwMVx0XHQvLyBHZXQgY2FsbCB0byB2YWxpZGF0ZSBpdCBleGlzdHNcbiAgIDQwMlx0XHRjYWxsLCBlcnIgOj0gY2guY2FsbE1hbmFnZXIuR2V0Q2FsbChjYWxsSUQpXG4gICA0MDNcdFx0aWYgZXJyICE9IG5pbCB7XG4gICA0MDRcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNOb3RGb3VuZCwgZ2luLkh7XG4gICA0MDVcdFx0XHRcdFwiZXJyb3JcIjogICBcIkNhbGwgbm90IGZvdW5kXCIsXG4gICA0MDZcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDQwN1x0XHRcdH0pXG4gICA0MDhcdFx0XHRyZXR1cm5cbiAgIDQwOVx0XHR9XG4gICA0MTBcdFxuICAgNDExXHRcdC8vIEdlbmVyYXRlIHRva2VuXG4gICA0MTJcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCByZXF1ZXN0LlVzZXJJRCwgcmVxdWVzdC5Sb2xlKVxuICAgNDEzXHRcdGlmIGVyciAhPSBuaWwge1xuICAgNDE0XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICA0MTVcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlblwiLFxuICAgNDE2XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICA0MTdcdFx0XHR9KVxuICAgNDE4XHRcdFx0cmV0dXJuXG4gICA0MTlcdFx0fVxuICAgNDIwXHRcbiAgIDQyMVx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgZ2luLkh7XG4gICA0MjJcdFx0XHRcInRva2VuXCI6ICAgICAgICB0b2tlbi5Ub2tlbixcbiAgIDQyM1x0XHRcdFwiZXhwaXJlc19hdFwiOiAgIHRva2VuLkV4cGlyZXNBdCxcbiAgIDQyNFx0XHRcdFwiY2hhbm5lbF9uYW1lXCI6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICA0MjVcdFx0XHRcInVzZXJfaWRcIjogICAgICByZXF1ZXN0LlVzZXJJRCxcbiAgIDQyNlx0XHRcdFwicm9sZVwiOiAgICAgICAgIHJlcXVlc3QuUm9sZSxcbiAgIDQyN1x0XHR9KVxuICAgNDI4XHR9XG4uLi5cblBhdGg6IGNvbmZpZy9jb25maWcuZ29cbiAgICAgMVx0cGFja2FnZSBjb25maWdcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJsb2dcIlxuICAgICA1XHRcdFwib3NcIlxuICAgICA2XHRcdFwic3RyY29udlwiXG4gICAgIDdcdFxuICAgICA4XHRcdFwiZ2l0aHViLmNvbS9qb2hvL2dvZG90ZW52XCJcbiAgICAgOVx0KVxuICAgIDEwXHRcbiAgICAxMVx0dHlwZSBDb25maWcgc3RydWN0IHtcbiAgICAxMlx0XHRTZXJ2ZXIgU2VydmVyQ29uZmlnXG4gICAgMTNcdFx0QWdvcmEgIEFnb3JhQ29uZmlnXG4gICAgMTRcdFx0QUkgICAgIEFJQ29uZmlnXG4gICAgMTVcdH1cbiAgICAxNlx0XG4gICAgMTdcdHR5cGUgU2VydmVyQ29uZmlnIHN0cnVjdCB7XG4gICAgMThcdFx0UG9ydCBzdHJpbmdcbiAgICAxOVx0XHRIb3N0IHN0cmluZ1xuICAgIDIwXHR9XG4gICAgMjFcdFxuICAgIDIyXHR0eXBlIEFnb3JhQ29uZmlnIHN0cnVjdCB7XG4gICAgMjNcdFx0QXBwSUQgICAgICAgICAgc3RyaW5nXG4gICAgMjRcdFx0QXBwQ2VydGlmaWNhdGUgc3RyaW5nXG4gICAgMjVcdFx0Q2hhbm5lbFByZWZpeCAgc3RyaW5nXG4gICAgMjZcdH1cbiAgICAyN1x0XG4gICAgMjhcdHR5cGUgQUlDb25maWcgc3RydWN0IHtcbiAgICAyOVx0XHRPcGVuQUlBUElLZXkgICAgc3RyaW5nXG4gICAgMzBcdFx0T3BlbkFJTW9kZWwgICAgIHN0cmluZ1xuICAgIDMxXHRcdFNUVFByb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzMlx0XHRUVFNQcm92aWRlciAgICAgc3RyaW5nXG4gICAgMzNcdFx0Vm9pY2VJRCAgICAgICAgIHN0cmluZ1xuICAgIDM0XHRcdEVuYWJsZVZBRCAgICAgICBib29sXG4gICAgMzVcdFx0VkFEU2Vuc2l0aXZpdHkgIGZsb2F0NjRcbiAgICAzNlx0fVxuICAgIDM3XHRcbiAgICAzOFx0dmFyIEFwcENvbmZpZyAqQ29uZmlnXG4gICAgMzlcdFxuICAgIDQwXHRmdW5jIExvYWRDb25maWcoKSB7XG4gICAgNDFcdFx0Ly8gTG9hZCAuZW52IGZpbGUgaWYgaXQgZXhpc3RzXG4gICAgNDJcdFx0aWYgZXJyIDo9IGdvZG90ZW52LkxvYWQoKTsgZXJyICE9IG5pbCB7XG4gICAgNDNcdFx0XHRsb2cuUHJpbnRsbihcIk5vIC5lbnYgZmlsZSBmb3VuZCwgdXNpbmcgZW52aXJvbm1lbnQgdmFyaWFibGVzXCIpXG4gICAgNDRcdFx0fVxuICAgIDQ1XHRcbiAgICA0Nlx0XHRBcHBDb25maWcgPSAmQ29uZmlne1xuICAgIDQ3XHRcdFx0U2VydmVyOiBTZXJ2ZXJDb25maWd7XG4gICAgNDhcdFx0XHRcdFBvcnQ6IGdldEVudihcIlNFUlZFUl9QT1JUXCIsIFwiODA4MFwiKSxcbiAgICA0OVx0XHRcdFx0SG9zdDogZ2V0RW52KFwiU0VSVkVSX0hPU1RcIiwgXCIwLjAuMC4wXCIpLFxuICAgIDUwXHRcdFx0fSxcbiAgICA1MVx0XHRcdEFnb3JhOiBBZ29yYUNvbmZpZ3tcbiAgICA1Mlx0XHRcdFx0QXBwSUQ6ICAgICAgICAgIGdldEVudihcIkFHT1JBX0FQUF9JRFwiLCBcIlwiKSxcbiAgICA1M1x0XHRcdFx0QXBwQ2VydGlmaWNhdGU6IGdldEVudihcIkFHT1JBX0FQUF9DRVJUSUZJQ0FURVwiLCBcIlwiKSxcbiAgICA1NFx0XHRcdFx0Q2hhbm5lbFByZWZpeDogIGdldEVudihcIkFHT1JBX0NIQU5ORUxfUFJFRklYXCIsIFwiYWlfc3VwcG9ydF9cIiksXG4gICAgNTVcdFx0XHR9LFxuICAgIDU2XHRcdFx0QUk6IEFJQ29uZmlne1xuICAgIDU3XHRcdFx0XHRPcGVuQUlBUElLZXk6ICAgIGdldEVudihcIk9QRU5BSV9BUElfS0VZXCIsIFwiXCIpLFxuICAgIDU4XHRcdFx0XHRPcGVuQUlNb2RlbDogICAgIGdldEVudihcIk9QRU5BSV9NT0RFTFwiLCBcImdwdC00XCIpLFxuICAgIDU5XHRcdFx0XHRTVFRQcm92aWRlcjogICAgIGdldEVudihcIlNUVF9QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MFx0XHRcdFx0VFRTUHJvdmlkZXI6ICAgICBnZXRFbnYoXCJUVFNfUFJPVklERVJcIiwgXCJvcGVuYWlcIiksXG4gICAgNjFcdFx0XHRcdFZvaWNlSUQ6ICAgICAgICAgZ2V0RW52KFwiVFRTX1ZPSUNFX0lEXCIsIFwiYWxsb3lcIiksXG4gICAgNjJcdFx0XHRcdEVuYWJsZVZBRDogICAgICAgZ2V0RW52Qm9vbChcIkVOQUJMRV9WQURcIiwgdHJ1ZSksXG4gICAgNjNcdFx0XHRcdFZBRFNlbnNpdGl2aXR5OiAgZ2V0RW52RmxvYXQoXCJWQURfU0VOU0lUSVZJVFlcIiwgMC43KSxcbiAgICA2NFx0XHRcdH0sXG4gICAgNjVcdFx0fVxuLi4uXG5QYXRoOiAuZW52XG4gICAgIDFcdCMgU2VydmVyIENvbmZpZ3VyYXRpb25cbiAgICAgMlx0U0VSVkVSX1BPUlQ9ODA4MFxuICAgICAzXHRTRVJWRVJfSE9TVD0wLjAuMC4wXG4gICAgIDRcdFxuICAgICA1XHQjIEFnb3JhIENvbmZpZ3VyYXRpb25cbiAgICAgNlx0QUdPUkFfQVBQX0lEPTAxNDUyMzQ4MGQ5NTQ0YmU4MDBhMDE0ZjM1YmRjZmFjXG4gICAgIDdcdEFHT1JBX0FQUF9DRVJUSUZJQ0FURT1lN2YzN2U2N2Q4MmI0YWZmODQ2MzE3NzRjMWI1ZGU2MVxuICAgICA4XHRBR09SQV9DSEFOTkVMX1BSRUZJWD1haV9zdXBwb3J0X1xuICAgICA5XHRcbiAgICAxMFx0IyBBSSBDb25maWd1cmF0aW9uXG4gICAgMTFcdE9QRU5BSV9BUElfS0VZPWstcHJvai16eDNUV1hSLUxLY01QVG0yTHdxMHcyZ29wdXpWT2Y0dHVpZ3hRSDVpcGlPeVJDSzl6Rk9PZ0VNQmptcWYwQTdRVmRDMUhHNzFrTVQzQmxia0ZKMkxjZ2lCQXVwbDVVOWJCNXJFVHlQTVJsRVZfMUdaLWtac1dzMUpjVHBCWVMtbGhrYUVERE1BdkVxZm50WVFBVWRMS2VuUDM2VUFcbiAgICAxMlx0T1BFTkFJX01PREVMPWdwdC00XG4gICAgMTNcdFNUVF9QUk9WSURFUj1vcGVuYWlcbiAgICAxNFx0VFRTX1BST1ZJREVSPW9wZW5haVxuICAgIDE1XHRUVFNfVk9JQ0VfSUQ9YWxsb3lcbiAgICAxNlx0RU5BQkxFX1ZBRD10cnVlXG4gICAgMTdcdFZBRF9TRU5TSVRJVklUWT0wLjdcbi4uLlxuUGF0aDogbW9kZWxzL2NhbGwuZ29cbi4uLlxuICAgIDI3XHRcbiAgICAyOFx0Ly8gQ2FsbCByZXByZXNlbnRzIGEgY3VzdG9tZXIgc3VwcG9ydCBjYWxsIHNlc3Npb25cbiAgICAyOVx0dHlwZSBDYWxsIHN0cnVjdCB7XG4gICAgMzBcdFx0SUQgICAgICAgICAgc3RyaW5nICAgICAgIGBqc29uOlwiaWRcImBcbiAgICAzMVx0XHRDdXN0b21lcklEICBzdHJpbmcgICAgICAgYGpzb246XCJjdXN0b21lcl9pZFwiYFxuICAgIDMyXHRcdENoYW5uZWxOYW1lIHN0cmluZyAgICAgICBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgIDMzXHRcdFN0YXR1cyAgICAgIENhbGxTdGF0dXMgICBganNvbjpcInN0YXR1c1wiYFxuICAgIDM0XHRcdFR5cGUgICAgICAgIENhbGxUeXBlICAgICBganNvbjpcInR5cGVcImBcbiAgICAzNVx0XHRTdGFydFRpbWUgICB0aW1lLlRpbWUgICAgYGpzb246XCJzdGFydF90aW1lXCJgXG4gICAgMzZcdFx0RW5kVGltZSAgICAgKnRpbWUuVGltZSAgIGBqc29uOlwiZW5kX3RpbWUsb21pdGVtcHR5XCJgXG4gICAgMzdcdFx0RHVyYXRpb24gICAgaW50NjQgICAgICAgIGBqc29uOlwiZHVyYXRpb25cImAgLy8gaW4gc2Vjb25kc1xuICAgIDM4XHRcdFRyYW5zY3JpcHQgIFtdTWVzc2FnZSAgICBganNvbjpcInRyYW5zY3JpcHRcImBcbiAgICAzOVx0XHRNZXRhZGF0YSAgICBDYWxsTWV0YWRhdGEgYGpzb246XCJtZXRhZGF0YVwiYFxuICAgIDQwXHR9XG4gICAgNDFcdFxuICAgIDQyXHQvLyBNZXNzYWdlIHJlcHJlc2VudHMgYSBzaW5nbGUgbWVzc2FnZSBpbiB0aGUgY29udmVyc2F0aW9uXG4gICAgNDNcdHR5cGUgTWVzc2FnZSBzdHJ1Y3Qge1xuICAgIDQ0XHRcdElEICAgICAgICBzdHJpbmcgICAgYGpzb246XCJpZFwiYFxuICAgIDQ1XHRcdFNwZWFrZXIgICBzdHJpbmcgICAgYGpzb246XCJzcGVha2VyXCJgIC8vIFwiYWlcIiBvciBcImN1c3RvbWVyXCJcbiAgICA0Nlx0XHRDb250ZW50ICAgc3RyaW5nICAgIGBqc29uOlwiY29udGVudFwiYFxuICAgIDQ3XHRcdFRpbWVzdGFtcCB0aW1lLlRpbWUgYGpzb246XCJ0aW1lc3RhbXBcImBcbiAgICA0OFx0XHRUeXBlICAgICAgc3RyaW5nICAgIGBqc29uOlwidHlwZVwiYCAvLyBcInRleHRcIiwgXCJhdWRpb1wiXG4gICAgNDlcdH1cbiAgICA1MFx0XG4gICAgNTFcdC8vIENhbGxNZXRhZGF0YSBjb250YWlucyBhZGRpdGlvbmFsIGluZm9ybWF0aW9uIGFib3V0IHRoZSBjYWxsXG4gICAgNTJcdHR5cGUgQ2FsbE1ldGFkYXRhIHN0cnVjdCB7XG4gICAgNTNcdFx0UHVycG9zZSAgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcInB1cnBvc2VcImBcbiAgICA1NFx0XHRDdXN0b21lckluZm8gIEN1c3RvbWVySW5mbyAgICAgIGBqc29uOlwiY3VzdG9tZXJfaW5mb1wiYFxuICAgIDU1XHRcdEFJUGVyc29uYWxpdHkgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJhaV9wZXJzb25hbGl0eVwiYFxuICAgIDU2XHRcdExhbmd1YWdlICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgIDU3XHRcdFRhZ3MgICAgICAgICAgW11zdHJpbmcgICAgICAgICAgYGpzb246XCJ0YWdzXCJgXG4gICAgNThcdFx0Q3VzdG9tRmllbGRzICBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcImN1c3RvbV9maWVsZHNcImBcbiAgICA1OVx0fVxuLi4uXG4gICAxMzhcdFxuICAgMTM5XHQvLyBOZXdDYWxsIGNyZWF0ZXMgYSBuZXcgY2FsbCBpbnN0YW5jZVxuICAgMTQwXHRmdW5jIE5ld0NhbGwoY3VzdG9tZXJJRCwgcHVycG9zZSBzdHJpbmcsIGN1c3RvbWVySW5mbyBDdXN0b21lckluZm8pICpDYWxsIHtcbiAgIDE0MVx0XHRjYWxsSUQgOj0gdXVpZC5OZXcoKS5TdHJpbmcoKVxuICAgMTQyXHRcdGNoYW5uZWxOYW1lIDo9IFwiYWlfc3VwcG9ydF9cIiArIGNhbGxJRFxuICAgMTQzXHRcbiAgIDE0NFx0XHRyZXR1cm4gJkNhbGx7XG4gICAxNDVcdFx0XHRJRDogICAgICAgICAgY2FsbElELFxuICAgMTQ2XHRcdFx0Q3VzdG9tZXJJRDogIGN1c3RvbWVySUQsXG4gICAxNDdcdFx0XHRDaGFubmVsTmFtZTogY2hhbm5lbE5hbWUsXG4gICAxNDhcdFx0XHRTdGF0dXM6ICAgICAgQ2FsbFN0YXR1c1BlbmRpbmcsXG4gICAxNDlcdFx0XHRUeXBlOiAgICAgICAgQ2FsbFR5cGVBSVRvQ3VzdG9tZXIsIC8vIERlZmF1bHQgdG8gQUkgY2FsbGluZyBjdXN0b21lclxuICAgMTUwXHRcdFx0U3RhcnRUaW1lOiAgIHRpbWUuTm93KCksXG4gICAxNTFcdFx0XHRUcmFuc2NyaXB0OiAgbWFrZShbXU1lc3NhZ2UsIDApLFxuICAgMTUyXHRcdFx0TWV0YWRhdGE6IENhbGxNZXRhZGF0YXtcbiAgIDE1M1x0XHRcdFx0UHVycG9zZTogICAgICAgcHVycG9zZSxcbiAgIDE1NFx0XHRcdFx0Q3VzdG9tZXJJbmZvOiAgY3VzdG9tZXJJbmZvLFxuICAgMTU1XHRcdFx0XHRBSVBlcnNvbmFsaXR5OiBcImhlbHBmdWxcIixcbiAgIDE1Nlx0XHRcdFx0TGFuZ3VhZ2U6ICAgICAgXCJlblwiLFxuICAgMTU3XHRcdFx0XHRUYWdzOiAgICAgICAgICBtYWtlKFtdc3RyaW5nLCAwKSxcbiAgIDE1OFx0XHRcdFx0Q3VzdG9tRmllbGRzOiAgbWFrZShtYXBbc3RyaW5nXXN0cmluZyksXG4gICAxNTlcdFx0XHR9LFxuICAgMTYwXHRcdH1cbiAgIDE2MVx0fVxuICAgMTYyXHRcbiAgIDE2M1x0Ly8gTmV3Q3VzdG9tZXJDYWxsIGNyZWF0ZXMgYSBuZXcgY2FsbCBpbnN0YW5jZSB3aGVyZSBjdXN0b21lciBjYWxscyBBSVxuICAgMTY0XHRmdW5jIE5ld0N1c3RvbWVyQ2FsbChjdXN0b21lcklELCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTY1XHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNjZcdFx0Y2hhbm5lbE5hbWUgOj0gXCJjdXN0b21lcl9haV9cIiArIGNhbGxJRFxuICAgMTY3XHRcbiAgIDE2OFx0XHRyZXR1cm4gJkNhbGx7XG4gICAxNjlcdFx0XHRJRDogICAgICAgICAgY2FsbElELFxuICAgMTcwXHRcdFx0Q3VzdG9tZXJJRDogIGN1c3RvbWVySUQsXG4gICAxNzFcdFx0XHRDaGFubmVsTmFtZTogY2hhbm5lbE5hbWUsXG4gICAxNzJcdFx0XHRTdGF0dXM6ICAgICAgQ2FsbFN0YXR1c1BlbmRpbmcsXG4gICAxNzNcdFx0XHRUeXBlOiAgICAgICAgQ2FsbFR5cGVDdXN0b21lclRvQUksIC8vIEN1c3RvbWVyIGNhbGxpbmcgQUlcbiAgIDE3NFx0XHRcdFN0YXJ0VGltZTogICB0aW1lLk5vdygpLFxuICAgMTc1XHRcdFx0VHJhbnNjcmlwdDogIG1ha2UoW11NZXNzYWdlLCAwKSxcbiAgIDE3Nlx0XHRcdE1ldGFkYXRhOiBDYWxsTWV0YWRhdGF7XG4gICAxNzdcdFx0XHRcdFB1cnBvc2U6ICAgICAgIHB1cnBvc2UsXG4gICAxNzhcdFx0XHRcdEN1c3RvbWVySW5mbzogIGN1c3RvbWVySW5mbyxcbiAgIDE3OVx0XHRcdFx0QUlQZXJzb25hbGl0eTogXCJoZWxwZnVsXCIsXG4gICAxODBcdFx0XHRcdExhbmd1YWdlOiAgICAgIFwiZW5cIixcbiAgIDE4MVx0XHRcdFx0VGFnczogICAgICAgICAgbWFrZShbXXN0cmluZywgMCksXG4gICAxODJcdFx0XHRcdEN1c3RvbUZpZWxkczogIG1ha2UobWFwW3N0cmluZ11zdHJpbmcpLFxuICAgMTgzXHRcdFx0fSxcbiAgIDE4NFx0XHR9XG4gICAxODVcdH1cbi4uLlxuUGF0aDogc2VydmljZXMvY2FsbF9tYW5hZ2VyLmdvXG4uLi5cbiAgICAyOVx0XG4gICAgMzBcdC8vIEluaXRpYXRlQ2FsbCBzdGFydHMgYSBuZXcgQUkgY3VzdG9tZXIgc3VwcG9ydCBjYWxsIChBSSBjYWxscyBjdXN0b21lcilcbiAgICAzMVx0ZnVuYyAoY20gKkNhbGxNYW5hZ2VyKSBJbml0aWF0ZUNhbGwocmVxdWVzdCBtb2RlbHMuSW5pdGlhdGVDYWxsUmVxdWVzdCkgKCptb2RlbHMuQ2FsbCwgZXJyb3IpIHtcbiAgICAzMlx0XHRjbS5tdXRleC5Mb2NrKClcbiAgICAzM1x0XHRkZWZlciBjbS5tdXRleC5VbmxvY2soKVxuICAgIDM0XHRcbiAgICAzNVx0XHRsb2cuUHJpbnRmKFwi8J+agCBJbml0aWF0aW5nIG5ldyBBSS10by1jdXN0b21lciBjYWxsIGZvciBjdXN0b21lcjogJXMsIHB1cnBvc2U6ICVzXCIsIHJlcXVlc3QuQ3VzdG9tZXJJRCwgcmVxdWVzdC5QdXJwb3NlKVxuICAgIDM2XHRcbiAgICAzN1x0XHQvLyBDcmVhdGUgbmV3IGNhbGxcbiAgICAzOFx0XHRjYWxsIDo9IG1vZGVscy5OZXdDYWxsKHJlcXVlc3QuQ3VzdG9tZXJJRCwgcmVxdWVzdC5QdXJwb3NlLCByZXF1ZXN0LkN1c3RvbWVySW5mbylcbiAgICAzOVx0XG4gICAgNDBcdFx0Ly8gU2V0IGFkZGl0aW9uYWwgbWV0YWRhdGFcbiAgICA0MVx0XHRpZiByZXF1ZXN0LkFJUGVyc29uYWxpdHkgIT0gXCJcIiB7XG4gICAgNDJcdFx0XHRjYWxsLk1ldGFkYXRhLkFJUGVyc29uYWxpdHkgPSByZXF1ZXN0LkFJUGVyc29uYWxpdHlcbiAgICA0M1x0XHR9XG4gICAgNDRcdFx0aWYgcmVxdWVzdC5MYW5ndWFnZSAhPSBcIlwiIHtcbiAgICA0NVx0XHRcdGNhbGwuTWV0YWRhdGEuTGFuZ3VhZ2UgPSByZXF1ZXN0Lkxhbmd1YWdlXG4gICAgNDZcdFx0fVxuICAgIDQ3XHRcdGlmIHJlcXVlc3QuQ3VzdG9tRmllbGRzICE9IG5pbCB7XG4gICAgNDhcdFx0XHRjYWxsLk1ldGFkYXRhLkN1c3RvbUZpZWxkcyA9IHJlcXVlc3QuQ3VzdG9tRmllbGRzXG4gICAgNDlcdFx0fVxuLi4uXG4gICAgNjlcdFxuICAgIDcwXHQvLyBJbml0aWF0ZUN1c3RvbWVyQ2FsbCBzdGFydHMgYSBuZXcgY3VzdG9tZXItdG8tQUkgY2FsbCAoY3VzdG9tZXIgY2FsbHMgQUkpXG4gICAgNzFcdGZ1bmMgKGNtICpDYWxsTWFuYWdlcikgSW5pdGlhdGVDdXN0b21lckNhbGwocmVxdWVzdCBtb2RlbHMuSW5pdGlhdGVDdXN0b21lckNhbGxSZXF1ZXN0KSAoKm1vZGVscy5DYWxsLCBlcnJvcikge1xuICAgIDcyXHRcdGNtLm11dGV4LkxvY2soKVxuICAgIDczXHRcdGRlZmVyIGNtLm11dGV4LlVubG9jaygpXG4gICAgNzRcdFxuICAgIDc1XHRcdGxvZy5QcmludGYoXCLwn5OeIEluaXRpYXRpbmcgbmV3IGN1c3RvbWVyLXRvLUFJIGNhbGwgZm9yIGN1c3RvbWVyOiAlcywgcHVycG9zZTogJXNcIiwgcmVxdWVzdC5DdXN0b21lcklELCByZXF1ZXN0LlB1cnBvc2UpXG4gICAgNzZcdFxuICAgIDc3XHRcdC8vIENyZWF0ZSBuZXcgY3VzdG9tZXIgY2FsbFxuICAgIDc4XHRcdGNhbGwgOj0gbW9kZWxzLk5ld0N1c3RvbWVyQ2FsbChyZXF1ZXN0LkN1c3RvbWVySUQsIHJlcXVlc3QuUHVycG9zZSwgcmVxdWVzdC5DdXN0b21lckluZm8pXG4gICAgNzlcdFxuICAgIDgwXHRcdC8vIFNldCBhZGRpdGlvbmFsIG1ldGFkYXRhXG4gICAgODFcdFx0aWYgcmVxdWVzdC5BSVBlcnNvbmFsaXR5ICE9IFwiXCIge1xuICAgIDgyXHRcdFx0Y2FsbC5NZXRhZGF0YS5BSVBlcnNvbmFsaXR5ID0gcmVxdWVzdC5BSVBlcnNvbmFsaXR5XG4gICAgODNcdFx0fVxuICAgIDg0XHRcdGlmIHJlcXVlc3QuTGFuZ3VhZ2UgIT0gXCJcIiB7XG4gICAgODVcdFx0XHRjYWxsLk1ldGFkYXRhLkxhbmd1YWdlID0gcmVxdWVzdC5MYW5ndWFnZVxuICAgIDg2XHRcdH1cbiAgICA4N1x0XHRpZiByZXF1ZXN0LkN1c3RvbUZpZWxkcyAhPSBuaWwge1xuICAgIDg4XHRcdFx0Y2FsbC5NZXRhZGF0YS5DdXN0b21GaWVsZHMgPSByZXF1ZXN0LkN1c3RvbUZpZWxkc1xuICAgIDg5XHRcdH1cbi4uLlxuICAgMTA0XHRcbiAgIDEwNVx0Ly8gSm9pbkNhbGwgYWxsb3dzIGEgY3VzdG9tZXIgdG8gam9pbiBhbiBleGlzdGluZyBjYWxsXG4gICAxMDZcdGZ1bmMgKGNtICpDYWxsTWFuYWdlcikgSm9pbkNhbGwoY2FsbElELCBjdXN0b21lcklEIHN0cmluZykgKCptb2RlbHMuQ2FsbCwgZXJyb3IpIHtcbiAgIDEwN1x0XHRjbS5tdXRleC5Mb2NrKClcbiAgIDEwOFx0XHRkZWZlciBjbS5tdXRleC5VbmxvY2soKVxuICAgMTA5XHRcbiAgIDExMFx0XHRjYWxsLCBleGlzdHMgOj0gY20uY2FsbHNbY2FsbElEXVxuICAgMTExXHRcdGlmICFleGlzdHMge1xuICAgMTEyXHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImNhbGwgbm90IGZvdW5kOiAlc1wiLCBjYWxsSUQpXG4gICAxMTNcdFx0fVxuICAgMTE0XHRcbiAgIDExNVx0XHRpZiBjYWxsLkN1c3RvbWVySUQgIT0gY3VzdG9tZXJJRCB7XG4gICAxMTZcdFx0XHRyZXR1cm4gbmlsLCBmbXQuRXJyb3JmKFwidW5hdXRob3JpemVkOiBjdXN0b21lciAlcyBjYW5ub3Qgam9pbiBjYWxsICVzXCIsIGN1c3RvbWVySUQsIGNhbGxJRClcbiAgIDExN1x0XHR9XG4gICAxMThcdFxuICAgMTE5XHRcdGlmIGNhbGwuU3RhdHVzICE9IG1vZGVscy5DYWxsU3RhdHVzUmluZ2luZyAmJiBjYWxsLlN0YXR1cyAhPSBtb2RlbHMuQ2FsbFN0YXR1c1BlbmRpbmcge1xuICAgMTIwXHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImNhbGwgJXMgaXMgbm90IGF2YWlsYWJsZSBmb3Igam9pbmluZyAoc3RhdHVzOiAlcylcIiwgY2FsbElELCBjYWxsLlN0YXR1cylcbiAgIDEyMVx0XHR9XG4gICAxMjJcdFxuICAgMTIzXHRcdGxvZy5QcmludGYoXCLwn5GkIEN1c3RvbWVyICVzIGpvaW5pbmcgY2FsbCAlc1wiLCBjdXN0b21lcklELCBjYWxsSUQpXG4gICAxMjRcdFxuICAgMTI1XHRcdC8vIFVwZGF0ZSBjYWxsIHN0YXR1cyB0byBjb25uZWN0ZWRcbiAgIDEyNlx0XHRjYWxsLlVwZGF0ZVN0YXR1cyhtb2RlbHMuQ2FsbFN0YXR1c0Nvbm5lY3RlZClcbiAgIDEyN1x0XG4gICAxMjhcdFx0Ly8gU2VuZCBzdGF0dXMgdXBkYXRlIG5vdGlmaWNhdGlvblxuICAgMTI5XHRcdGlmIGVyciA6PSBjbS5ub3RpZmljYXRpb25TZXJ2aWNlLlNlbmRDYWxsU3RhdHVzVXBkYXRlKGNhbGwsIG1vZGVscy5DYWxsU3RhdHVzQ29ubmVjdGVkKTsgZXJyICE9IG5pbCB7XG4gICAxMzBcdFx0XHRsb2cuUHJpbnRmKFwiRmFpbGVkIHRvIHNlbmQgc3RhdHVzIHVwZGF0ZSBmb3IgY2FsbCAlczogJXZcIiwgY2FsbElELCBlcnIpXG4gICAxMzFcdFx0fVxuICAgMTMyXHRcbiAgIDEzM1x0XHQvLyBBZGQgaW5pdGlhbCBBSSBncmVldGluZ1xuICAgMTM0XHRcdGdyZWV0aW5nIDo9IGNtLmdlbmVyYXRlSW5pdGlhbEdyZWV0aW5nKGNhbGwpXG4gICAxMzVcdFx0Y2FsbC5BZGRNZXNzYWdlKFwiYWlcIiwgZ3JlZXRpbmcsIFwidGV4dFwiKVxuICAgMTM2XHRcbiAgIDEzN1x0XHRsb2cuUHJpbnRmKFwi8J+OiSBDdXN0b21lciBzdWNjZXNzZnVsbHkgam9pbmVkIGNhbGwgJXNcIiwgY2FsbElEKVxuICAgMTM4XHRcdHJldHVybiBjYWxsLCBuaWxcbiAgIDEzOVx0fVxuLi4uXG5QYXRoOiBtYWluLmdvXG4gICAgIDFcdHBhY2thZ2UgbWFpblxuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcIkJhY2tlbmQvaGFuZGxlcnNcIlxuICAgICA2XHRcdFwiQmFja2VuZC9taWRkbGV3YXJlXCJcbiAgICAgN1x0XHRcIkJhY2tlbmQvc2VydmljZXNcIlxuICAgICA4XHRcdFwiZm10XCJcbiAgICAgOVx0XHRcImxvZ1wiXG4gICAgMTBcdFx0XCJuZXQvaHR0cFwiXG4gICAgMTFcdFx0XCJvc1wiXG4gICAgMTJcdFx0XCJvcy9zaWduYWxcIlxuICAgIDEzXHRcdFwic3lzY2FsbFwiXG4gICAgMTRcdFxuICAgIDE1XHRcdFwiZ2l0aHViLmNvbS9naW4tZ29uaWMvZ2luXCJcbiAgICAxNlx0KVxuICAgIDE3XHRcbiAgICAxOFx0ZnVuYyBtYWluKCkge1xuICAgIDE5XHRcdC8vIExvYWQgY29uZmlndXJhdGlvblxuICAgIDIwXHRcdGNvbmZpZy5Mb2FkQ29uZmlnKClcbiAgICAyMVx0XHRjZmcgOj0gY29uZmlnLkFwcENvbmZpZ1xuICAgIDIyXHRcbiAgICAyM1x0XHRsb2cuUHJpbnRmKFwi8J+agCBTdGFydGluZyBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIilcbiAgICAyNFx0XHRsb2cuUHJpbnRmKFwi8J+TiyBTZXJ2ZXI6ICVzOiVzXCIsIGNmZy5TZXJ2ZXIuSG9zdCwgY2ZnLlNlcnZlci5Qb3J0KVxuICAgIDI1XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIEFwcCBJRDogJXNcIiwgY2ZnLkFnb3JhLkFwcElEKVxuICAgIDI2XHRcbiAgICAyN1x0XHQvLyBJbml0aWFsaXplIHNlcnZpY2VzXG4gICAgMjhcdFx0bm90aWZpY2F0aW9uU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdOb3RpZmljYXRpb25TZXJ2aWNlKClcbiAgICAyOVx0XHRhZ29yYVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QWdvcmFTZXJ2aWNlKGNmZylcbiAgICAzMFx0XHRhaVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QUlTZXJ2aWNlKGNmZylcbiAgICAzMVx0XHRjYWxsTWFuYWdlciA6PSBzZXJ2aWNlcy5OZXdDYWxsTWFuYWdlcihub3RpZmljYXRpb25TZXJ2aWNlLCBhaVNlcnZpY2UsIGFnb3JhU2VydmljZSlcbiAgICAzMlx0XG4gICAgMzNcdFx0Ly8gSW5pdGlhbGl6ZSBoYW5kbGVyc1xuICAgIDM0XHRcdGNhbGxIYW5kbGVyIDo9IGhhbmRsZXJzLk5ld0NhbGxIYW5kbGVyKGNhbGxNYW5hZ2VyLCBhZ29yYVNlcnZpY2UpXG4gICAgMzVcdFxuICAgIDM2XHRcdC8vIFNldHVwIEdpbiByb3V0ZXJcbiAgICAzN1x0XHRyb3V0ZXIgOj0gc2V0dXBSb3V0ZXIoY2FsbEhhbmRsZXIpXG4gICAgMzhcdFxuICAgIDM5XHRcdC8vIFNldHVwIGdyYWNlZnVsIHNodXRkb3duXG4gICAgNDBcdFx0c2V0dXBHcmFjZWZ1bFNodXRkb3duKGFnb3JhU2VydmljZSlcbiAgICA0MVx0XG4gICAgNDJcdFx0Ly8gU3RhcnQgc2VydmVyXG4gICAgNDNcdFx0YWRkcmVzcyA6PSBmbXQuU3ByaW50ZihcIiVzOiVzXCIsIGNmZy5TZXJ2ZXIuSG9zdCwgY2ZnLlNlcnZlci5Qb3J0KVxuICAgIDQ0XHRcdGxvZy5QcmludGYoXCLinIUgU2VydmVyIHN0YXJ0aW5nIG9uICVzXCIsIGFkZHJlc3MpXG4gICAgNDVcdFxuICAgIDQ2XHRcdGlmIGVyciA6PSByb3V0ZXIuUnVuKGFkZHJlc3MpOyBlcnIgIT0gbmlsIHtcbiAgICA0N1x0XHRcdGxvZy5GYXRhbGYoXCLinYwgRmFpbGVkIHRvIHN0YXJ0IHNlcnZlcjogJXZcIiwgZXJyKVxuICAgIDQ4XHRcdH1cbiAgICA0OVx0fVxuICAgIDUwXHRcbiAgICA1MVx0Ly8gc2V0dXBSb3V0ZXIgY29uZmlndXJlcyB0aGUgR2luIHJvdXRlciB3aXRoIGFsbCByb3V0ZXMgYW5kIG1pZGRsZXdhcmVcbiAgICA1Mlx0ZnVuYyBzZXR1cFJvdXRlcihjYWxsSGFuZGxlciAqaGFuZGxlcnMuQ2FsbEhhbmRsZXIpICpnaW4uRW5naW5lIHtcbiAgICA1M1x0XHQvLyBTZXQgR2luIG1vZGVcbiAgICA1NFx0XHRnaW4uU2V0TW9kZShnaW4uUmVsZWFzZU1vZGUpXG4gICAgNTVcdFxuICAgIDU2XHRcdHJvdXRlciA6PSBnaW4uTmV3KClcbiAgICA1N1x0XG4gICAgNThcdFx0Ly8gQWRkIG1pZGRsZXdhcmVcbiAgICA1OVx0XHRyb3V0ZXIuVXNlKG1pZGRsZXdhcmUuTG9nZ2VyTWlkZGxld2FyZSgpKVxuICAgIDYwXHRcdHJvdXRlci5Vc2UobWlkZGxld2FyZS5DT1JTTWlkZGxld2FyZSgpKVxuICAgIDYxXHRcdHJvdXRlci5Vc2UoZ2luLlJlY292ZXJ5KCkpXG4gICAgNjJcdFxuICAgIDYzXHRcdC8vIEhlYWx0aCBjaGVjayBlbmRwb2ludFxuICAgIDY0XHRcdHJvdXRlci5HRVQoXCIvaGVhbHRoXCIsIGNhbGxIYW5kbGVyLkhlYWx0aENoZWNrKVxuLi4uXG4gICAgODJcdFxuICAgIDgzXHRcdC8vIFdlbGNvbWUgcm91dGVcbiAgICA4NFx0XHRyb3V0ZXIuR0VUKFwiL1wiLCBmdW5jKGMgKmdpbi5Db250ZXh0KSB7XG4gICAgODVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgZ2luLkh7XG4gICAgODZcdFx0XHRcdFwibWVzc2FnZVwiOiBcIvCfpJYgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXCIsXG4gICAgODdcdFx0XHRcdFwidmVyc2lvblwiOiBcIjEuMC4wXCIsXG4gICAgODhcdFx0XHRcdFwic3RhdHVzXCI6ICBcInJ1bm5pbmdcIixcbiAgICA4OVx0XHRcdFx0XCJlbmRwb2ludHNcIjogZ2luLkh7XG4gICAgOTBcdFx0XHRcdFx0XCJoZWFsdGhcIjogICAgICAgICAgICAgICAgXCJHRVQgL2hlYWx0aFwiLFxuICAgIDkxXHRcdFx0XHRcdFwiaW5pdGlhdGVfY2FsbFwiOiAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzL2luaXRpYXRlXCIsXG4gICAgOTJcdFx0XHRcdFx0XCJjdXN0b21lcl9pbml0aWF0ZVwiOiAgICAgXCJQT1NUIC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGVcIixcbiAgICA5M1x0XHRcdFx0XHRcImpvaW5fY2FsbFwiOiAgICAgICAgICAgICBcIlBPU1QgL2FwaS9jYWxscy86Y2FsbF9pZC9qb2luXCIsXG4gICAgOTRcdFx0XHRcdFx0XCJnZXRfY2FsbFwiOiAgICAgICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy86Y2FsbF9pZFwiLFxuICAgIDk1XHRcdFx0XHRcdFwiZW5kX2NhbGxcIjogICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2VuZFwiLFxuICAgIDk2XHRcdFx0XHRcdFwiYWN0aXZlX2NhbGxzXCI6ICAgICAgICAgIFwiR0VUIC9hcGkvY2FsbHMvYWN0aXZlXCIsXG4gICAgOTdcdFx0XHRcdFx0XCJjYWxsX3N0YXRzXCI6ICAgICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy86Y2FsbF9pZC9zdGF0c1wiLFxuICAgIDk4XHRcdFx0XHRcdFwiZ2VuZXJhdGVfdG9rZW5cIjogICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3Rva2VuXCIsXG4gICAgOTlcdFx0XHRcdH0sXG4gICAxMDBcdFx0XHR9KVxuICAgMTAxXHRcdH0pXG4gICAxMDJcdFxuICAgMTAzXHRcdGxvZy5QcmludGYoXCLwn5uj77iPIFJvdXRlcyBjb25maWd1cmVkIHN1Y2Nlc3NmdWxseVwiKVxuICAgMTA0XHRcdHJldHVybiByb3V0ZXJcbiAgIDEwNVx0fVxuLi4uXG4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI1ZTk2NmY5OC0yNzMxLTRjMTgtYTZkYy0xMDIyNjIwMWZhYjU7dG9vbHVfdnJ0eF8wMUJKMnhrYlJ5dDNaUkh2SkFxcDNjOTkiOnsicmVxdWVzdElkIjoiNWU5NjZmOTgtMjczMS00YzE4LWE2ZGMtMTAyMjYyMDFmYWI1IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUJKMnhrYlJ5dDNaUkh2SkFxcDNjOTkiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJUaGUgZm9sbG93aW5nIGNvZGUgc2VjdGlvbnMgd2VyZSByZXRyaWV2ZWQ6XG5QYXRoOiBzZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvXG4gICAgIDFcdHBhY2thZ2Ugc2VydmljZXNcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJCYWNrZW5kL2NvbmZpZ1wiXG4gICAgIDVcdFx0XCJmbXRcIlxuICAgICA2XHRcdFwibG9nXCJcbiAgICAgN1x0XHRcInN0cmNvbnZcIlxuICAgICA4XHRcdFwidGltZVwiXG4gICAgIDlcdFxuICAgIDEwXHRcdFwiZ2l0aHViLmNvbS9BZ29yYUlPL1Rvb2xzL0R5bmFtaWNLZXkvQWdvcmFEeW5hbWljS2V5L2dvL3NyYy9ydGN0b2tlbmJ1aWxkZXIyXCJcbiAgICAxMVx0KVxuICAgIDEyXHRcbiAgICAxM1x0Ly8gQWdvcmFTZXJ2aWNlIGhhbmRsZXMgQWdvcmEgUlRDIG9wZXJhdGlvbnNcbiAgICAxNFx0dHlwZSBBZ29yYVNlcnZpY2Ugc3RydWN0IHtcbiAgICAxNVx0XHRjb25maWcgKmNvbmZpZy5Db25maWdcbiAgICAxNlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNvbnRhaW46XG4gICAgMTdcdFx0Ly8gLSBBZ29yYSBzZXJ2aWNlIGluc3RhbmNlXG4gICAgMThcdFx0Ly8gLSBNZWRpYSBub2RlIGZhY3RvcnlcbiAgICAxOVx0XHQvLyAtIEFjdGl2ZSBjb25uZWN0aW9ucyBtYXBcbiAgICAyMFx0XHQvLyAtIEF1ZGlvL1ZpZGVvIG9ic2VydmVyc1xuICAgIDIxXHR9XG4gICAgMjJcdFxuICAgIDIzXHQvLyBUb2tlblJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgZm9yIEFnb3JhIHRva2VuXG4gICAgMjRcdHR5cGUgVG9rZW5SZXF1ZXN0IHN0cnVjdCB7XG4gICAgMjVcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMjZcdFx0VXNlcklEICAgICAgc3RyaW5nIGBqc29uOlwidXNlcl9pZFwiYFxuICAgIDI3XHRcdFJvbGUgICAgICAgIGludCAgICBganNvbjpcInJvbGVcImAgLy8gMSA9IHB1Ymxpc2hlciwgMiA9IHN1YnNjcmliZXJcbiAgICAyOFx0fVxuICAgIDI5XHRcbiAgICAzMFx0Ly8gVG9rZW5SZXNwb25zZSByZXByZXNlbnRzIEFnb3JhIHRva2VuIHJlc3BvbnNlXG4gICAgMzFcdHR5cGUgVG9rZW5SZXNwb25zZSBzdHJ1Y3Qge1xuICAgIDMyXHRcdFRva2VuICAgICBzdHJpbmcgYGpzb246XCJ0b2tlblwiYFxuICAgIDMzXHRcdEV4cGlyZXNBdCBpbnQ2NCAgYGpzb246XCJleHBpcmVzX2F0XCJgXG4gICAgMzRcdH1cbiAgICAzNVx0XG4gICAgMzZcdC8vIE5ld0Fnb3JhU2VydmljZSBjcmVhdGVzIGEgbmV3IEFnb3JhIHNlcnZpY2UgaW5zdGFuY2VcbiAgICAzN1x0ZnVuYyBOZXdBZ29yYVNlcnZpY2UoY2ZnICpjb25maWcuQ29uZmlnKSAqQWdvcmFTZXJ2aWNlIHtcbiAgICAzOFx0XHRzZXJ2aWNlIDo9ICZBZ29yYVNlcnZpY2V7XG4gICAgMzlcdFx0XHRjb25maWc6IGNmZyxcbiAgICA0MFx0XHR9XG4gICAgNDFcdFxuICAgIDQyXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIGluaXRpYWxpemUgQWdvcmEgU0RLIGhlcmU6XG4gICAgNDNcdFx0Ly8gMS4gSW5pdGlhbGl6ZSBBZ29yYSBzZXJ2aWNlIHdpdGggYXBwIElEXG4gICAgNDRcdFx0Ly8gMi4gQ3JlYXRlIG1lZGlhIG5vZGUgZmFjdG9yeVxuICAgIDQ1XHRcdC8vIDMuIFNldCB1cCBhdWRpby92aWRlbyBwcm9jZXNzaW5nIGNhbGxiYWNrc1xuICAgIDQ2XHRcbiAgICA0N1x0XHRsb2cuUHJpbnRmKFwi8J+OryBBZ29yYSBzZXJ2aWNlIGluaXRpYWxpemVkIHdpdGggQXBwIElEOiAlc1wiLCBjZmcuQWdvcmEuQXBwSUQpXG4gICAgNDhcdFx0cmV0dXJuIHNlcnZpY2VcbiAgICA0OVx0fVxuICAgIDUwXHRcbiAgICA1MVx0Ly8gR2VuZXJhdGVUb2tlbiBnZW5lcmF0ZXMgYW4gQWdvcmEgUlRDIHRva2VuIGZvciBjaGFubmVsIGFjY2Vzc1xuICAgIDUyXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBHZW5lcmF0ZVRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgKCpUb2tlblJlc3BvbnNlLCBlcnJvcikge1xuICAgIDUzXHRcdGxvZy5QcmludGYoXCLwn5SRIEdlbmVyYXRpbmcgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB1c2VyOiAlcywgcm9sZTogJWRcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRCwgcm9sZSlcbiAgICA1NFx0XG4gICAgNTVcdFx0Ly8gVG9rZW4gZXhwaXJlcyBpbiAyNCBob3Vyc1xuICAgIDU2XHRcdGV4cGlyYXRpb25UaW1lIDo9IHRpbWUuTm93KCkuQWRkKDI0ICogdGltZS5Ib3VyKS5Vbml4KClcbiAgICA1N1x0XG4gICAgNThcdFx0dmFyIHRva2VuIHN0cmluZ1xuICAgIDU5XHRcdHZhciBlcnIgZXJyb3JcbiAgICA2MFx0XG4gICAgNjFcdFx0aWYgYXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlICE9IFwiXCIge1xuICAgIDYyXHRcdFx0Ly8gR2VuZXJhdGUgcmVhbCB0b2tlbiB3aXRoIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA2M1x0XHRcdHRva2VuLCBlcnIgPSBhcy5nZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB1aW50MzIoZXhwaXJhdGlvblRpbWUpKVxuICAgIDY0XHRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjVcdFx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gZ2VuZXJhdGUgdG9rZW46ICV3XCIsIGVycilcbiAgICA2Nlx0XHRcdH1cbiAgICA2N1x0XHRcdGxvZy5QcmludGYoXCLinIUgR2VuZXJhdGVkIHJlYWwgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB0b2tlbiBpczogJXNcIiwgY2hhbm5lbE5hbWUsIHRva2VuKVxuICAgIDY4XHRcdH0gZWxzZSB7XG4gICAgNjlcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgIDcwXHRcdFx0dG9rZW4gPSBhcy5nZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDcxXHRcdFx0bG9nLlByaW50ZihcIuKaoO+4jyBbTU9DS10gVXNpbmcgbW9jayB0b2tlbiAobm8gYXBwIGNlcnRpZmljYXRlIHByb3ZpZGVkKVwiKVxuICAgIDcyXHRcdH1cbiAgICA3M1x0XG4gICAgNzRcdFx0cmVzcG9uc2UgOj0gJlRva2VuUmVzcG9uc2V7XG4gICAgNzVcdFx0XHRUb2tlbjogICAgIHRva2VuLFxuICAgIDc2XHRcdFx0RXhwaXJlc0F0OiBleHBpcmF0aW9uVGltZSxcbiAgICA3N1x0XHR9XG4gICAgNzhcdFxuICAgIDc5XHRcdGxvZy5QcmludGYoXCLinIUgVG9rZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgODBcdFx0cmV0dXJuIHJlc3BvbnNlLCBuaWxcbiAgICA4MVx0fVxuICAgIDgyXHRcbiAgICA4M1x0Ly8gZ2VuZXJhdGVSZWFsVG9rZW4gZ2VuZXJhdGVzIGEgcmVhbCBBZ29yYSB0b2tlbiB1c2luZyB0aGUgb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDg0XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZywgcm9sZSBpbnQsIGV4cGlyYXRpb24gdWludDMyKSAoc3RyaW5nLCBlcnJvcikge1xuICAgIDg1XHRcdC8vIENvbnZlcnQgdXNlcklEIHRvIHVpbnQzMiAoQWdvcmEgZXhwZWN0cyBudW1lcmljIFVJRClcbiAgICA4Nlx0XHR2YXIgdWlkIHVpbnQzMlxuICAgIDg3XHRcdGlmIHVzZXJJRCAhPSBcIlwiIHtcbiAgICA4OFx0XHRcdGlmIHBhcnNlZFVJRCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VVaW50KHVzZXJJRCwgMTAsIDMyKTsgZXJyID09IG5pbCB7XG4gICAgODlcdFx0XHRcdHVpZCA9IHVpbnQzMihwYXJzZWRVSUQpXG4gICAgOTBcdFx0XHR9IGVsc2Uge1xuICAgIDkxXHRcdFx0XHQvLyBJZiB1c2VySUQgaXMgbm90IG51bWVyaWMsIGdlbmVyYXRlIGEgaGFzaC1iYXNlZCBVSURcbiAgICA5Mlx0XHRcdFx0dWlkID0gdWludDMyKGxlbih1c2VySUQpKSAvLyBTaW1wbGUgZmFsbGJhY2ssIGluIHByb2R1Y3Rpb24gdXNlIGEgcHJvcGVyIGhhc2hcbiAgICA5M1x0XHRcdH1cbiAgICA5NFx0XHR9IGVsc2Uge1xuICAgIDk1XHRcdFx0dWlkID0gMCAvLyBMZXQgQWdvcmEgYXNzaWduIGEgVUlEXG4gICAgOTZcdFx0fVxuICAgIDk3XHRcbiAgICA5OFx0XHQvLyBDb252ZXJ0IHJvbGUgdG8gQWdvcmEgcm9sZSB0eXBlXG4gICAgOTlcdFx0dmFyIGFnb3JhUm9sZSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVcbiAgIDEwMFx0XHRpZiByb2xlID09IDEge1xuICAgMTAxXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlUHVibGlzaGVyXG4gICAxMDJcdFx0fSBlbHNlIHtcbiAgIDEwM1x0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVN1YnNjcmliZXJcbiAgIDEwNFx0XHR9XG4gICAxMDVcdFxuICAgMTA2XHRcdC8vIEdlbmVyYXRlIHRva2VuIHVzaW5nIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgIDEwN1x0XHR0b2tlbiwgZXJyIDo9IHJ0Y3Rva2VuYnVpbGRlcjIuQnVpbGRUb2tlbldpdGhVaWQoXG4gICAxMDhcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwSUQsXG4gICAxMDlcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUsXG4gICAxMTBcdFx0XHRjaGFubmVsTmFtZSxcbiAgIDExMVx0XHRcdHVpZCxcbiAgIDExMlx0XHRcdGFnb3JhUm9sZSxcbiAgIDExM1x0XHRcdGV4cGlyYXRpb24sXG4gICAxMTRcdFx0XHRleHBpcmF0aW9uLCAvLyBQcml2aWxlZ2UgZXhwaXJlZCB0aW1lc3RhbXAgKHNhbWUgYXMgdG9rZW4gZXhwaXJhdGlvbilcbiAgIDExNVx0XHQpXG4gICAxMTZcdFxuICAgMTE3XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTE4XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gYnVpbGQgQWdvcmEgdG9rZW46ICV3XCIsIGVycilcbiAgIDExOVx0XHR9XG4gICAxMjBcdFxuICAgMTIxXHRcdHJldHVybiB0b2tlbiwgbmlsXG4gICAxMjJcdH1cbiAgIDEyM1x0XG4gICAxMjRcdC8vIGdlbmVyYXRlTW9ja1Rva2VuIGdlbmVyYXRlcyBhIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAxMjVcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nLCByb2xlIGludCkgc3RyaW5nIHtcbiAgIDEyNlx0XHR0aW1lc3RhbXAgOj0gdGltZS5Ob3coKS5Vbml4KClcbiAgIDEyN1x0XHRyZXR1cm4gZm10LlNwcmludGYoXCJtb2NrX3Rva2VuXyVzXyVzXyVkXyVkXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHRpbWVzdGFtcClcbiAgIDEyOFx0fVxuICAgMTI5XHRcbiAgIDEzMFx0Ly8gSm9pbkNoYW5uZWwgam9pbnMgYW4gQWdvcmEgY2hhbm5lbCBmb3IgQUkgcHJvY2Vzc2luZ1xuICAgMTMxXHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBKb2luQ2hhbm5lbChjaGFubmVsTmFtZSwgdXNlcklEIHN0cmluZykgZXJyb3Ige1xuICAgMTMyXHRcdGxvZy5QcmludGYoXCLwn5SXIFtNT0NLXSBBSSBqb2luaW5nIEFnb3JhIGNoYW5uZWw6ICVzIGFzIHVzZXI6ICVzXCIsIGNoYW5uZWxOYW1lLCB1c2VySUQpXG4gICAxMzNcdFxuICAgMTM0XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxMzVcdFx0Ly8gMS4gQ3JlYXRlIFJUQyBjb25uZWN0aW9uXG4gICAxMzZcdFx0Ly8gMi4gU2V0IHVwIGxvY2FsIHVzZXJcbiAgIDEzN1x0XHQvLyAzLiBDcmVhdGUgYXVkaW8gdHJhY2sgZm9yIEFJIHZvaWNlXG4gICAxMzhcdFx0Ly8gNC4gUmVnaXN0ZXIgYXVkaW8gZnJhbWUgb2JzZXJ2ZXJzIGZvciBwcm9jZXNzaW5nIGluY29taW5nIGF1ZGlvXG4gICAxMzlcdFx0Ly8gNS4gSm9pbiB0aGUgY2hhbm5lbFxuICAgMTQwXHRcdC8vIDYuIFN0YXJ0IGF1ZGlvIHByb2Nlc3NpbmcgcGlwZWxpbmVcbiAgIDE0MVx0XG4gICAxNDJcdFx0Ly8gTW9jayBpbXBsZW1lbnRhdGlvblxuICAgMTQzXHRcdHRpbWUuU2xlZXAoNTAwICogdGltZS5NaWxsaXNlY29uZCkgLy8gU2ltdWxhdGUgY29ubmVjdGlvbiB0aW1lXG4gICAxNDRcdFxuICAgMTQ1XHRcdGxvZy5QcmludGYoXCLinIUgW01PQ0tdIEFJIHN1Y2Nlc3NmdWxseSBqb2luZWQgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNDZcdFx0cmV0dXJuIG5pbFxuICAgMTQ3XHR9XG4gICAxNDhcdFxuICAgMTQ5XHQvLyBMZWF2ZUNoYW5uZWwgbGVhdmVzIGFuIEFnb3JhIGNoYW5uZWxcbiAgIDE1MFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgTGVhdmVDaGFubmVsKGNhbGxJRCBzdHJpbmcpIGVycm9yIHtcbiAgIDE1MVx0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAxNTJcdFx0bG9nLlByaW50ZihcIvCfkYsgW01PQ0tdIEFJIGxlYXZpbmcgQWdvcmEgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxNTNcdFxuICAgMTU0XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxNTVcdFx0Ly8gMS4gU3RvcCBhdWRpbyBwcm9jZXNzaW5nXG4gICAxNTZcdFx0Ly8gMi4gVW5wdWJsaXNoIGF1ZGlvIHRyYWNrc1xuICAgMTU3XHRcdC8vIDMuIERpc2Nvbm5lY3QgZnJvbSBjaGFubmVsXG4gICAxNThcdFx0Ly8gNC4gQ2xlYW4gdXAgcmVzb3VyY2VzXG4gICAxNTlcdFxuICAgMTYwXHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb25cbiAgIDE2MVx0XHR0aW1lLlNsZWVwKDIwMCAqIHRpbWUuTWlsbGlzZWNvbmQpIC8vIFNpbXVsYXRlIGRpc2Nvbm5lY3Rpb24gdGltZVxuICAgMTYyXHRcbiAgIDE2M1x0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgbGVmdCBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDE2NFx0XHRyZXR1cm4gbmlsXG4gICAxNjVcdH1cbiAgIDE2Nlx0XG4gICAxNjdcdC8vIFNlbmRBdWRpb1RvQ2hhbm5lbCBzZW5kcyBBSS1nZW5lcmF0ZWQgYXVkaW8gdG8gdGhlIGNoYW5uZWxcbiAgIDE2OFx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgU2VuZEF1ZGlvVG9DaGFubmVsKGNhbGxJRCBzdHJpbmcsIGF1ZGlvRGF0YSBbXWJ5dGUpIGVycm9yIHtcbiAgIDE2OVx0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAxNzBcdFx0bG9nLlByaW50ZihcIvCflIogW01PQ0tdIFNlbmRpbmcgJWQgYnl0ZXMgb2YgYXVkaW8gdG8gY2hhbm5lbDogJXNcIiwgbGVuKGF1ZGlvRGF0YSksIGNoYW5uZWxOYW1lKVxuICAgMTcxXHRcbiAgIDE3Mlx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMTczXHRcdC8vIDEuIENvbnZlcnQgYXVkaW8gZGF0YSB0byByZXF1aXJlZCBmb3JtYXQgKFBDTSlcbiAgIDE3NFx0XHQvLyAyLiBQdXNoIGF1ZGlvIGZyYW1lcyB0byBBZ29yYSBhdWRpbyB0cmFja1xuICAgMTc1XHRcdC8vIDMuIEhhbmRsZSBhdWRpbyBlbmNvZGluZyBhbmQgdHJhbnNtaXNzaW9uXG4gICAxNzZcdFxuICAgMTc3XHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb24gLSBzaW11bGF0ZSBhdWRpbyB0cmFuc21pc3Npb24gdGltZVxuICAgMTc4XHRcdHRyYW5zbWlzc2lvblRpbWUgOj0gdGltZS5EdXJhdGlvbihsZW4oYXVkaW9EYXRhKS8xMDAwKSAqIHRpbWUuTWlsbGlzZWNvbmRcbiAgIDE3OVx0XHRpZiB0cmFuc21pc3Npb25UaW1lID4gMTAwKnRpbWUuTWlsbGlzZWNvbmQge1xuICAgMTgwXHRcdFx0dHJhbnNtaXNzaW9uVGltZSA9IDEwMCAqIHRpbWUuTWlsbGlzZWNvbmRcbiAgIDE4MVx0XHR9XG4uLi5cbiAgIDE4N1x0XG4gICAxODhcdC8vIFNldHVwQXVkaW9Qcm9jZXNzaW5nIHNldHMgdXAgYXVkaW8gcHJvY2Vzc2luZyBjYWxsYmFja3NcbiAgIDE4OVx0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgU2V0dXBBdWRpb1Byb2Nlc3NpbmcoY2FsbElEIHN0cmluZywgYXVkaW9DYWxsYmFjayBmdW5jKFtdYnl0ZSkgZXJyb3IpIGVycm9yIHtcbiAgIDE5MFx0XHRjaGFubmVsTmFtZSA6PSBhcy5nZXRDaGFubmVsTmFtZShjYWxsSUQpXG4gICAxOTFcdFx0bG9nLlByaW50ZihcIvCfjpnvuI8gW01PQ0tdIFNldHRpbmcgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAxOTJcdFxuICAgMTkzXHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQ6XG4gICAxOTRcdFx0Ly8gMS4gUmVnaXN0ZXIgYXVkaW8gZnJhbWUgb2JzZXJ2ZXJcbiAgIDE5NVx0XHQvLyAyLiBTZXQgdXAgVkFEIChWb2ljZSBBY3Rpdml0eSBEZXRlY3Rpb24pXG4gICAxOTZcdFx0Ly8gMy4gQ29uZmlndXJlIGF1ZGlvIGZvcm1hdCAoc2FtcGxlIHJhdGUsIGNoYW5uZWxzLCBldGMuKVxuICAgMTk3XHRcdC8vIDQuIFN0YXJ0IHByb2Nlc3NpbmcgaW5jb21pbmcgYXVkaW8gZnJhbWVzXG4gICAxOThcdFxuICAgMTk5XHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb24gLSBzaW11bGF0ZSByZWNlaXZpbmcgYXVkaW8gcGVyaW9kaWNhbGx5XG4gICAyMDBcdFx0Z28gYXMuc2ltdWxhdGVJbmNvbWluZ0F1ZGlvKGNhbGxJRCwgYXVkaW9DYWxsYmFjaylcbiAgIDIwMVx0XG4gICAyMDJcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQXVkaW8gcHJvY2Vzc2luZyBzZXR1cCBjb21wbGV0ZSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAyMDNcdFx0cmV0dXJuIG5pbFxuICAgMjA0XHR9XG4uLi5cblBhdGg6IGhhbmRsZXJzL2NhbGxfaGFuZGxlci5nb1xuLi4uXG4gICAgNjJcdFxuICAgIDYzXHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY2hhbm5lbFxuICAgIDY0XHRcdHRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgXCJhaV9hZ2VudFwiLCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgICA2NVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2Nlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDY3XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgQWdvcmEgdG9rZW5cIixcbiAgICA2OFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDY5XHRcdFx0fSlcbiAgICA3MFx0XHRcdHJldHVyblxuICAgIDcxXHRcdH1cbiAgICA3Mlx0XG4gICAgNzNcdFx0Ly8gSm9pbiB0aGUgY2hhbm5lbCBhcyBBSSBhZ2VudFxuICAgIDc0XHRcdGlmIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuSm9pbkNoYW5uZWwoY2FsbC5DaGFubmVsTmFtZSwgXCJhaV9hZ2VudFwiKTsgZXJyICE9IG5pbCB7XG4gICAgNzVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgICA3Nlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGpvaW4gQWdvcmEgY2hhbm5lbFwiLFxuICAgIDc3XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgNzhcdFx0XHR9KVxuICAgIDc5XHRcdFx0cmV0dXJuXG4gICAgODBcdFx0fVxuICAgIDgxXHRcbiAgICA4Mlx0XHQvLyBTZXQgdXAgYXVkaW8gcHJvY2Vzc2luZyBmb3IgdGhpcyBjYWxsXG4gICAgODNcdFx0YXVkaW9DYWxsYmFjayA6PSBmdW5jKGF1ZGlvRGF0YSBbXWJ5dGUpIGVycm9yIHtcbiAgICA4NFx0XHRcdHJldHVybiBjaC5jYWxsTWFuYWdlci5Qcm9jZXNzQXVkaW9NZXNzYWdlKGNhbGwuSUQsIGF1ZGlvRGF0YSlcbiAgICA4NVx0XHR9XG4uLi5cbiAgIDEzMVx0XG4gICAxMzJcdFx0Ly8gSW5pdGlhdGUgdGhlIGN1c3RvbWVyLXRvLUFJIGNhbGxcbiAgIDEzM1x0XHRjYWxsLCBlcnIgOj0gY2guY2FsbE1hbmFnZXIuSW5pdGlhdGVDdXN0b21lckNhbGwocmVxdWVzdClcbiAgIDEzNFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDEzNVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMTM2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gaW5pdGlhdGUgY3VzdG9tZXIgY2FsbFwiLFxuICAgMTM3XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAxMzhcdFx0XHR9KVxuICAgMTM5XHRcdFx0cmV0dXJuXG4gICAxNDBcdFx0fVxuICAgMTQxXHRcbiAgIDE0Mlx0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGN1c3RvbWVyXG4gICAxNDNcdFx0Y3VzdG9tZXJUb2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIHJlcXVlc3QuQ3VzdG9tZXJJRCwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAxNDRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxNDVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDE0Nlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIGN1c3RvbWVyIEFnb3JhIHRva2VuXCIsXG4gICAxNDdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDE0OFx0XHRcdH0pXG4gICAxNDlcdFx0XHRyZXR1cm5cbiAgIDE1MFx0XHR9XG4uLi5cbiAgIDI0OFx0XG4gICAyNDlcdFx0Ly8gSm9pbiB0aGUgY2FsbFxuICAgMjUwXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Kb2luQ2FsbChyZXF1ZXN0LkNhbGxJRCwgcmVxdWVzdC5DdXN0b21lcklEKVxuICAgMjUxXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMjUyXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQmFkUmVxdWVzdCwgZ2luLkh7XG4gICAyNTNcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBqb2luIGNhbGxcIixcbiAgIDI1NFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMjU1XHRcdFx0fSlcbiAgIDI1Nlx0XHRcdHJldHVyblxuICAgMjU3XHRcdH1cbiAgIDI1OFx0XG4gICAyNTlcdFx0Ly8gR2VuZXJhdGUgQWdvcmEgdG9rZW4gZm9yIHRoZSBjdXN0b21lclxuICAgMjYwXHRcdHRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgcmVxdWVzdC5DdXN0b21lcklELCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgIDI2MVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDI2Mlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMjYzXHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgQWdvcmEgdG9rZW5cIixcbiAgIDI2NFx0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMjY1XHRcdFx0fSlcbiAgIDI2Nlx0XHRcdHJldHVyblxuICAgMjY3XHRcdH1cbiAgIDI2OFx0XG4gICAyNjlcdFx0cmVzcG9uc2UgOj0gbW9kZWxzLkpvaW5DYWxsUmVzcG9uc2V7XG4gICAyNzBcdFx0XHRDaGFubmVsTmFtZTogY2FsbC5DaGFubmVsTmFtZSxcbiAgIDI3MVx0XHRcdFRva2VuOiAgICAgICB0b2tlbi5Ub2tlbixcbiAgIDI3Mlx0XHRcdENhbGxJbmZvOiAgICAqY2FsbCxcbiAgIDI3M1x0XHR9XG4gICAyNzRcdFxuICAgMjc1XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCByZXNwb25zZSlcbiAgIDI3Nlx0fVxuLi4uXG5QYXRoOiAuZW52XG4gICAgIDFcdCMgU2VydmVyIENvbmZpZ3VyYXRpb25cbiAgICAgMlx0U0VSVkVSX1BPUlQ9ODA4MFxuICAgICAzXHRTRVJWRVJfSE9TVD0wLjAuMC4wXG4gICAgIDRcdFxuICAgICA1XHQjIEFnb3JhIENvbmZpZ3VyYXRpb25cbiAgICAgNlx0QUdPUkFfQVBQX0lEPTAxNDUyMzQ4MGQ5NTQ0YmU4MDBhMDE0ZjM1YmRjZmFjXG4gICAgIDdcdEFHT1JBX0FQUF9DRVJUSUZJQ0FURT1lN2YzN2U2N2Q4MmI0YWZmODQ2MzE3NzRjMWI1ZGU2MVxuICAgICA4XHRBR09SQV9DSEFOTkVMX1BSRUZJWD1haV9zdXBwb3J0X1xuICAgICA5XHRcbiAgICAxMFx0IyBBSSBDb25maWd1cmF0aW9uXG4gICAgMTFcdE9QRU5BSV9BUElfS0VZPWstcHJvai16eDNUV1hSLUxLY01QVG0yTHdxMHcyZ29wdXpWT2Y0dHVpZ3hRSDVpcGlPeVJDSzl6Rk9PZ0VNQmptcWYwQTdRVmRDMUhHNzFrTVQzQmxia0ZKMkxjZ2lCQXVwbDVVOWJCNXJFVHlQTVJsRVZfMUdaLWtac1dzMUpjVHBCWVMtbGhrYUVERE1BdkVxZm50WVFBVWRMS2VuUDM2VUFcbiAgICAxMlx0T1BFTkFJX01PREVMPWdwdC00XG4gICAgMTNcdFNUVF9QUk9WSURFUj1vcGVuYWlcbiAgICAxNFx0VFRTX1BST1ZJREVSPW9wZW5haVxuICAgIDE1XHRUVFNfVk9JQ0VfSUQ9YWxsb3lcbiAgICAxNlx0RU5BQkxFX1ZBRD10cnVlXG4gICAgMTdcdFZBRF9TRU5TSVRJVklUWT0wLjdcbi4uLlxuUGF0aDogbWFpbi5nb1xuLi4uXG4gICAgNjVcdFxuICAgIDY2XHRcdC8vIEFQSSByb3V0ZXNcbiAgICA2N1x0XHRhcGkgOj0gcm91dGVyLkdyb3VwKFwiL2FwaVwiKVxuICAgIDY4XHRcdHtcbiAgICA2OVx0XHRcdC8vIENhbGwgbWFuYWdlbWVudCByb3V0ZXNcbiAgICA3MFx0XHRcdGNhbGxzIDo9IGFwaS5Hcm91cChcIi9jYWxsc1wiKVxuICAgIDcxXHRcdFx0e1xuICAgIDcyXHRcdFx0XHRjYWxscy5QT1NUKFwiL2luaXRpYXRlXCIsIGNhbGxIYW5kbGVyLkluaXRpYXRlQ2FsbCkgICAgICAgICAgICAgICAgICAgIC8vIEFJIGNhbGxzIGN1c3RvbWVyXG4gICAgNzNcdFx0XHRcdGNhbGxzLlBPU1QoXCIvY3VzdG9tZXItaW5pdGlhdGVcIiwgY2FsbEhhbmRsZXIuSW5pdGlhdGVDdXN0b21lckNhbGwpICAgLy8gQ3VzdG9tZXIgY2FsbHMgQUlcbiAgICA3NFx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC9qb2luXCIsIGNhbGxIYW5kbGVyLkpvaW5DYWxsKVxuICAgIDc1XHRcdFx0XHRjYWxscy5HRVQoXCIvOmNhbGxfaWRcIiwgY2FsbEhhbmRsZXIuR2V0Q2FsbClcbiAgICA3Nlx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC9lbmRcIiwgY2FsbEhhbmRsZXIuRW5kQ2FsbClcbiAgICA3N1x0XHRcdFx0Y2FsbHMuR0VUKFwiL2FjdGl2ZVwiLCBjYWxsSGFuZGxlci5HZXRBY3RpdmVDYWxscylcbiAgICA3OFx0XHRcdFx0Y2FsbHMuR0VUKFwiLzpjYWxsX2lkL3N0YXRzXCIsIGNhbGxIYW5kbGVyLkdldENhbGxTdGF0cylcbiAgICA3OVx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC90b2tlblwiLCBjYWxsSGFuZGxlci5HZW5lcmF0ZVRva2VuKVxuICAgIDgwXHRcdFx0fVxuICAgIDgxXHRcdH1cbiAgICA4Mlx0XG4gICAgODNcdFx0Ly8gV2VsY29tZSByb3V0ZVxuICAgIDg0XHRcdHJvdXRlci5HRVQoXCIvXCIsIGZ1bmMoYyAqZ2luLkNvbnRleHQpIHtcbiAgICA4NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBnaW4uSHtcbiAgICA4Nlx0XHRcdFx0XCJtZXNzYWdlXCI6IFwi8J+kliBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIixcbiAgICA4N1x0XHRcdFx0XCJ2ZXJzaW9uXCI6IFwiMS4wLjBcIixcbiAgICA4OFx0XHRcdFx0XCJzdGF0dXNcIjogIFwicnVubmluZ1wiLFxuICAgIDg5XHRcdFx0XHRcImVuZHBvaW50c1wiOiBnaW4uSHtcbiAgICA5MFx0XHRcdFx0XHRcImhlYWx0aFwiOiAgICAgICAgICAgICAgICBcIkdFVCAvaGVhbHRoXCIsXG4gICAgOTFcdFx0XHRcdFx0XCJpbml0aWF0ZV9jYWxsXCI6ICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvaW5pdGlhdGVcIixcbiAgICA5Mlx0XHRcdFx0XHRcImN1c3RvbWVyX2luaXRpYXRlXCI6ICAgICBcIlBPU1QgL2FwaS9jYWxscy9jdXN0b21lci1pbml0aWF0ZVwiLFxuICAgIDkzXHRcdFx0XHRcdFwiam9pbl9jYWxsXCI6ICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2pvaW5cIixcbiAgICA5NFx0XHRcdFx0XHRcImdldF9jYWxsXCI6ICAgICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkXCIsXG4gICAgOTVcdFx0XHRcdFx0XCJlbmRfY2FsbFwiOiAgICAgICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvZW5kXCIsXG4gICAgOTZcdFx0XHRcdFx0XCJhY3RpdmVfY2FsbHNcIjogICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy9hY3RpdmVcIixcbiAgICA5N1x0XHRcdFx0XHRcImNhbGxfc3RhdHNcIjogICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3N0YXRzXCIsXG4gICAgOThcdFx0XHRcdFx0XCJnZW5lcmF0ZV90b2tlblwiOiAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvdG9rZW5cIixcbiAgICA5OVx0XHRcdFx0fSxcbiAgIDEwMFx0XHRcdH0pXG4gICAxMDFcdFx0fSlcbiAgIDEwMlx0XG4gICAxMDNcdFx0bG9nLlByaW50ZihcIvCfm6PvuI8gUm91dGVzIGNvbmZpZ3VyZWQgc3VjY2Vzc2Z1bGx5XCIpXG4gICAxMDRcdFx0cmV0dXJuIHJvdXRlclxuICAgMTA1XHR9XG4uLi5cblBhdGg6IGNvbmZpZy9jb25maWcuZ29cbiAgICAgMVx0cGFja2FnZSBjb25maWdcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJsb2dcIlxuICAgICA1XHRcdFwib3NcIlxuICAgICA2XHRcdFwic3RyY29udlwiXG4gICAgIDdcdFxuICAgICA4XHRcdFwiZ2l0aHViLmNvbS9qb2hvL2dvZG90ZW52XCJcbiAgICAgOVx0KVxuICAgIDEwXHRcbiAgICAxMVx0dHlwZSBDb25maWcgc3RydWN0IHtcbiAgICAxMlx0XHRTZXJ2ZXIgU2VydmVyQ29uZmlnXG4gICAgMTNcdFx0QWdvcmEgIEFnb3JhQ29uZmlnXG4gICAgMTRcdFx0QUkgICAgIEFJQ29uZmlnXG4gICAgMTVcdH1cbiAgICAxNlx0XG4gICAgMTdcdHR5cGUgU2VydmVyQ29uZmlnIHN0cnVjdCB7XG4gICAgMThcdFx0UG9ydCBzdHJpbmdcbiAgICAxOVx0XHRIb3N0IHN0cmluZ1xuICAgIDIwXHR9XG4gICAgMjFcdFxuICAgIDIyXHR0eXBlIEFnb3JhQ29uZmlnIHN0cnVjdCB7XG4gICAgMjNcdFx0QXBwSUQgICAgICAgICAgc3RyaW5nXG4gICAgMjRcdFx0QXBwQ2VydGlmaWNhdGUgc3RyaW5nXG4gICAgMjVcdFx0Q2hhbm5lbFByZWZpeCAgc3RyaW5nXG4gICAgMjZcdH1cbiAgICAyN1x0XG4gICAgMjhcdHR5cGUgQUlDb25maWcgc3RydWN0IHtcbiAgICAyOVx0XHRPcGVuQUlBUElLZXkgICAgc3RyaW5nXG4gICAgMzBcdFx0T3BlbkFJTW9kZWwgICAgIHN0cmluZ1xuICAgIDMxXHRcdFNUVFByb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzMlx0XHRUVFNQcm92aWRlciAgICAgc3RyaW5nXG4gICAgMzNcdFx0Vm9pY2VJRCAgICAgICAgIHN0cmluZ1xuICAgIDM0XHRcdEVuYWJsZVZBRCAgICAgICBib29sXG4gICAgMzVcdFx0VkFEU2Vuc2l0aXZpdHkgIGZsb2F0NjRcbiAgICAzNlx0fVxuICAgIDM3XHRcbiAgICAzOFx0dmFyIEFwcENvbmZpZyAqQ29uZmlnXG4gICAgMzlcdFxuICAgIDQwXHRmdW5jIExvYWRDb25maWcoKSB7XG4gICAgNDFcdFx0Ly8gTG9hZCAuZW52IGZpbGUgaWYgaXQgZXhpc3RzXG4gICAgNDJcdFx0aWYgZXJyIDo9IGdvZG90ZW52LkxvYWQoKTsgZXJyICE9IG5pbCB7XG4gICAgNDNcdFx0XHRsb2cuUHJpbnRsbihcIk5vIC5lbnYgZmlsZSBmb3VuZCwgdXNpbmcgZW52aXJvbm1lbnQgdmFyaWFibGVzXCIpXG4gICAgNDRcdFx0fVxuICAgIDQ1XHRcbiAgICA0Nlx0XHRBcHBDb25maWcgPSAmQ29uZmlne1xuICAgIDQ3XHRcdFx0U2VydmVyOiBTZXJ2ZXJDb25maWd7XG4gICAgNDhcdFx0XHRcdFBvcnQ6IGdldEVudihcIlNFUlZFUl9QT1JUXCIsIFwiODA4MFwiKSxcbiAgICA0OVx0XHRcdFx0SG9zdDogZ2V0RW52KFwiU0VSVkVSX0hPU1RcIiwgXCIwLjAuMC4wXCIpLFxuICAgIDUwXHRcdFx0fSxcbiAgICA1MVx0XHRcdEFnb3JhOiBBZ29yYUNvbmZpZ3tcbiAgICA1Mlx0XHRcdFx0QXBwSUQ6ICAgICAgICAgIGdldEVudihcIkFHT1JBX0FQUF9JRFwiLCBcIlwiKSxcbiAgICA1M1x0XHRcdFx0QXBwQ2VydGlmaWNhdGU6IGdldEVudihcIkFHT1JBX0FQUF9DRVJUSUZJQ0FURVwiLCBcIlwiKSxcbiAgICA1NFx0XHRcdFx0Q2hhbm5lbFByZWZpeDogIGdldEVudihcIkFHT1JBX0NIQU5ORUxfUFJFRklYXCIsIFwiYWlfc3VwcG9ydF9cIiksXG4gICAgNTVcdFx0XHR9LFxuICAgIDU2XHRcdFx0QUk6IEFJQ29uZmlne1xuICAgIDU3XHRcdFx0XHRPcGVuQUlBUElLZXk6ICAgIGdldEVudihcIk9QRU5BSV9BUElfS0VZXCIsIFwiXCIpLFxuICAgIDU4XHRcdFx0XHRPcGVuQUlNb2RlbDogICAgIGdldEVudihcIk9QRU5BSV9NT0RFTFwiLCBcImdwdC00XCIpLFxuICAgIDU5XHRcdFx0XHRTVFRQcm92aWRlcjogICAgIGdldEVudihcIlNUVF9QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MFx0XHRcdFx0VFRTUHJvdmlkZXI6ICAgICBnZXRFbnYoXCJUVFNfUFJPVklERVJcIiwgXCJvcGVuYWlcIiksXG4gICAgNjFcdFx0XHRcdFZvaWNlSUQ6ICAgICAgICAgZ2V0RW52KFwiVFRTX1ZPSUNFX0lEXCIsIFwiYWxsb3lcIiksXG4gICAgNjJcdFx0XHRcdEVuYWJsZVZBRDogICAgICAgZ2V0RW52Qm9vbChcIkVOQUJMRV9WQURcIiwgdHJ1ZSksXG4gICAgNjNcdFx0XHRcdFZBRFNlbnNpdGl2aXR5OiAgZ2V0RW52RmxvYXQoXCJWQURfU0VOU0lUSVZJVFlcIiwgMC43KSxcbiAgICA2NFx0XHRcdH0sXG4gICAgNjVcdFx0fVxuLi4uXG5QYXRoOiBzZXJ2aWNlcy9haV9zZXJ2aWNlLmdvXG4uLi5cbiAgIDE0N1x0XHRcbiAgIDE0OFx0XHQvLyBBZGQgY3VycmVudCB1c2VyIG1lc3NhZ2VcbiAgIDE0OVx0XHRtZXNzYWdlcyA9IGFwcGVuZChtZXNzYWdlcywgTExNTWVzc2FnZXtcbiAgIDE1MFx0XHRcdFJvbGU6ICAgIFwidXNlclwiLFxuICAgMTUxXHRcdFx0Q29udGVudDogdXNlck1lc3NhZ2UsXG4gICAxNTJcdFx0fSlcbiAgIDE1M1x0XHRcbiAgIDE1NFx0XHRyZXR1cm4gbWVzc2FnZXNcbiAgIDE1NVx0fVxuICAgMTU2XHRcbiAgIDE1N1x0Ly8gY2FsbE9wZW5BSUFQSSBtYWtlcyBhY3R1YWwgQVBJIGNhbGwgdG8gT3BlbkFJXG4gICAxNThcdGZ1bmMgKGFpICpBSVNlcnZpY2UpIGNhbGxPcGVuQUlBUEkobWVzc2FnZXMgW11MTE1NZXNzYWdlKSAoc3RyaW5nLCBlcnJvcikge1xuICAgMTU5XHRcdHJlcXVlc3QgOj0gTExNUmVxdWVzdHtcbiAgIDE2MFx0XHRcdE1vZGVsOiAgICAgICBhaS5jb25maWcuQUkuT3BlbkFJTW9kZWwsXG4gICAxNjFcdFx0XHRNZXNzYWdlczogICAgbWVzc2FnZXMsXG4gICAxNjJcdFx0XHRUZW1wZXJhdHVyZTogMC43LFxuICAgMTYzXHRcdFx0TWF4VG9rZW5zOiAgIDE1MCxcbiAgIDE2NFx0XHR9XG4gICAxNjVcdFx0XG4gICAxNjZcdFx0anNvbkRhdGEsIGVyciA6PSBqc29uLk1hcnNoYWwocmVxdWVzdClcbiAgIDE2N1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE2OFx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIG1hcnNoYWwgcmVxdWVzdDogJXdcIiwgZXJyKVxuICAgMTY5XHRcdH1cbiAgIDE3MFx0XHRcbiAgIDE3MVx0XHRyZXEsIGVyciA6PSBodHRwLk5ld1JlcXVlc3QoXCJQT1NUXCIsIFwiaHR0cHM6Ly9hcGkub3BlbmFpLmNvbS92MS9jaGF0L2NvbXBsZXRpb25zXCIsIGJ5dGVzLk5ld0J1ZmZlcihqc29uRGF0YSkpXG4gICAxNzJcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxNzNcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byBjcmVhdGUgcmVxdWVzdDogJXdcIiwgZXJyKVxuICAgMTc0XHRcdH1cbiAgIDE3NVx0XHRcbiAgIDE3Nlx0XHRyZXEuSGVhZGVyLlNldChcIkNvbnRlbnQtVHlwZVwiLCBcImFwcGxpY2F0aW9uL2pzb25cIilcbiAgIDE3N1x0XHRyZXEuSGVhZGVyLlNldChcIkF1dGhvcml6YXRpb25cIiwgXCJCZWFyZXIgXCIrYWkuY29uZmlnLkFJLk9wZW5BSUFQSUtleSlcbiAgIDE3OFx0XHRcbiAgIDE3OVx0XHRyZXNwLCBlcnIgOj0gYWkuY2xpZW50LkRvKHJlcSlcbiAgIDE4MFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE4MVx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIG1ha2UgcmVxdWVzdDogJXdcIiwgZXJyKVxuICAgMTgyXHRcdH1cbiAgIDE4M1x0XHRkZWZlciByZXNwLkJvZHkuQ2xvc2UoKVxuICAgMTg0XHRcdFxuICAgMTg1XHRcdGJvZHksIGVyciA6PSBpby5SZWFkQWxsKHJlc3AuQm9keSlcbiAgIDE4Nlx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE4N1x0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIHJlYWQgcmVzcG9uc2U6ICV3XCIsIGVycilcbiAgIDE4OFx0XHR9XG4gICAxODlcdFx0XG4gICAxOTBcdFx0dmFyIGxsbVJlc3BvbnNlIExMTVJlc3BvbnNlXG4gICAxOTFcdFx0aWYgZXJyIDo9IGpzb24uVW5tYXJzaGFsKGJvZHksICZsbG1SZXNwb25zZSk7IGVyciAhPSBuaWwge1xuICAgMTkyXHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gdW5tYXJzaGFsIHJlc3BvbnNlOiAld1wiLCBlcnIpXG4gICAxOTNcdFx0fVxuICAgMTk0XHRcdFxuICAgMTk1XHRcdGlmIGxlbihsbG1SZXNwb25zZS5DaG9pY2VzKSA9PSAwIHtcbiAgIDE5Nlx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwibm8gcmVzcG9uc2UgZnJvbSBMTE1cIilcbiAgIDE5N1x0XHR9XG4gICAxOThcdFx0XG4gICAxOTlcdFx0cmV0dXJuIGxsbVJlc3BvbnNlLkNob2ljZXNbMF0uTWVzc2FnZS5Db250ZW50LCBuaWxcbiAgIDIwMFx0fVxuLi4uXG5QYXRoOiBSRUFETUUubWRcbi4uLlxuICAgIDMwXHTilIIg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYIOKUgiAgICDilIIg4pSM4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSQIOKUgiAgICDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJhcbiAgICAzMVx04pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYICAgIOKUgiDilIIgTm90aWZpY2F0aW9u4pSCIOKUgiAgICDilIzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJBcbiAgICAzMlx0ICAgICAgICAgICAgICAgICAgICAgICDilIIg4pSCIFNlcnZpY2UgICAgIOKUgiDilILil4TilIDilIDilrrilIIgUHVzaC9TTVMvRW1haWwgIOKUglxuICAgIDMzXHQgICAgICAgICAgICAgICAgICAgICAgIOKUgiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJgg4pSCICAgIOKUgiBTZXJ2aWNlcyAgICAgICAg4pSCXG4gICAgMzRcdCAgICAgICAgICAgICAgICAgICAgICAg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYICAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmFxuICAgIDM1XHRgYGBcbiAgICAzNlx0XG4gICAgMzdcdCMjIPCfk4sgUHJlcmVxdWlzaXRlc1xuICAgIDM4XHRcbiAgICAzOVx0LSBHbyAxLjIyIG9yIGhpZ2hlclxuICAgIDQwXHQtIEFnb3JhLmlvIGFjY291bnQgYW5kIEFwcCBJRFxuICAgIDQxXHQtIE9wZW5BSSBBUEkga2V5IChvcHRpb25hbCwgdXNlcyBtb2NrIGJ5IGRlZmF1bHQpXG4gICAgNDJcdFxuICAgIDQzXHQjIyDwn5ug77iPIFNldHVwXG4gICAgNDRcdFxuICAgIDQ1XHQxLiAqKkNsb25lIGFuZCBzZXR1cCoqOlxuICAgIDQ2XHQgICBgYGBiYXNoXG4gICAgNDdcdCAgIGdpdCBjbG9uZSA8cmVwb3NpdG9yeT5cbiAgICA0OFx0ICAgY2QgQmFja2VuZFxuICAgIDQ5XHQgICBgYGBcbiAgICA1MFx0XG4gICAgNTFcdDIuICoqSW5zdGFsbCBkZXBlbmRlbmNpZXMqKjpcbiAgICA1Mlx0ICAgYGBgYmFzaFxuICAgIDUzXHQgICBnbyBtb2QgdGlkeVxuICAgIDU0XHQgICBgYGBcbiAgICA1NVx0XG4gICAgNTZcdDMuICoqQ29uZmlndXJlIGVudmlyb25tZW50Kio6XG4gICAgNTdcdCAgIGBgYGJhc2hcbiAgICA1OFx0ICAgY3AgLmVudiAuZW52XG4gICAgNTlcdCAgICMgRWRpdCAuZW52IHdpdGggeW91ciBjb25maWd1cmF0aW9uXG4gICAgNjBcdCAgIGBgYFxuLi4uXG4gICAgODRcdFxuICAgIDg1XHR8IE1ldGhvZCB8IEVuZHBvaW50IHwgRGVzY3JpcHRpb24gfFxuICAgIDg2XHR8LS0tLS0tLS18LS0tLS0tLS0tLXwtLS0tLS0tLS0tLS0tfFxuICAgIDg3XHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzL2luaXRpYXRlYCB8IPCfpJbinqHvuI/wn5GkIEluaXRpYXRlIEFJIGNhbGwgdG8gY3VzdG9tZXIgfFxuICAgIDg4XHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzL2N1c3RvbWVyLWluaXRpYXRlYCB8IPCfkaTinqHvuI/wn6SWIEN1c3RvbWVyIGluaXRpYXRlcyBjYWxsIHRvIEFJIHxcbiAgICA4OVx0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC9qb2luYCB8IEN1c3RvbWVyIGpvaW5zIGFuIGV4aXN0aW5nIGNhbGwgfFxuICAgIDkwXHR8IGBHRVRgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWRgIHwgR2V0IGNhbGwgZGV0YWlscyB8XG4gICAgOTFcdHwgYFBPU1RgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWQvZW5kYCB8IEVuZCBhIGNhbGwgfFxuICAgIDkyXHR8IGBHRVRgIHwgYC9hcGkvY2FsbHMvYWN0aXZlYCB8IExpc3QgYWxsIGFjdGl2ZSBjYWxscyB8XG4gICAgOTNcdHwgYEdFVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC9zdGF0c2AgfCBHZXQgY2FsbCBzdGF0aXN0aWNzIHxcbiAgICA5NFx0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC90b2tlbmAgfCBHZW5lcmF0ZSBBZ29yYSB0b2tlbiB8XG4gICAgOTVcdFxuICAgIDk2XHQjIyDwn5OWIFVzYWdlIEV4YW1wbGVzXG4gICAgOTdcdFxuICAgIDk4XHQjIyMgMS4g8J+kluKeoe+4j/CfkaQgSW5pdGlhdGUgQUktdG8tQ3VzdG9tZXIgQ2FsbFxuLi4uXG4gICAyMzRcdFxuICAgMjM1XHQjIyDwn5SnIENvbmZpZ3VyYXRpb25cbiAgIDIzNlx0XG4gICAyMzdcdCMjIyBTZXJ2ZXIgU2V0dGluZ3NcbiAgIDIzOFx0YGBgZW52XG4gICAyMzlcdFNFUlZFUl9QT1JUPTgwODBcbiAgIDI0MFx0U0VSVkVSX0hPU1Q9MC4wLjAuMFxuICAgMjQxXHRgYGBcbiAgIDI0Mlx0XG4gICAyNDNcdCMjIyBBZ29yYSBTZXR0aW5nc1xuICAgMjQ0XHRgYGBlbnZcbiAgIDI0NVx0QUdPUkFfQVBQX0lEPXlvdXJfYXBwX2lkXG4gICAyNDZcdEFHT1JBX0FQUF9DRVJUSUZJQ0FURT15b3VyX2NlcnRpZmljYXRlXG4gICAyNDdcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAyNDhcdGBgYFxuICAgMjQ5XHRcbiAgIDI1MFx0IyMjIEFJIFNldHRpbmdzXG4gICAyNTFcdGBgYGVudlxuICAgMjUyXHRPUEVOQUlfQVBJX0tFWT15b3VyX2tleVxuICAgMjUzXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgIDI1NFx0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgMjU1XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAyNTZcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgMjU3XHRFTkFCTEVfVkFEPXRydWVcbiAgIDI1OFx0VkFEX1NFTlNJVElWSVRZPTAuN1xuICAgMjU5XHRgYGBcbiAgIDI2MFx0XG4gICAyNjFcdCMjIPCfp6ogRGV2ZWxvcG1lbnRcbiAgIDI2Mlx0XG4gICAyNjNcdCMjIyBNb2NrIFNlcnZpY2VzXG4gICAyNjRcdFxuICAgMjY1XHRUaGUgYXBwbGljYXRpb24gaW5jbHVkZXMgbW9jayBpbXBsZW1lbnRhdGlvbnMgZm9yOlxuICAgMjY2XHQtICoqTm90aWZpY2F0aW9uIFNlcnZpY2UqKjogU2ltdWxhdGVzIHB1c2gvU01TL2VtYWlsIG5vdGlmaWNhdGlvbnNcbiAgIDI2N1x0LSAqKkFJIFNlcnZpY2UqKjogTW9jayBTVFQsIExMTSwgYW5kIFRUUyByZXNwb25zZXNcbiAgIDI2OFx0LSAqKkFnb3JhIFNlcnZpY2UqKjogTW9jayB0b2tlbiBnZW5lcmF0aW9uIGFuZCBjaGFubmVsIG1hbmFnZW1lbnRcbiAgIDI2OVx0XG4gICAyNzBcdCMjIyBUZXN0aW5nXG4gICAyNzFcdFxuICAgMjcyXHRgYGBiYXNoXG4gICAyNzNcdCMgVGVzdCBoZWFsdGggZW5kcG9pbnRcbiAgIDI3NFx0Y3VybCBodHRwOi8vbG9jYWxob3N0OjgwODAvaGVhbHRoXG4uLi5cblBhdGg6IHRlc3RfYXBpLnNoXG4gICAgIDFcdCMhL2Jpbi9iYXNoXG4gICAgIDJcdFxuICAgICAzXHQjIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZCBBUEkgVGVzdCBTY3JpcHRcbiAgICAgNFx0IyBUaGlzIHNjcmlwdCBkZW1vbnN0cmF0ZXMgYm90aCBjYWxsIGZsb3dzOiBBSS10by1DdXN0b21lciBhbmQgQ3VzdG9tZXItdG8tQUlcbiAgICAgNVx0XG4gICAgIDZcdEJBU0VfVVJMPVwiaHR0cDovL2xvY2FsaG9zdDo4MDgwXCJcbiAgICAgN1x0XG4gICAgIDhcdGVjaG8gXCLwn6SWIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZCAtIEFQSSBUZXN0XCJcbiAgICAgOVx0ZWNobyBcIj09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVwiXG4gICAgMTBcdGVjaG8gXCJUZXN0aW5nIGJvdGggY2FsbCBmbG93czpcIlxuICAgIDExXHRlY2hvIFwiMS4gQUktdG8tQ3VzdG9tZXIgKEFJIGluaXRpYXRlcyBjYWxsKVwiXG4gICAgMTJcdGVjaG8gXCIyLiBDdXN0b21lci10by1BSSAoQ3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwpXCJcbiAgICAxM1x0ZWNobyBcIlwiXG4gICAgMTRcdFxuICAgIDE1XHQjIFRlc3QgMTogSGVhbHRoIENoZWNrXG4gICAgMTZcdGVjaG8gXCIxLiBUZXN0aW5nIGhlYWx0aCBlbmRwb2ludC4uLlwiXG4gICAgMTdcdGN1cmwgLXMgXCIkQkFTRV9VUkwvaGVhbHRoXCIgfCBqcSAnLidcbiAgICAxOFx0XG4gICAgMTlcdCMgVGVzdCAyOiAuLi4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIyNjlmOTFlZS01YTkzLTRmYjAtYjM1ZC01YjVmY2E5ZmM1ODk7dG9vbHVfdnJ0eF8wMUVnNlB6VEZEZjJrUUc1aVZwcTkybnAiOnsicmVxdWVzdElkIjoiMjY5ZjkxZWUtNWE5My00ZmIwLWIzNWQtNWI1ZmNhOWZjNTg5IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUVnNlB6VEZEZjJrUUc1aVZwcTkybnAiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJTdWNjZXNzZnVsbHkgZWRpdGVkIHRoZSBmaWxlIHNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ28uXG5SZXN1bHQgZm9yIHN0cl9yZXBsYWNlIGZvciBlbnRyeSB3aXRoIGluZGV4IFsxXTpcblJlcGxhY2VtZW50IHN1Y2Nlc3NmdWwuXG5FZGl0ZWQgc2VjdGlvbiBhZnRlciBJREUgYXV0by1mb3JtYXR0aW5nIHdhcyBhcHBsaWVkOlxuICAgIDE5XHRcdC8vIC0gQWN0aXZlIGNvbm5lY3Rpb25zIG1hcFxuICAgIDIwXHRcdC8vIC0gQXVkaW8vVmlkZW8gb2JzZXJ2ZXJzXG4gICAgMjFcdH1cbiAgICAyMlx0XG4gICAgMjNcdC8vIFRva2VuUmVxdWVzdCByZXByZXNlbnRzIGEgcmVxdWVzdCBmb3IgQWdvcmEgdG9rZW5cbiAgICAyNFx0dHlwZSBUb2tlblJlcXVlc3Qgc3RydWN0IHtcbiAgICAyNVx0XHRDaGFubmVsTmFtZSBzdHJpbmcgYGpzb246XCJjaGFubmVsX25hbWVcImBcbiAgICAyNlx0XHRVc2VySUQgICAgICBpbnQgICAgYGpzb246XCJ1c2VyX2lkXCJgXG4gICAgMjdcdFx0Um9sZSAgICAgICAgaW50ICAgIGBqc29uOlwicm9sZVwiYCAvLyAxID0gcHVibGlzaGVyLCAyID0gc3Vic2NyaWJlclxuICAgIDI4XHR9XG4gICAgMjlcdFxuICAgIDMwXHQvLyBUb2tlblJlc3BvbnNlIHJlcHJlc2VudHMgQWdvcmEgdG9rZW4gcmVzcG9uc2VcbiAgICAzMVx0dHlwZSBUb2tlblJlc3BvbnNlIHN0cnVjdCB7XG4gICAgMzJcdFx0VG9rZW4gICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG5cblJldmlldyB0aGUgY2hhbmdlcyBhbmQgbWFrZSBzdXJlIHRoZXkgYXJlIGFzIGV4cGVjdGVkIChjb3JyZWN0IGluZGVudGF0aW9uLCBubyBkdXBsaWNhdGUgbGluZXMsIGV0YykuXG5FZGl0IHRoZSBmaWxlIGFnYWluIGlmIG5lY2Vzc2FyeS5cbiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX19LCJkcmFmdEV4Y2hhbmdlIjp7InJlcXVlc3RfbWVzc2FnZSI6IiIsInJpY2hfdGV4dF9qc29uX3JlcHIiOnsidHlwZSI6ImRvYyIsImNvbnRlbnQiOlt7InR5cGUiOiJwYXJhZ3JhcGgifV19LCJzdGF0dXMiOiJkcmFmdCJ9LCJkcmFmdEFjdGl2ZUNvbnRleHRJZHMiOlsiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZS9CYWNrZW5kL3NlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ28iLCIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmRmYWxzZWZhbHNlIiwidXNlckd1aWRlbGluZXMiLCJhZ2VudE1lbW9yaWVzIl0sInJlcXVlc3RJZHMiOltdLCJpc1Bpbm5lZCI6ZmFsc2UsImlzU2hhcmVhYmxlIjp0cnVlLCJleHRyYURhdGEiOnsiaXNBZ2VudENvbnZlcnNhdGlvbiI6dHJ1ZSwiaGFzRGlydHlFZGl0cyI6dHJ1ZX0sInBlcnNvbmFUeXBlIjowLCJyb290VGFza1V1aWQiOiJhNjg4YTQzMy1lOWU1LTRlNmQtOGFiZi0xZjk4MTk0MTE4N2MifSwiNjVlNjM4ZmUtMjM1OS00NDJjLTk5NGEtM2I2YTRmODljNzMzIjp7ImlkIjoiNjVlNjM4ZmUtMjM1OS00NDJjLTk5NGEtM2I2YTRmODljNzMzIiwiY3JlYXRlZEF0SXNvIjoiMjAyNS0wNS0yNlQwODowNjo1Mi45NjNaIiwibGFzdEludGVyYWN0ZWRBdElzbyI6IjIwMjUtMDUtMjZUMDg6MTM6MDguMzkyWiIsImNoYXRIaXN0b3J5IjpbeyJyZXF1ZXN0X2lkIjoiNWI5MzZjNGYtZWMzOC00YTFmLWJhMDUtZWU0OTEzZTRjZjcxIiwidXVpZCI6ImMyZDE5MjU0LTIxMDctNDUwYS1iMWFmLTIyZDk0MzVkNGNiNSIsImNoYXRJdGVtVHlwZSI6ImFnZW50aWMtY2hlY2twb2ludC1kZWxpbWl0ZXIiLCJzdGF0dXMiOiJzdWNjZXNzIiwiZnJvbVRpbWVzdGFtcCI6MCwidG9UaW1lc3RhbXAiOjE3NDgyNDY4MTI5NjQsInNlZW5fc3RhdGUiOiJzZWVuIn0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjI4NzM0MTJkLWEwMDItNDFiYS05YjNiLWNhYTkxMTI4ODRkMiIsInJlcXVlc3RfbWVzc2FnZSI6InJlZmFjdG9yIHRoZSBiYWNrZW5kLCByZW1vdmUgdGhlIEFJIGZlYXR1cmUsIG5vdyBvbmx5IGtlZXAgdGhlIGZlYXR1cmUgYWxsb3cgb25lIHVzZXIgY2FsbCBhbm90aGVyIHVzZXIuIChzZW5kIHRoZSBub3RpZmljYXRpb24gdG8gdGhlIHVzZXIgcmVjZWl2ZWQgdGhlIGNhbGwgLSBjYW4gbW9jayBmb3Igbm93KSIsInJpY2hfdGV4dF9qc29uX3JlcHIiOnsidHlwZSI6ImRvYyIsImNvbnRlbnQiOlt7InR5cGUiOiJwYXJhZ3JhcGgiLCJjb250ZW50IjpbeyJ0eXBlIjoidGV4dCIsInRleHQiOiJyZWZhY3RvciB0aGUgYmFja2VuZCwgcmVtb3ZlIHRoZSBBSSBmZWF0dXJlLCBub3cgb25seSBrZWVwIHRoZSBmZWF0dXJlIGFsbG93IG9uZSB1c2VyIGNhbGwgYW5vdGhlciB1c2VyLiAoc2VuZCB0aGUgbm90aWZpY2F0aW9uIHRvIHRoZSB1c2VyIHJlY2VpdmVkIHRoZSBjYWxsIC0gY2FuIG1vY2sgZm9yIG5vdykifV19XX0sIm1lbnRpb25lZF9pdGVtcyI6W10sInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVdUdnN0ZlFUVXhVY2NyYXp0Vlk3SlQiLCJ0b29sX25hbWUiOiJjb2RlYmFzZS1yZXRyaWV2YWwiLCJpbnB1dF9qc29uIjoie1wiaW5mb3JtYXRpb25fcmVxdWVzdFwiOiBcIlNob3cgbWUgdGhlIG92ZXJhbGwgc3RydWN0dXJlIG9mIHRoZSBiYWNrZW5kIGNvZGViYXNlLCBpbmNsdWRpbmcgYWxsIEdvIGZpbGVzLCBzZXJ2aWNlcywgaGFuZGxlcnMsIGFuZCBhbnkgQUktcmVsYXRlZCBmdW5jdGlvbmFsaXR5IHRoYXQgbmVlZHMgdG8gYmUgcmVtb3ZlZC4gSSBuZWVkIHRvIHVuZGVyc3RhbmQgdGhlIGN1cnJlbnQgYXJjaGl0ZWN0dXJlIGZvciB1c2VyIGNhbGxpbmcgZmVhdHVyZXMuXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiSSdsbCBoZWxwIHlvdSByZWZhY3RvciB0aGUgYmFja2VuZCB0byByZW1vdmUgdGhlIEFJIGZlYXR1cmUgYW5kIGtlZXAgb25seSB0aGUgdXNlci10by11c2VyIGNhbGxpbmcgZnVuY3Rpb25hbGl0eS4gTGV0IG1lIGZpcnN0IGdhdGhlciBpbmZvcm1hdGlvbiBhYm91dCB0aGUgY3VycmVudCBjb2RlYmFzZSBzdHJ1Y3R1cmUgdG8gdW5kZXJzdGFuZCB3aGF0IG5lZWRzIHRvIGJlIG1vZGlmaWVkLlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MCwidHlwZSI6MCwidGV4dF9ub2RlIjp7ImNvbnRlbnQiOiJyZWZhY3RvciB0aGUgYmFja2VuZCwgcmVtb3ZlIHRoZSBBSSBmZWF0dXJlLCBub3cgb25seSBrZWVwIHRoZSBmZWF0dXJlIGFsbG93IG9uZSB1c2VyIGNhbGwgYW5vdGhlciB1c2VyLiAoc2VuZCB0aGUgbm90aWZpY2F0aW9uIHRvIHRoZSB1c2VyIHJlY2VpdmVkIHRoZSBjYWxsIC0gY2FuIG1vY2sgZm9yIG5vdykifX0seyJpZCI6MSwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIiwiZm9sZGVyUm9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlfX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA4OjA2OjU0LjIzMloiLCJyZXNwb25zZV90ZXh0IjoiSSdsbCBoZWxwIHlvdSByZWZhY3RvciB0aGUgYmFja2VuZCB0byByZW1vdmUgdGhlIEFJIGZlYXR1cmUgYW5kIGtlZXAgb25seSB0aGUgdXNlci10by11c2VyIGNhbGxpbmcgZnVuY3Rpb25hbGl0eS4gTGV0IG1lIGZpcnN0IGdhdGhlciBpbmZvcm1hdGlvbiBhYm91dCB0aGUgY3VycmVudCBjb2RlYmFzZSBzdHJ1Y3R1cmUgdG8gdW5kZXJzdGFuZCB3aGF0IG5lZWRzIHRvIGJlIG1vZGlmaWVkLlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjQxOTM5ZDljLTljYTQtNDYwNy1iNmMzLTg2OGNlNmFiOGJjMSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVlQTERIamRmcFNpbm5IM1ZadmFtWHAiLCJ0b29sX25hbWUiOiJjb2RlYmFzZS1yZXRyaWV2YWwiLCJpbnB1dF9qc29uIjoie1wiaW5mb3JtYXRpb25fcmVxdWVzdFwiOiBcIlNob3cgbWUgdGhlIG5vdGlmaWNhdGlvbiBzZXJ2aWNlIGltcGxlbWVudGF0aW9uLCBhbnkgQUktcmVsYXRlZCBtb2RlbHMsIHJlcXVlc3QvcmVzcG9uc2Ugc3RydWN0dXJlcywgYW5kIGNvbmZpZ3VyYXRpb24gZmlsZXMgdGhhdCBjb250YWluIEFJIHNldHRpbmdzLiBJIG5lZWQgdG8gdW5kZXJzdGFuZCB3aGF0IGNvbXBvbmVudHMgYXJlIHNwZWNpZmljYWxseSByZWxhdGVkIHRvIEFJIGZ1bmN0aW9uYWxpdHkuXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiTm93IGxldCBtZSBsb29rIGF0IHRoZSBub3RpZmljYXRpb24gc2VydmljZSBhbmQgYW55IG90aGVyIEFJLXJlbGF0ZWQgY29tcG9uZW50czpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IG1haW4uZ29cbi4uLlxuICAgIDE3XHRcbiAgICAxOFx0ZnVuYyBtYWluKCkge1xuICAgIDE5XHRcdC8vIExvYWQgY29uZmlndXJhdGlvblxuICAgIDIwXHRcdGNvbmZpZy5Mb2FkQ29uZmlnKClcbiAgICAyMVx0XHRjZmcgOj0gY29uZmlnLkFwcENvbmZpZ1xuICAgIDIyXHRcbiAgICAyM1x0XHRsb2cuUHJpbnRmKFwi8J+agCBTdGFydGluZyBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIilcbiAgICAyNFx0XHRsb2cuUHJpbnRmKFwi8J+TiyBTZXJ2ZXI6ICVzOiVzXCIsIGNmZy5TZXJ2ZXIuSG9zdCwgY2ZnLlNlcnZlci5Qb3J0KVxuICAgIDI1XHRcdGxvZy5QcmludGYoXCLwn46vIEFnb3JhIEFwcCBJRDogJXNcIiwgY2ZnLkFnb3JhLkFwcElEKVxuICAgIDI2XHRcbiAgICAyN1x0XHQvLyBJbml0aWFsaXplIHNlcnZpY2VzXG4gICAgMjhcdFx0bm90aWZpY2F0aW9uU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdOb3RpZmljYXRpb25TZXJ2aWNlKClcbiAgICAyOVx0XHRhZ29yYVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QWdvcmFTZXJ2aWNlKGNmZylcbiAgICAzMFx0XHRhaVNlcnZpY2UgOj0gc2VydmljZXMuTmV3QUlTZXJ2aWNlKGNmZylcbiAgICAzMVx0XHRjYWxsTWFuYWdlciA6PSBzZXJ2aWNlcy5OZXdDYWxsTWFuYWdlcihub3RpZmljYXRpb25TZXJ2aWNlLCBhaVNlcnZpY2UsIGFnb3JhU2VydmljZSlcbiAgICAzMlx0XG4gICAgMzNcdFx0Ly8gSW5pdGlhbGl6ZSBoYW5kbGVyc1xuICAgIDM0XHRcdGNhbGxIYW5kbGVyIDo9IGhhbmRsZXJzLk5ld0NhbGxIYW5kbGVyKGNhbGxNYW5hZ2VyLCBhZ29yYVNlcnZpY2UpXG4gICAgMzVcdFxuICAgIDM2XHRcdC8vIFNldHVwIEdpbiByb3V0ZXJcbiAgICAzN1x0XHRyb3V0ZXIgOj0gc2V0dXBSb3V0ZXIoY2FsbEhhbmRsZXIpXG4gICAgMzhcdFxuICAgIDM5XHRcdC8vIFNldHVwIGdyYWNlZnVsIHNodXRkb3duXG4gICAgNDBcdFx0c2V0dXBHcmFjZWZ1bFNodXRkb3duKGFnb3JhU2VydmljZSlcbi4uLlxuICAgIDY1XHRcbiAgICA2Nlx0XHQvLyBBUEkgcm91dGVzXG4gICAgNjdcdFx0YXBpIDo9IHJvdXRlci5Hcm91cChcIi9hcGlcIilcbiAgICA2OFx0XHR7XG4gICAgNjlcdFx0XHQvLyBDYWxsIG1hbmFnZW1lbnQgcm91dGVzXG4gICAgNzBcdFx0XHRjYWxscyA6PSBhcGkuR3JvdXAoXCIvY2FsbHNcIilcbiAgICA3MVx0XHRcdHtcbiAgICA3Mlx0XHRcdFx0Y2FsbHMuUE9TVChcIi9pbml0aWF0ZVwiLCBjYWxsSGFuZGxlci5Jbml0aWF0ZUNhbGwpICAgICAgICAgICAgICAgICAgICAvLyBBSSBjYWxscyBjdXN0b21lclxuICAgIDczXHRcdFx0XHRjYWxscy5QT1NUKFwiL2N1c3RvbWVyLWluaXRpYXRlXCIsIGNhbGxIYW5kbGVyLkluaXRpYXRlQ3VzdG9tZXJDYWxsKSAgIC8vIEN1c3RvbWVyIGNhbGxzIEFJXG4gICAgNzRcdFx0XHRcdGNhbGxzLlBPU1QoXCIvOmNhbGxfaWQvam9pblwiLCBjYWxsSGFuZGxlci5Kb2luQ2FsbClcbiAgICA3NVx0XHRcdFx0Y2FsbHMuR0VUKFwiLzpjYWxsX2lkXCIsIGNhbGxIYW5kbGVyLkdldENhbGwpXG4gICAgNzZcdFx0XHRcdGNhbGxzLlBPU1QoXCIvOmNhbGxfaWQvZW5kXCIsIGNhbGxIYW5kbGVyLkVuZENhbGwpXG4gICAgNzdcdFx0XHRcdGNhbGxzLkdFVChcIi9hY3RpdmVcIiwgY2FsbEhhbmRsZXIuR2V0QWN0aXZlQ2FsbHMpXG4gICAgNzhcdFx0XHRcdGNhbGxzLkdFVChcIi86Y2FsbF9pZC9zdGF0c1wiLCBjYWxsSGFuZGxlci5HZXRDYWxsU3RhdHMpXG4gICAgNzlcdFx0XHRcdGNhbGxzLlBPU1QoXCIvOmNhbGxfaWQvdG9rZW5cIiwgY2FsbEhhbmRsZXIuR2VuZXJhdGVUb2tlbilcbiAgICA4MFx0XHRcdH1cbiAgICA4MVx0XHR9XG4gICAgODJcdFxuICAgIDgzXHRcdC8vIFdlbGNvbWUgcm91dGVcbiAgICA4NFx0XHRyb3V0ZXIuR0VUKFwiL1wiLCBmdW5jKGMgKmdpbi5Db250ZXh0KSB7XG4gICAgODVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgZ2luLkh7XG4gICAgODZcdFx0XHRcdFwibWVzc2FnZVwiOiBcIvCfpJYgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXCIsXG4gICAgODdcdFx0XHRcdFwidmVyc2lvblwiOiBcIjEuMC4wXCIsXG4gICAgODhcdFx0XHRcdFwic3RhdHVzXCI6ICBcInJ1bm5pbmdcIixcbiAgICA4OVx0XHRcdFx0XCJlbmRwb2ludHNcIjogZ2luLkh7XG4gICAgOTBcdFx0XHRcdFx0XCJoZWFsdGhcIjogICAgICAgICAgICAgICAgXCJHRVQgL2hlYWx0aFwiLFxuICAgIDkxXHRcdFx0XHRcdFwiaW5pdGlhdGVfY2FsbFwiOiAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzL2luaXRpYXRlXCIsXG4gICAgOTJcdFx0XHRcdFx0XCJjdXN0b21lcl9pbml0aWF0ZVwiOiAgICAgXCJQT1NUIC9hcGkvY2FsbHMvY3VzdG9tZXItaW5pdGlhdGVcIixcbiAgICA5M1x0XHRcdFx0XHRcImpvaW5fY2FsbFwiOiAgICAgICAgICAgICBcIlBPU1QgL2FwaS9jYWxscy86Y2FsbF9pZC9qb2luXCIsXG4gICAgOTRcdFx0XHRcdFx0XCJnZXRfY2FsbFwiOiAgICAgICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy86Y2FsbF9pZFwiLFxuICAgIDk1XHRcdFx0XHRcdFwiZW5kX2NhbGxcIjogICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2VuZFwiLFxuICAgIDk2XHRcdFx0XHRcdFwiYWN0aXZlX2NhbGxzXCI6ICAgICAgICAgIFwiR0VUIC9hcGkvY2FsbHMvYWN0aXZlXCIsXG4gICAgOTdcdFx0XHRcdFx0XCJjYWxsX3N0YXRzXCI6ICAgICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy86Y2FsbF9pZC9zdGF0c1wiLFxuICAgIDk4XHRcdFx0XHRcdFwiZ2VuZXJhdGVfdG9rZW5cIjogICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3Rva2VuXCIsXG4gICAgOTlcdFx0XHRcdH0sXG4gICAxMDBcdFx0XHR9KVxuICAgMTAxXHRcdH0pXG4gICAxMDJcdFxuICAgMTAzXHRcdGxvZy5QcmludGYoXCLwn5uj77iPIFJvdXRlcyBjb25maWd1cmVkIHN1Y2Nlc3NmdWxseVwiKVxuICAgMTA0XHRcdHJldHVybiByb3V0ZXJcbiAgIDEwNVx0fVxuLi4uXG5QYXRoOiBoYW5kbGVycy9jYWxsX2hhbmRsZXIuZ29cbiAgICAgMVx0cGFja2FnZSBoYW5kbGVyc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvbW9kZWxzXCJcbiAgICAgNVx0XHRcIkJhY2tlbmQvc2VydmljZXNcIlxuICAgICA2XHRcdFwiZm10XCJcbiAgICAgN1x0XHRcIm5ldC9odHRwXCJcbiAgICAgOFx0XHRcInRpbWVcIlxuICAgICA5XHRcbiAgICAxMFx0XHRcImdpdGh1Yi5jb20vZ2luLWdvbmljL2dpblwiXG4gICAgMTFcdClcbiAgICAxMlx0XG4gICAgMTNcdC8vIENhbGxIYW5kbGVyIGhhbmRsZXMgSFRUUCByZXF1ZXN0cyByZWxhdGVkIHRvIGNhbGxzXG4gICAgMTRcdHR5cGUgQ2FsbEhhbmRsZXIgc3RydWN0IHtcbiAgICAxNVx0XHRjYWxsTWFuYWdlciAgKnNlcnZpY2VzLkNhbGxNYW5hZ2VyXG4gICAgMTZcdFx0YWdvcmFTZXJ2aWNlICpzZXJ2aWNlcy5BZ29yYVNlcnZpY2VcbiAgICAxN1x0fVxuICAgIDE4XHRcbiAgICAxOVx0Ly8gTmV3Q2FsbEhhbmRsZXIgY3JlYXRlcyBhIG5ldyBjYWxsIGhhbmRsZXIgaW5zdGFuY2VcbiAgICAyMFx0ZnVuYyBOZXdDYWxsSGFuZGxlcihjYWxsTWFuYWdlciAqc2VydmljZXMuQ2FsbE1hbmFnZXIsIGFnb3JhU2VydmljZSAqc2VydmljZXMuQWdvcmFTZXJ2aWNlKSAqQ2FsbEhhbmRsZXIge1xuICAgIDIxXHRcdHJldHVybiAmQ2FsbEhhbmRsZXJ7XG4gICAgMjJcdFx0XHRjYWxsTWFuYWdlcjogIGNhbGxNYW5hZ2VyLFxuICAgIDIzXHRcdFx0YWdvcmFTZXJ2aWNlOiBhZ29yYVNlcnZpY2UsXG4gICAgMjRcdFx0fVxuICAgIDI1XHR9XG4uLi5cbiAgICA2Mlx0XG4gICAgNjNcdFx0Ly8gR2VuZXJhdGUgQWdvcmEgdG9rZW4gZm9yIHRoZSBjaGFubmVsXG4gICAgNjRcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCBcImFpX2FnZW50XCIsIDEpIC8vIFJvbGUgMSA9IHB1Ymxpc2hlclxuICAgIDY1XHRcdGlmIGVyciAhPSBuaWwge1xuICAgIDY2XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgNjdcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSBBZ29yYSB0b2tlblwiLFxuICAgIDY4XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAgNjlcdFx0XHR9KVxuICAgIDcwXHRcdFx0cmV0dXJuXG4gICAgNzFcdFx0fVxuICAgIDcyXHRcbiAgICA3M1x0XHQvLyBKb2luIHRoZSBjaGFubmVsIGFzIEFJIGFnZW50XG4gICAgNzRcdFx0aWYgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5Kb2luQ2hhbm5lbChjYWxsLkNoYW5uZWxOYW1lLCBcImFpX2FnZW50XCIpOyBlcnIgIT0gbmlsIHtcbiAgICA3NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgIDc2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gam9pbiBBZ29yYSBjaGFubmVsXCIsXG4gICAgNzdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA3OFx0XHRcdH0pXG4gICAgNzlcdFx0XHRyZXR1cm5cbiAgICA4MFx0XHR9XG4gICAgODFcdFxuICAgIDgyXHRcdC8vIFNldCB1cCBhdWRpbyBwcm9jZXNzaW5nIGZvciB0aGlzIGNhbGxcbiAgICA4M1x0XHRhdWRpb0NhbGxiYWNrIDo9IGZ1bmMoYXVkaW9EYXRhIFtdYnl0ZSkgZXJyb3Ige1xuICAgIDg0XHRcdFx0cmV0dXJuIGNoLmNhbGxNYW5hZ2VyLlByb2Nlc3NBdWRpb01lc3NhZ2UoY2FsbC5JRCwgYXVkaW9EYXRhKVxuICAgIDg1XHRcdH1cbi4uLlxuICAgMTMxXHRcbiAgIDEzMlx0XHQvLyBJbml0aWF0ZSB0aGUgY3VzdG9tZXItdG8tQUkgY2FsbFxuICAgMTMzXHRcdGNhbGwsIGVyciA6PSBjaC5jYWxsTWFuYWdlci5Jbml0aWF0ZUN1c3RvbWVyQ2FsbChyZXF1ZXN0KVxuICAgMTM0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTM1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAxMzZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBpbml0aWF0ZSBjdXN0b21lciBjYWxsXCIsXG4gICAxMzdcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDEzOFx0XHRcdH0pXG4gICAxMzlcdFx0XHRyZXR1cm5cbiAgIDE0MFx0XHR9XG4gICAxNDFcdFxuICAgMTQyXHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY3VzdG9tZXJcbiAgIDE0M1x0XHRjdXN0b21lclRva2VuLCBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkdlbmVyYXRlVG9rZW4oY2FsbC5DaGFubmVsTmFtZSwgcmVxdWVzdC5DdXN0b21lcklELCAxKSAvLyBSb2xlIDEgPSBwdWJsaXNoZXJcbiAgIDE0NFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE0NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0ludGVybmFsU2VydmVyRXJyb3IsIGdpbi5Ie1xuICAgMTQ2XHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gZ2VuZXJhdGUgY3VzdG9tZXIgQWdvcmEgdG9rZW5cIixcbiAgIDE0N1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMTQ4XHRcdFx0fSlcbiAgIDE0OVx0XHRcdHJldHVyblxuICAgMTUwXHRcdH1cbi4uLlxuICAgMTczXHRcbiAgIDE3NFx0XHQvLyBBZGQgaW5pdGlhbCBBSSBncmVldGluZ1xuICAgMTc1XHRcdGdyZWV0aW5nIDo9IGNoLmdlbmVyYXRlQ3VzdG9tZXJDYWxsR3JlZXRpbmcoY2FsbClcbiAgIDE3Nlx0XHRjYWxsLkFkZE1lc3NhZ2UoXCJhaVwiLCBncmVldGluZywgXCJ0ZXh0XCIpXG4gICAxNzdcdFxuICAgMTc4XHRcdC8vIFByZXBhcmUgQUkgaW5mb1xuICAgMTc5XHRcdGFpSW5mbyA6PSBtb2RlbHMuQUlJbmZve1xuICAgMTgwXHRcdFx0TmFtZTogICAgICAgIFwiQUkgU3VwcG9ydCBBc3Npc3RhbnRcIixcbiAgIDE4MVx0XHRcdFBlcnNvbmFsaXR5OiBjYWxsLk1ldGFkYXRhLkFJUGVyc29uYWxpdHksXG4gICAxODJcdFx0XHRMYW5ndWFnZTogICAgY2FsbC5NZXRhZGF0YS5MYW5ndWFnZSxcbiAgIDE4M1x0XHRcdENhcGFiaWxpdGllczogW11zdHJpbmd7XG4gICAxODRcdFx0XHRcdFwiYmlsbGluZ19zdXBwb3J0XCIsXG4gICAxODVcdFx0XHRcdFwidGVjaG5pY2FsX3N1cHBvcnRcIixcbiAgIDE4Nlx0XHRcdFx0XCJhY2NvdW50X21hbmFnZW1lbnRcIixcbiAgIDE4N1x0XHRcdFx0XCJnZW5lcmFsX2lucXVpcmllc1wiLFxuICAgMTg4XHRcdFx0XHRcInBhc3N3b3JkX3Jlc2V0XCIsXG4gICAxODlcdFx0XHRcdFwic3Vic2NyaXB0aW9uX21hbmFnZW1lbnRcIixcbiAgIDE5MFx0XHRcdH0sXG4gICAxOTFcdFx0fVxuICAgMTkyXHRcbiAgIDE5M1x0XHRyZXNwb25zZSA6PSBtb2RlbHMuSW5pdGlhdGVDdXN0b21lckNhbGxSZXNwb25zZXtcbiAgIDE5NFx0XHRcdENhbGxJRDogICAgICBjYWxsLklELFxuICAgMTk1XHRcdFx0Q2hhbm5lbE5hbWU6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAxOTZcdFx0XHRUb2tlbjogICAgICAgY3VzdG9tZXJUb2tlbi5Ub2tlbixcbiAgIDE5N1x0XHRcdFN0YXR1czogICAgICBzdHJpbmcoY2FsbC5TdGF0dXMpLFxuICAgMTk4XHRcdFx0TWVzc2FnZTogICAgIFwiQ3VzdG9tZXIgY2FsbCBpbml0aWF0ZWQgc3VjY2Vzc2Z1bGx5LiBBSSBpcyByZWFkeSB0byBhc3Npc3QuXCIsXG4gICAxOTlcdFx0XHRBSUluZm86ICAgICAgYWlJbmZvLFxuICAgMjAwXHRcdH1cbiAgIDIwMVx0XG4gICAyMDJcdFx0Yy5KU09OKGh0dHAuU3RhdHVzQ3JlYXRlZCwgcmVzcG9uc2UpXG4gICAyMDNcdH1cbi4uLlxuICAgMjQ4XHRcbiAgIDI0OVx0XHQvLyBKb2luIHRoZSBjYWxsXG4gICAyNTBcdFx0Y2FsbCwgZXJyIDo9IGNoLmNhbGxNYW5hZ2VyLkpvaW5DYWxsKHJlcXVlc3QuQ2FsbElELCByZXF1ZXN0LkN1c3RvbWVySUQpXG4gICAyNTFcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAyNTJcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNCYWRSZXF1ZXN0LCBnaW4uSHtcbiAgIDI1M1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGpvaW4gY2FsbFwiLFxuICAgMjU0XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAyNTVcdFx0XHR9KVxuICAgMjU2XHRcdFx0cmV0dXJuXG4gICAyNTdcdFx0fVxuICAgMjU4XHRcbiAgIDI1OVx0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGN1c3RvbWVyXG4gICAyNjBcdFx0dG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCByZXF1ZXN0LkN1c3RvbWVySUQsIDEpIC8vIFJvbGUgMSA9IHB1Ymxpc2hlclxuICAgMjYxXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMjYyXHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAyNjNcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSBBZ29yYSB0b2tlblwiLFxuICAgMjY0XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAyNjVcdFx0XHR9KVxuICAgMjY2XHRcdFx0cmV0dXJuXG4gICAyNjdcdFx0fVxuICAgMjY4XHRcbiAgIDI2OVx0XHRyZXNwb25zZSA6PSBtb2RlbHMuSm9pbkNhbGxSZXNwb25zZXtcbiAgIDI3MFx0XHRcdENoYW5uZWxOYW1lOiBjYWxsLkNoYW5uZWxOYW1lLFxuICAgMjcxXHRcdFx0VG9rZW46ICAgICAgIHRva2VuLlRva2VuLFxuICAgMjcyXHRcdFx0Q2FsbEluZm86ICAgICpjYWxsLFxuICAgMjczXHRcdH1cbiAgIDI3NFx0XG4gICAyNzVcdFx0Yy5KU09OKGh0dHAuU3RhdHVzT0ssIHJlc3BvbnNlKVxuICAgMjc2XHR9XG4uLi5cblBhdGg6IHNlcnZpY2VzL2NhbGxfbWFuYWdlci5nb1xuICAgICAxXHRwYWNrYWdlIHNlcnZpY2VzXG4gICAgIDJcdFxuICAgICAzXHRpbXBvcnQgKFxuICAgICA0XHRcdFwiQmFja2VuZC9tb2RlbHNcIlxuICAgICA1XHRcdFwiZm10XCJcbiAgICAgNlx0XHRcImxvZ1wiXG4gICAgIDdcdFx0XCJzeW5jXCJcbiAgICAgOFx0XHRcInRpbWVcIlxuICAgICA5XHQpXG4gICAgMTBcdFxuICAgIDExXHQvLyBDYWxsTWFuYWdlciBtYW5hZ2VzIGFjdGl2ZSBjYWxscyBhbmQgdGhlaXIgbGlmZWN5Y2xlXG4gICAgMTJcdHR5cGUgQ2FsbE1hbmFnZXIgc3RydWN0IHtcbiAgICAxM1x0XHRjYWxscyAgICAgICAgICAgICAgIG1hcFtzdHJpbmddKm1vZGVscy5DYWxsXG4gICAgMTRcdFx0bXV0ZXggICAgICAgICAgICAgICBzeW5jLlJXTXV0ZXhcbiAgICAxNVx0XHRub3RpZmljYXRpb25TZXJ2aWNlICpOb3RpZmljYXRpb25TZXJ2aWNlXG4gICAgMTZcdFx0YWlTZXJ2aWNlICAgICAgICAgICpBSVNlcnZpY2VcbiAgICAxN1x0XHRhZ29yYVNlcnZpY2UgICAgICAgKkFnb3JhU2VydmljZVxuICAgIDE4XHR9XG4gICAgMTlcdFxuICAgIDIwXHQvLyBOZXdDYWxsTWFuYWdlciBjcmVhdGVzIGEgbmV3IGNhbGwgbWFuYWdlciBpbnN0YW5jZVxuICAgIDIxXHRmdW5jIE5ld0NhbGxNYW5hZ2VyKG5vdGlmaWNhdGlvblNlcnZpY2UgKk5vdGlmaWNhdGlvblNlcnZpY2UsIGFpU2VydmljZSAqQUlTZXJ2aWNlLCBhZ29yYVNlcnZpY2UgKkFnb3JhU2VydmljZSkgKkNhbGxNYW5hZ2VyIHtcbiAgICAyMlx0XHRyZXR1cm4gJkNhbGxNYW5hZ2Vye1xuICAgIDIzXHRcdFx0Y2FsbHM6ICAgICAgICAgICAgICAgbWFrZShtYXBbc3RyaW5nXSptb2RlbHMuQ2FsbCksXG4gICAgMjRcdFx0XHRub3RpZmljYXRpb25TZXJ2aWNlOiBub3RpZmljYXRpb25TZXJ2aWNlLFxuICAgIDI1XHRcdFx0YWlTZXJ2aWNlOiAgICAgICAgICBhaVNlcnZpY2UsXG4gICAgMjZcdFx0XHRhZ29yYVNlcnZpY2U6ICAgICAgIGFnb3JhU2VydmljZSxcbiAgICAyN1x0XHR9XG4gICAgMjhcdH1cbi4uLlxuICAgIDY5XHRcbiAgICA3MFx0Ly8gSW5pdGlhdGVDdXN0b21lckNhbGwgc3RhcnRzIGEgbmV3IGN1c3RvbWVyLXRvLUFJIGNhbGwgKGN1c3RvbWVyIGNhbGxzIEFJKVxuICAgIDcxXHRmdW5jIChjbSAqQ2FsbE1hbmFnZXIpIEluaXRpYXRlQ3VzdG9tZXJDYWxsKHJlcXVlc3QgbW9kZWxzLkluaXRpYXRlQ3VzdG9tZXJDYWxsUmVxdWVzdCkgKCptb2RlbHMuQ2FsbCwgZXJyb3IpIHtcbiAgICA3Mlx0XHRjbS5tdXRleC5Mb2NrKClcbiAgICA3M1x0XHRkZWZlciBjbS5tdXRleC5VbmxvY2soKVxuICAgIDc0XHRcbiAgICA3NVx0XHRsb2cuUHJpbnRmKFwi8J+TniBJbml0aWF0aW5nIG5ldyBjdXN0b21lci10by1BSSBjYWxsIGZvciB1c2VyIElEOiAlZCwgcHVycG9zZTogJXNcIiwgcmVxdWVzdC5Vc2VySUQsIHJlcXVlc3QuUHVycG9zZSlcbiAgICA3Nlx0XG4gICAgNzdcdFx0Ly8gQ3JlYXRlIG5ldyBjdXN0b21lciBjYWxsXG4gICAgNzhcdFx0Y2FsbCA6PSBtb2RlbHMuTmV3Q3VzdG9tZXJDYWxsKHJlcXVlc3QuVXNlcklELCByZXF1ZXN0LlB1cnBvc2UsIHJlcXVlc3QuQ3VzdG9tZXJJbmZvKVxuICAgIDc5XHRcbiAgICA4MFx0XHQvLyBTZXQgYWRkaXRpb25hbCBtZXRhZGF0YVxuICAgIDgxXHRcdGlmIHJlcXVlc3QuQUlQZXJzb25hbGl0eSAhPSBcIlwiIHtcbiAgICA4Mlx0XHRcdGNhbGwuTWV0YWRhdGEuQUlQZXJzb25hbGl0eSA9IHJlcXVlc3QuQUlQZXJzb25hbGl0eVxuICAgIDgzXHRcdH1cbiAgICA4NFx0XHRpZiByZXF1ZXN0Lkxhbmd1YWdlICE9IFwiXCIge1xuICAgIDg1XHRcdFx0Y2FsbC5NZXRhZGF0YS5MYW5ndWFnZSA9IHJlcXVlc3QuTGFuZ3VhZ2VcbiAgICA4Nlx0XHR9XG4gICAgODdcdFx0aWYgcmVxdWVzdC5DdXN0b21GaWVsZHMgIT0gbmlsIHtcbiAgICA4OFx0XHRcdGNhbGwuTWV0YWRhdGEuQ3VzdG9tRmllbGRzID0gcmVxdWVzdC5DdXN0b21GaWVsZHNcbiAgICA4OVx0XHR9XG4uLi5cblBhdGg6IFJFQURNRS5tZFxuICAgICAxXHQjIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFxuICAgICAyXHRcbiAgICAgM1x0QSBHbyBiYWNrZW5kIHNlcnZpY2UgdGhhdCBwcm92aWRlcyBBSS1wb3dlcmVkIGN1c3RvbWVyIHN1cHBvcnQgdGhyb3VnaCB2b2ljZSBjYWxscyB1c2luZyBBZ29yYSBSVEMgQVBJLiBUaGUgQUkgY2FuIGluaXRpYXRlIGNhbGxzIHRvIGN1c3RvbWVycyBhbmQgcHJvdmlkZSByZWFsLXRpbWUgdm9pY2UgYXNzaXN0YW5jZS5cbiAgICAgNFx0XG4gICAgIDVcdCMjIPCfmoAgRmVhdHVyZXNcbi4uLlxuICAgIDg0XHRcbiAgICA4NVx0fCBNZXRob2QgfCBFbmRwb2ludCB8IERlc2NyaXB0aW9uIHxcbiAgICA4Nlx0fC0tLS0tLS0tfC0tLS0tLS0tLS18LS0tLS0tLS0tLS0tLXxcbiAgICA4N1x0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy9pbml0aWF0ZWAgfCDwn6SW4p6h77iP8J+RpCBJbml0aWF0ZSBBSSBjYWxsIHRvIGN1c3RvbWVyIHxcbiAgICA4OFx0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy9jdXN0b21lci1pbml0aWF0ZWAgfCDwn5Gk4p6h77iP8J+kliBDdXN0b21lciBpbml0aWF0ZXMgY2FsbCB0byBBSSB8XG4gICAgODlcdHwgYFBPU1RgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWQvam9pbmAgfCBDdXN0b21lciBqb2lucyBhbiBleGlzdGluZyBjYWxsIHxcbiAgICA5MFx0fCBgR0VUYCB8IGAvYXBpL2NhbGxzLzpjYWxsX2lkYCB8IEdldCBjYWxsIGRldGFpbHMgfFxuICAgIDkxXHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzLzpjYWxsX2lkL2VuZGAgfCBFbmQgYSBjYWxsIHxcbiAgICA5Mlx0fCBgR0VUYCB8IGAvYXBpL2NhbGxzL2FjdGl2ZWAgfCBMaXN0IGFsbCBhY3RpdmUgY2FsbHMgfFxuICAgIDkzXHR8IGBHRVRgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWQvc3RhdHNgIHwgR2V0IGNhbGwgc3RhdGlzdGljcyB8XG4gICAgOTRcdHwgYFBPU1RgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWQvdG9rZW5gIHwgR2VuZXJhdGUgQWdvcmEgdG9rZW4gfFxuICAgIDk1XHRcbiAgICA5Nlx0IyMg8J+TliBVc2FnZSBFeGFtcGxlc1xuICAgIDk3XHRcbiAgICA5OFx0IyMjIDEuIPCfpJbinqHvuI/wn5GkIEluaXRpYXRlIEFJLXRvLUN1c3RvbWVyIENhbGxcbi4uLlxuUGF0aDogc2VydmljZXMvYWlfc2VydmljZS5nb1xuLi4uXG4gICAgNzhcdFx0XG4gICAgNzlcdFx0Ly8gTW9jayByZXNwb25zZXMgYmFzZWQgb24gYXVkaW8gbGVuZ3RoIHRvIHNpbXVsYXRlIHJlYWxpc3RpYyBiZWhhdmlvclxuICAgIDgwXHRcdG1vY2tSZXNwb25zZXMgOj0gW11zdHJpbmd7XG4gICAgODFcdFx0XHRcIkhlbGxvLCBJIG5lZWQgaGVscCB3aXRoIG15IGFjY291bnRcIixcbiAgICA4Mlx0XHRcdFwiQ2FuIHlvdSBoZWxwIG1lIHJlc2V0IG15IHBhc3N3b3JkP1wiLFxuICAgIDgzXHRcdFx0XCJJJ20gaGF2aW5nIHRyb3VibGUgd2l0aCBteSBiaWxsaW5nXCIsXG4gICAgODRcdFx0XHRcIldoYXQgYXJlIHlvdXIgYnVzaW5lc3MgaG91cnM/XCIsXG4gICAgODVcdFx0XHRcIkkgd2FudCB0byBjYW5jZWwgbXkgc3Vic2NyaXB0aW9uXCIsXG4gICAgODZcdFx0XHRcIkhvdyBkbyBJIHVwZGF0ZSBteSBwYXltZW50IG1ldGhvZD9cIixcbiAgICA4N1x0XHRcdFwiWWVzLCB0aGF0IHNvdW5kcyBnb29kXCIsXG4gICAgODhcdFx0XHRcIk5vLCB0aGF0J3Mgbm90IHdoYXQgSSBtZWFudFwiLFxuICAgIDg5XHRcdFx0XCJDYW4geW91IHJlcGVhdCB0aGF0IHBsZWFzZT9cIixcbiAgICA5MFx0XHRcdFwiVGhhbmsgeW91IGZvciB5b3VyIGhlbHBcIixcbiAgICA5MVx0XHR9XG4gICAgOTJcdFx0XG4gICAgOTNcdFx0Ly8gU2ltcGxlIG1vY2sgc2VsZWN0aW9uIGJhc2VkIG9uIGF1ZGlvIGRhdGEgbGVuZ3RoXG4gICAgOTRcdFx0cmVzcG9uc2VJbmRleCA6PSBsZW4oYXVkaW9EYXRhKSAlIGxlbihtb2NrUmVzcG9uc2VzKVxuICAgIDk1XHRcdFxuICAgIDk2XHRcdHJlc3BvbnNlIDo9ICZTVFRSZXNwb25zZXtcbiAgICA5N1x0XHRcdFRleHQ6ICAgICAgIG1vY2tSZXNwb25zZXNbcmVzcG9uc2VJbmRleF0sXG4gICAgOThcdFx0XHRDb25maWRlbmNlOiAwLjg1ICsgZmxvYXQ2NChsZW4oYXVkaW9EYXRhKSUxNSkvMTAwLCAvLyBNb2NrIGNvbmZpZGVuY2UgYmV0d2VlbiAwLjg1LTEuMFxuICAgIDk5XHRcdFx0TGFuZ3VhZ2U6ICAgXCJlblwiLFxuICAgMTAwXHRcdH1cbiAgIDEwMVx0XHRcbiAgIDEwMlx0XHRsb2cuUHJpbnRmKFwi8J+TnSBbTU9DS10gU1RUIFJlc3VsdDogJyVzJyAoY29uZmlkZW5jZTogJS4yZilcIiwgcmVzcG9uc2UuVGV4dCwgcmVzcG9uc2UuQ29uZmlkZW5jZSlcbiAgIDEwM1x0XHRyZXR1cm4gcmVzcG9uc2UsIG5pbFxuICAgMTA0XHR9XG4gICAxMDVcdFxuICAgMTA2XHQvLyBHZW5lcmF0ZUFJUmVzcG9uc2UgZ2VuZXJhdGVzIEFJIHJlc3BvbnNlIHVzaW5nIExMTVxuICAgMTA3XHRmdW5jIChhaSAqQUlTZXJ2aWNlKSBHZW5lcmF0ZUFJUmVzcG9uc2UoY2FsbCAqbW9kZWxzLkNhbGwsIHVzZXJNZXNzYWdlIHN0cmluZykgKHN0cmluZywgZXJyb3IpIHtcbiAgIDEwOFx0XHRsb2cuUHJpbnRmKFwi8J+kliBQcm9jZXNzaW5nIEFJIHJlc3BvbnNlIGZvciBjYWxsICVzXCIsIGNhbGwuSUQpXG4gICAxMDlcdFx0XG4gICAxMTBcdFx0Ly8gQnVpbGQgY29udmVyc2F0aW9uIGNvbnRleHRcbiAgIDExMVx0XHRtZXNzYWdlcyA6PSBhaS5idWlsZENvbnZlcnNhdGlvbkNvbnRleHQoY2FsbCwgdXNlck1lc3NhZ2UpXG4gICAxMTJcdFx0XG4gICAxMTNcdFx0Ly8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNhbGwgT3BlbkFJIEFQSVxuICAgMTE0XHRcdGlmIGFpLmNvbmZpZy5BSS5PcGVuQUlBUElLZXkgIT0gXCJcIiB7XG4gICAxMTVcdFx0XHRyZXR1cm4gYWkuY2FsbE9wZW5BSUFQSShtZXNzYWdlcylcbiAgIDExNlx0XHR9XG4gICAxMTdcdFx0XG4gICAxMThcdFx0Ly8gTW9jayBBSSByZXNwb25zZVxuICAgMTE5XHRcdHJldHVybiBhaS5nZW5lcmF0ZU1vY2tBSVJlc3BvbnNlKHVzZXJNZXNzYWdlLCBjYWxsLk1ldGFkYXRhLlB1cnBvc2UpXG4gICAxMjBcdH1cbi4uLlxuICAgMjU5XHRcbiAgIDI2MFx0Ly8gUHJvY2Vzc1ZBRCBwZXJmb3JtcyBWb2ljZSBBY3Rpdml0eSBEZXRlY3Rpb25cbiAgIDI2MVx0ZnVuYyAoYWkgKkFJU2VydmljZSkgUHJvY2Vzc1ZBRChhdWRpb0RhdGEgW11ieXRlKSAoYm9vbCwgZXJyb3IpIHtcbiAgIDI2Mlx0XHRpZiAhYWkuY29uZmlnLkFJLkVuYWJsZVZBRCB7XG4gICAyNjNcdFx0XHRyZXR1cm4gdHJ1ZSwgbmlsIC8vIEFsd2F5cyBjb25zaWRlciBhcyBzcGVlY2ggaWYgVkFEIGlzIGRpc2FibGVkXG4gICAyNjRcdFx0fVxuICAgMjY1XHRcdFxuICAgMjY2XHRcdC8vIE1vY2sgVkFEIHByb2Nlc3NpbmdcbiAgIDI2N1x0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIHVzZSBBZ29yYSdzIFZBRCBvciBvdGhlciBWQUQgYWxnb3JpdGhtc1xuICAgMjY4XHRcdFxuICAgMjY5XHRcdC8vIFNpbXBsZSBtb2NrOiBjb25zaWRlciBpdCBzcGVlY2ggaWYgYXVkaW8gZGF0YSBoYXMgc3VmZmljaWVudCBcImVuZXJneVwiXG4gICAyNzBcdFx0aWYgbGVuKGF1ZGlvRGF0YSkgPCAxMDAge1xuICAgMjcxXHRcdFx0cmV0dXJuIGZhbHNlLCBuaWwgLy8gVG9vIHNob3J0IHRvIGJlIHNwZWVjaFxuICAgMjcyXHRcdH1cbiAgIDI3M1x0XHRcbiAgIDI3NFx0XHQvLyBNb2NrIGVuZXJneSBjYWxjdWxhdGlvblxuICAgMjc1XHRcdHZhciBlbmVyZ3kgaW50NjRcbiAgIDI3Nlx0XHRmb3IgXywgc2FtcGxlIDo9IHJhbmdlIGF1ZGlvRGF0YSB7XG4gICAyNzdcdFx0XHRlbmVyZ3kgKz0gaW50NjQoc2FtcGxlICogc2FtcGxlKVxuICAgMjc4XHRcdH1cbiAgIDI3OVx0XHRcbiAgIDI4MFx0XHRhdmdFbmVyZ3kgOj0gZmxvYXQ2NChlbmVyZ3kpIC8gZmxvYXQ2NChsZW4oYXVkaW9EYXRhKSlcbiAgIDI4MVx0XHR0aHJlc2hvbGQgOj0gMTAwMC4wICogYWkuY29uZmlnLkFJLlZBRFNlbnNpdGl2aXR5XG4gICAyODJcdFx0XG4gICAyODNcdFx0aXNTcGVlY2ggOj0gYXZnRW5lcmd5ID4gdGhyZXNob2xkXG4gICAyODRcdFx0bG9nLlByaW50ZihcIvCfjpnvuI8gW01PQ0tdIFZBRDogRW5lcmd5PSUuMmYsIFRocmVzaG9sZD0lLjJmLCBJc1NwZWVjaD0ldlwiLCBhdmdFbmVyZ3ksIHRocmVzaG9sZCwgaXNTcGVlY2gpXG4gICAyODVcdFx0XG4gICAyODZcdFx0cmV0dXJuIGlzU3BlZWNoLCBuaWxcbiAgIDI4N1x0fVxuLi4uXG5QYXRoOiBzZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvXG4uLi5cbiAgICA2MFx0XG4gICAgNjFcdFx0aWYgYXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlICE9IFwiXCIge1xuICAgIDYyXHRcdFx0Ly8gR2VuZXJhdGUgcmVhbCB0b2tlbiB3aXRoIG9mZmljaWFsIEFnb3JhIGxpYnJhcnlcbiAgICA2M1x0XHRcdHRva2VuLCBlcnIgPSBhcy5nZW5lcmF0ZVJlYWxUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlLCB1aW50MzIoZXhwaXJhdGlvblRpbWUpKVxuICAgIDY0XHRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjVcdFx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gZ2VuZXJhdGUgdG9rZW46ICV3XCIsIGVycilcbiAgICA2Nlx0XHRcdH1cbiAgICA2N1x0XHRcdGxvZy5QcmludGYoXCLinIUgR2VuZXJhdGVkIHJlYWwgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCB0b2tlbiBpczogJXNcIiwgY2hhbm5lbE5hbWUsIHRva2VuKVxuICAgIDY4XHRcdH0gZWxzZSB7XG4gICAgNjlcdFx0XHQvLyBHZW5lcmF0ZSBtb2NrIHRva2VuIGZvciBkZXZlbG9wbWVudFxuICAgIDcwXHRcdFx0dG9rZW4gPSBhcy5nZW5lcmF0ZU1vY2tUb2tlbihjaGFubmVsTmFtZSwgdXNlcklELCByb2xlKVxuICAgIDcxXHRcdFx0bG9nLlByaW50ZihcIuKaoO+4jyBbTU9DS10gVXNpbmcgbW9jayB0b2tlbiAobm8gYXBwIGNlcnRpZmljYXRlIHByb3ZpZGVkKVwiKVxuICAgIDcyXHRcdH1cbiAgICA3M1x0XG4gICAgNzRcdFx0cmVzcG9uc2UgOj0gJlRva2VuUmVzcG9uc2V7XG4gICAgNzVcdFx0XHRUb2tlbjogICAgIHRva2VuLFxuICAgIDc2XHRcdFx0RXhwaXJlc0F0OiBleHBpcmF0aW9uVGltZSxcbiAgICA3N1x0XHR9XG4gICAgNzhcdFxuICAgIDc5XHRcdGxvZy5QcmludGYoXCLinIUgVG9rZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSBmb3IgY2hhbm5lbDogJXNcIiwgY2hhbm5lbE5hbWUpXG4gICAgODBcdFx0cmV0dXJuIHJlc3BvbnNlLCBuaWxcbiAgICA4MVx0fVxuLi4uXG4gICAgOTJcdFxuICAgIDkzXHRcdGlmIGFzLmNvbmZpZy5BZ29yYS5BcHBDZXJ0aWZpY2F0ZSAhPSBcIlwiIHtcbiAgICA5NFx0XHRcdC8vIEdlbmVyYXRlIHJlYWwgdG9rZW4gd2l0aCBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5IHVzaW5nIFVJRCBkaXJlY3RseVxuICAgIDk1XHRcdFx0dG9rZW4sIGVyciA9IGFzLmdlbmVyYXRlUmVhbFRva2VuV2l0aFVJRChjaGFubmVsTmFtZSwgdWlkLCByb2xlLCB1aW50MzIoZXhwaXJhdGlvblRpbWUpKVxuICAgIDk2XHRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgOTdcdFx0XHRcdHJldHVybiBuaWwsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gZ2VuZXJhdGUgdG9rZW46ICV3XCIsIGVycilcbiAgICA5OFx0XHRcdH1cbiAgICA5OVx0XHRcdGxvZy5QcmludGYoXCLinIUgR2VuZXJhdGVkIHJlYWwgQWdvcmEgdG9rZW4gZm9yIGNoYW5uZWw6ICVzLCBVSUQ6ICVkLCB0b2tlbjogJXNcIiwgY2hhbm5lbE5hbWUsIHVpZCwgdG9rZW4pXG4gICAxMDBcdFx0fSBlbHNlIHtcbiAgIDEwMVx0XHRcdC8vIEdlbmVyYXRlIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAxMDJcdFx0XHR0b2tlbiA9IGFzLmdlbmVyYXRlTW9ja1Rva2VuV2l0aFVJRChjaGFubmVsTmFtZSwgdWlkLCByb2xlKVxuICAgMTAzXHRcdFx0bG9nLlByaW50ZihcIuKaoO+4jyBbTU9DS10gVXNpbmcgbW9jayB0b2tlbiAobm8gYXBwIGNlcnRpZmljYXRlIHByb3ZpZGVkKVwiKVxuICAgMTA0XHRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdFx0cmVzcG9uc2UgOj0gJlRva2VuUmVzcG9uc2V7XG4gICAxMDdcdFx0XHRUb2tlbjogICAgIHRva2VuLFxuICAgMTA4XHRcdFx0RXhwaXJlc0F0OiBleHBpcmF0aW9uVGltZSxcbiAgIDEwOVx0XHR9XG4gICAxMTBcdFxuICAgMTExXHRcdGxvZy5QcmludGYoXCLinIUgVG9rZW4gZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSBmb3IgY2hhbm5lbDogJXMsIFVJRDogJWRcIiwgY2hhbm5lbE5hbWUsIHVpZClcbiAgIDExMlx0XHRyZXR1cm4gcmVzcG9uc2UsIG5pbFxuICAgMTEzXHR9XG4uLi5cbiAgIDE1NVx0XG4gICAxNTZcdC8vIGdlbmVyYXRlUmVhbFRva2VuV2l0aFVJRCBnZW5lcmF0ZXMgYSByZWFsIEFnb3JhIHRva2VuIHVzaW5nIFVJRCBkaXJlY3RseVxuICAgMTU3XHRmdW5jIChhcyAqQWdvcmFTZXJ2aWNlKSBnZW5lcmF0ZVJlYWxUb2tlbldpdGhVSUQoY2hhbm5lbE5hbWUgc3RyaW5nLCB1aWQgdWludDMyLCByb2xlIGludCwgZXhwaXJhdGlvbiB1aW50MzIpIChzdHJpbmcsIGVycm9yKSB7XG4gICAxNThcdFx0Ly8gQ29udmVydCByb2xlIHRvIEFnb3JhIHJvbGUgdHlwZVxuICAgMTU5XHRcdHZhciBhZ29yYVJvbGUgcnRjdG9rZW5idWlsZGVyMi5Sb2xlXG4gICAxNjBcdFx0aWYgcm9sZSA9PSAxIHtcbiAgIDE2MVx0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVB1Ymxpc2hlclxuICAgMTYyXHRcdH0gZWxzZSB7XG4gICAxNjNcdFx0XHRhZ29yYVJvbGUgPSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVTdWJzY3JpYmVyXG4gICAxNjRcdFx0fVxuICAgMTY1XHRcbiAgIDE2Nlx0XHQvLyBHZW5lcmF0ZSB0b2tlbiB1c2luZyBvZmZpY2lhbCBBZ29yYSBsaWJyYXJ5IHdpdGggVUlEIGRpcmVjdGx5XG4gICAxNjdcdFx0dG9rZW4sIGVyciA6PSBydGN0b2tlbmJ1aWxkZXIyLkJ1aWxkVG9rZW5XaXRoVWlkKFxuICAgMTY4XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcElELFxuICAgMTY5XHRcdFx0YXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlLFxuICAgMTcwXHRcdFx0Y2hhbm5lbE5hbWUsXG4gICAxNzFcdFx0XHR1aWQsXG4gICAxNzJcdFx0XHRhZ29yYVJvbGUsXG4gICAxNzNcdFx0XHRleHBpcmF0aW9uLFxuICAgMTc0XHRcdFx0ZXhwaXJhdGlvbiwgLy8gUHJpdmlsZWdlIGV4cGlyZWQgdGltZXN0YW1wIChzYW1lIGFzIHRva2VuIGV4cGlyYXRpb24pXG4gICAxNzVcdFx0KVxuICAgMTc2XHRcbiAgIDE3N1x0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDE3OFx0XHRcdHJldHVybiBcIlwiLCBmbXQuRXJyb3JmKFwiZmFpbGVkIHRvIGJ1aWxkIEFnb3JhIHRva2VuOiAld1wiLCBlcnIpXG4gICAxNzlcdFx0fVxuLi4uXG4gICAxOTVcdFxuICAgMTk2XHQvLyBKb2luQ2hhbm5lbCBqb2lucyBhbiBBZ29yYSBjaGFubmVsIGZvciBBSSBwcm9jZXNzaW5nXG4gICAxOTdcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIEpvaW5DaGFubmVsKGNoYW5uZWxOYW1lLCB1c2VySUQgc3RyaW5nKSBlcnJvciB7XG4gICAxOThcdFx0bG9nLlByaW50ZihcIvCflJcgW01PQ0tdIEFJIGpvaW5pbmcgQWdvcmEgY2hhbm5lbDogJXMgYXMgdXNlcjogJXNcIiwgY2hhbm5lbE5hbWUsIHVzZXJJRClcbiAgIDE5OVx0XG4gICAyMDBcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZDpcbiAgIDIwMVx0XHQvLyAxLiBDcmVhdGUgUlRDIGNvbm5lY3Rpb25cbiAgIDIwMlx0XHQvLyAyLiBTZXQgdXAgbG9jYWwgdXNlclxuICAgMjAzXHRcdC8vIDMuIENyZWF0ZSBhdWRpbyB0cmFjayBmb3IgQUkgdm9pY2VcbiAgIDIwNFx0XHQvLyA0LiBSZWdpc3RlciBhdWRpbyBmcmFtZSBvYnNlcnZlcnMgZm9yIHByb2Nlc3NpbmcgaW5jb21pbmcgYXVkaW9cbiAgIDIwNVx0XHQvLyA1LiBKb2luIHRoZSBjaGFubmVsXG4gICAyMDZcdFx0Ly8gNi4gU3RhcnQgYXVkaW8gcHJvY2Vzc2luZyBwaXBlbGluZVxuICAgMjA3XHRcbiAgIDIwOFx0XHQvLyBNb2NrIGltcGxlbWVudGF0aW9uXG4gICAyMDlcdFx0dGltZS5TbGVlcCg1MDAgKiB0aW1lLk1pbGxpc2Vjb25kKSAvLyBTaW11bGF0ZSBjb25uZWN0aW9uIHRpbWVcbiAgIDIxMFx0XG4gICAyMTFcdFx0bG9nLlByaW50ZihcIuKchSBbTU9DS10gQUkgc3VjY2Vzc2Z1bGx5IGpvaW5lZCBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgIDIxMlx0XHRyZXR1cm4gbmlsXG4gICAyMTNcdH1cbi4uLlxuUGF0aDogbW9kZWxzL2NhbGwuZ29cbi4uLlxuICAgIDI4XHRcbiAgICAyOVx0Ly8gQ2FsbCByZXByZXNlbnRzIGEgY3VzdG9tZXIgc3VwcG9ydCBjYWxsIHNlc3Npb25cbiAgICAzMFx0dHlwZSBDYWxsIHN0cnVjdCB7XG4gICAgMzFcdFx0SUQgICAgICAgICAgc3RyaW5nICAgICAgIGBqc29uOlwiaWRcImBcbiAgICAzMlx0XHRDdXN0b21lcklEICBzdHJpbmcgICAgICAgYGpzb246XCJjdXN0b21lcl9pZFwiYFxuICAgIDMzXHRcdENoYW5uZWxOYW1lIHN0cmluZyAgICAgICBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgIDM0XHRcdFN0YXR1cyAgICAgIENhbGxTdGF0dXMgICBganNvbjpcInN0YXR1c1wiYFxuICAgIDM1XHRcdFR5cGUgICAgICAgIENhbGxUeXBlICAgICBganNvbjpcInR5cGVcImBcbiAgICAzNlx0XHRTdGFydFRpbWUgICB0aW1lLlRpbWUgICAgYGpzb246XCJzdGFydF90aW1lXCJgXG4gICAgMzdcdFx0RW5kVGltZSAgICAgKnRpbWUuVGltZSAgIGBqc29uOlwiZW5kX3RpbWUsb21pdGVtcHR5XCJgXG4gICAgMzhcdFx0RHVyYXRpb24gICAgaW50NjQgICAgICAgIGBqc29uOlwiZHVyYXRpb25cImAgLy8gaW4gc2Vjb25kc1xuICAgIDM5XHRcdFRyYW5zY3JpcHQgIFtdTWVzc2FnZSAgICBganNvbjpcInRyYW5zY3JpcHRcImBcbiAgICA0MFx0XHRNZXRhZGF0YSAgICBDYWxsTWV0YWRhdGEgYGpzb246XCJtZXRhZGF0YVwiYFxuICAgIDQxXHR9XG4gICAgNDJcdFxuICAgIDQzXHQvLyBNZXNzYWdlIHJlcHJlc2VudHMgYSBzaW5nbGUgbWVzc2FnZSBpbiB0aGUgY29udmVyc2F0aW9uXG4gICAgNDRcdHR5cGUgTWVzc2FnZSBzdHJ1Y3Qge1xuICAgIDQ1XHRcdElEICAgICAgICBzdHJpbmcgICAgYGpzb246XCJpZFwiYFxuICAgIDQ2XHRcdFNwZWFrZXIgICBzdHJpbmcgICAgYGpzb246XCJzcGVha2VyXCJgIC8vIFwiYWlcIiBvciBcImN1c3RvbWVyXCJcbiAgICA0N1x0XHRDb250ZW50ICAgc3RyaW5nICAgIGBqc29uOlwiY29udGVudFwiYFxuICAgIDQ4XHRcdFRpbWVzdGFtcCB0aW1lLlRpbWUgYGpzb246XCJ0aW1lc3RhbXBcImBcbiAgICA0OVx0XHRUeXBlICAgICAgc3RyaW5nICAgIGBqc29uOlwidHlwZVwiYCAvLyBcInRleHRcIiwgXCJhdWRpb1wiXG4gICAgNTBcdH1cbiAgICA1MVx0XG4gICAgNTJcdC8vIENhbGxNZXRhZGF0YSBjb250YWlucyBhZGRpdGlvbmFsIGluZm9ybWF0aW9uIGFib3V0IHRoZSBjYWxsXG4gICAgNTNcdHR5cGUgQ2FsbE1ldGFkYXRhIHN0cnVjdCB7XG4gICAgNTRcdFx0UHVycG9zZSAgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcInB1cnBvc2VcImBcbiAgICA1NVx0XHRDdXN0b21lckluZm8gIEN1c3RvbWVySW5mbyAgICAgIGBqc29uOlwiY3VzdG9tZXJfaW5mb1wiYFxuICAgIDU2XHRcdEFJUGVyc29uYWxpdHkgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJhaV9wZXJzb25hbGl0eVwiYFxuICAgIDU3XHRcdExhbmd1YWdlICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgIDU4XHRcdFRhZ3MgICAgICAgICAgW11zdHJpbmcgICAgICAgICAgYGpzb246XCJ0YWdzXCJgXG4gICAgNTlcdFx0Q3VzdG9tRmllbGRzICBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcImN1c3RvbV9maWVsZHNcImBcbiAgICA2MFx0fVxuLi4uXG4gICAxNDBcdFxuICAgMTQxXHQvLyBOZXdDYWxsIGNyZWF0ZXMgYSBuZXcgY2FsbCBpbnN0YW5jZVxuICAgMTQyXHRmdW5jIE5ld0NhbGwoY3VzdG9tZXJJRCwgcHVycG9zZSBzdHJpbmcsIGN1c3RvbWVySW5mbyBDdXN0b21lckluZm8pICpDYWxsIHtcbiAgIDE0M1x0XHRjYWxsSUQgOj0gdXVpZC5OZXcoKS5TdHJpbmcoKVxuICAgMTQ0XHRcdGNoYW5uZWxOYW1lIDo9IFwiYWlfc3VwcG9ydF9cIiArIGNhbGxJRFxuICAgMTQ1XHRcbiAgIDE0Nlx0XHRyZXR1cm4gJkNhbGx7XG4gICAxNDdcdFx0XHRJRDogICAgICAgICAgY2FsbElELFxuICAgMTQ4XHRcdFx0Q3VzdG9tZXJJRDogIGN1c3RvbWVySUQsXG4gICAxNDlcdFx0XHRDaGFubmVsTmFtZTogY2hhbm5lbE5hbWUsXG4gICAxNTBcdFx0XHRTdGF0dXM6ICAgICAgQ2FsbFN0YXR1c1BlbmRpbmcsXG4gICAxNTFcdFx0XHRUeXBlOiAgICAgICAgQ2FsbFR5cGVBSVRvQ3VzdG9tZXIsIC8vIERlZmF1bHQgdG8gQUkgY2FsbGluZyBjdXN0b21lclxuICAgMTUyXHRcdFx0U3RhcnRUaW1lOiAgIHRpbWUuTm93KCksXG4gICAxNTNcdFx0XHRUcmFuc2NyaXB0OiAgbWFrZShbXU1lc3NhZ2UsIDApLFxuICAgMTU0XHRcdFx0TWV0YWRhdGE6IENhbGxNZXRhZGF0YXtcbiAgIDE1NVx0XHRcdFx0UHVycG9zZTogICAgICAgcHVycG9zZSxcbiAgIDE1Nlx0XHRcdFx0Q3VzdG9tZXJJbmZvOiAgY3VzdG9tZXJJbmZvLFxuICAgMTU3XHRcdFx0XHRBSVBlcnNvbmFsaXR5OiBcImhlbHBmdWxcIixcbiAgIDE1OFx0XHRcdFx0TGFuZ3VhZ2U6ICAgICAgXCJlblwiLFxuICAgMTU5XHRcdFx0XHRUYWdzOiAgICAgICAgICBtYWtlKFtdc3RyaW5nLCAwKSxcbiAgIDE2MFx0XHRcdFx0Q3VzdG9tRmllbGRzOiAgbWFrZShtYXBbc3RyaW5nXXN0cmluZyksXG4gICAxNjFcdFx0XHR9LFxuICAgMTYyXHRcdH1cbiAgIDE2M1x0fVxuICAgMTY0XHRcbiAgIDE2NVx0Ly8gTmV3Q3VzdG9tZXJDYWxsIGNyZWF0ZXMgYSBuZXcgY2FsbCBpbnN0YW5jZSB3aGVyZSBjdXN0b21lciBjYWxscyBBSVxuICAgMTY2XHRmdW5jIE5ld0N1c3RvbWVyQ2FsbCh1c2VySUQgaW50LCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTY3XHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNjhcdFx0Y2hhbm5lbE5hbWUgOj0gXCJjdXN0b21lcl9haV9cIiArIGNhbGxJRFxuICAgMTY5XHRcbiAgIDE3MFx0XHRyZXR1cm4gJkNhbGx7XG4gICAxNzFcdFx0XHRJRDogICAgICAgICAgY2FsbElELFxuICAgMTcyXHRcdFx0Q3VzdG9tZXJJRDogIGZtdC5TcHJpbnRmKFwidXNlcl8lZFwiLCB1c2VySUQpLCAvLyBDb252ZXJ0IGludCB1c2VySUQgdG8gc3RyaW5nIGZvciBpbnRlcm5hbCB1c2VcbiAgIDE3M1x0XHRcdENoYW5uZWxOYW1lOiBjaGFubmVsTmFtZSxcbiAgIDE3NFx0XHRcdFN0YXR1czogICAgICBDYWxsU3RhdHVzUGVuZGluZyxcbiAgIDE3NVx0XHRcdFR5cGU6ICAgICAgICBDYWxsVHlwZUN1c3RvbWVyVG9BSSwgLy8gQ3VzdG9tZXIgY2FsbGluZyBBSVxuICAgMTc2XHRcdFx0U3RhcnRUaW1lOiAgIHRpbWUuTm93KCksXG4gICAxNzdcdFx0XHRUcmFuc2NyaXB0OiAgbWFrZShbXU1lc3NhZ2UsIDApLFxuICAgMTc4XHRcdFx0TWV0YWRhdGE6IENhbGxNZXRhZGF0YXtcbiAgIDE3OVx0XHRcdFx0UHVycG9zZTogICAgICAgcHVycG9zZSxcbiAgIDE4MFx0XHRcdFx0Q3VzdG9tZXJJbmZvOiAgY3VzdG9tZXJJbmZvLFxuICAgMTgxXHRcdFx0XHRBSVBlcnNvbmFsaXR5OiBcImhlbHBmdWxcIixcbiAgIDE4Mlx0XHRcdFx0TGFuZ3VhZ2U6ICAgICAgXCJlblwiLFxuICAgMTgzXHRcdFx0XHRUYWdzOiAgICAgICAgICBtYWtlKFtdc3RyaW5nLCAwKSxcbiAgIDE4NFx0XHRcdFx0Q3VzdG9tRmllbGRzOiAgbWFrZShtYXBbc3RyaW5nXXN0cmluZyksXG4gICAxODVcdFx0XHR9LFxuICAgMTg2XHRcdH1cbiAgIDE4N1x0fVxuLi4uXG4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFXVHZzdGZRVFV4VWNjcmF6dFZZN0pUIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODowNzo0MS4wNzBaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgbG9vayBhdCB0aGUgbm90aWZpY2F0aW9uIHNlcnZpY2UgYW5kIGFueSBvdGhlciBBSS1yZWxhdGVkIGNvbXBvbmVudHM6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMTM5ZmMzYTEtYzQ4NC00NWI3LWI1YjYtNzFhNDJlNGMzMDA2IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxTUoyNDFDTFFueXpmSEhhOWVER0FkZSIsInRvb2xfbmFtZSI6InZpZXciLCJpbnB1dF9qc29uIjoie1wicGF0aFwiOiBcIi5cIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiJOb3cgbGV0IG1lIGNoZWNrIGlmIHRoZXJlIGFyZSBhbnkgb3RoZXIgZmlsZXMgdGhhdCBtaWdodCBiZSByZWxhdGVkIHRvIHRoZSBjdXJyZW50IHN0cnVjdHVyZTpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IHNlcnZpY2VzL2FpX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcIkJhY2tlbmQvbW9kZWxzXCJcbiAgICAgNlx0XHRcImJ5dGVzXCJcbiAgICAgN1x0XHRcImVuY29kaW5nL2pzb25cIlxuICAgICA4XHRcdFwiZm10XCJcbiAgICAgOVx0XHRcImlvXCJcbiAgICAxMFx0XHRcImxvZ1wiXG4gICAgMTFcdFx0XCJuZXQvaHR0cFwiXG4gICAgMTJcdFx0XCJzdHJpbmdzXCJcbiAgICAxM1x0XHRcInRpbWVcIlxuICAgIDE0XHQpXG4gICAgMTVcdFxuICAgIDE2XHQvLyBBSVNlcnZpY2UgaGFuZGxlcyBhbGwgQUktcmVsYXRlZCBvcGVyYXRpb25zXG4gICAgMTdcdHR5cGUgQUlTZXJ2aWNlIHN0cnVjdCB7XG4gICAgMThcdFx0Y29uZmlnICpjb25maWcuQ29uZmlnXG4gICAgMTlcdFx0Y2xpZW50ICpodHRwLkNsaWVudFxuICAgIDIwXHR9XG4gICAgMjFcdFxuICAgIDIyXHQvLyBTVFRSZXNwb25zZSByZXByZXNlbnRzIHNwZWVjaC10by10ZXh0IHJlc3BvbnNlXG4gICAgMjNcdHR5cGUgU1RUUmVzcG9uc2Ugc3RydWN0IHtcbiAgICAyNFx0XHRUZXh0ICAgICAgIHN0cmluZyAgYGpzb246XCJ0ZXh0XCJgXG4gICAgMjVcdFx0Q29uZmlkZW5jZSBmbG9hdDY0IGBqc29uOlwiY29uZmlkZW5jZVwiYFxuICAgIDI2XHRcdExhbmd1YWdlICAgc3RyaW5nICBganNvbjpcImxhbmd1YWdlXCJgXG4gICAgMjdcdH1cbiAgICAyOFx0XG4gICAgMjlcdC8vIExMTVJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgdG8gdGhlIGxhbmd1YWdlIG1vZGVsXG4gICAgMzBcdHR5cGUgTExNUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDMxXHRcdE1vZGVsICAgIHN0cmluZyAgICBganNvbjpcIm1vZGVsXCJgXG4gICAgMzJcdFx0TWVzc2FnZXMgW11MTE1NZXNzYWdlIGBqc29uOlwibWVzc2FnZXNcImBcbiAgICAzM1x0XHRUZW1wZXJhdHVyZSBmbG9hdDY0IGBqc29uOlwidGVtcGVyYXR1cmVcImBcbiAgICAzNFx0XHRNYXhUb2tlbnMgICBpbnQgICAgIGBqc29uOlwibWF4X3Rva2Vuc1wiYFxuICAgIDM1XHR9XG4gICAgMzZcdFxuICAgIDM3XHQvLyBMTE1NZXNzYWdlIHJlcHJlc2VudHMgYSBtZXNzYWdlIGluIHRoZSBjb252ZXJzYXRpb25cbiAgICAzOFx0dHlwZSBMTE1NZXNzYWdlIHN0cnVjdCB7XG4gICAgMzlcdFx0Um9sZSAgICBzdHJpbmcgYGpzb246XCJyb2xlXCJgXG4gICAgNDBcdFx0Q29udGVudCBzdHJpbmcgYGpzb246XCJjb250ZW50XCJgXG4gICAgNDFcdH1cbiAgICA0Mlx0XG4gICAgNDNcdC8vIExMTVJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIGxhbmd1YWdlIG1vZGVsIHJlc3BvbnNlXG4gICAgNDRcdHR5cGUgTExNUmVzcG9uc2Ugc3RydWN0IHtcbiAgICA0NVx0XHRDaG9pY2VzIFtdc3RydWN0IHtcbiAgICA0Nlx0XHRcdE1lc3NhZ2UgTExNTWVzc2FnZSBganNvbjpcIm1lc3NhZ2VcImBcbiAgICA0N1x0XHR9IGBqc29uOlwiY2hvaWNlc1wiYFxuICAgIDQ4XHR9XG4gICAgNDlcdFxuICAgIDUwXHQvLyBUVFNSZXF1ZXN0IHJlcHJlc2VudHMgdGV4dC10by1zcGVlY2ggcmVxdWVzdFxuICAgIDUxXHR0eXBlIFRUU1JlcXVlc3Qgc3RydWN0IHtcbiAgICA1Mlx0XHRNb2RlbCBzdHJpbmcgYGpzb246XCJtb2RlbFwiYFxuICAgIDUzXHRcdElucHV0IHN0cmluZyBganNvbjpcImlucHV0XCJgXG4gICAgNTRcdFx0Vm9pY2Ugc3RyaW5nIGBqc29uOlwidm9pY2VcImBcbiAgICA1NVx0fVxuICAgIDU2XHRcbiAgICA1N1x0Ly8gTmV3QUlTZXJ2aWNlIGNyZWF0ZXMgYSBuZXcgQUkgc2VydmljZSBpbnN0YW5jZVxuICAgIDU4XHRmdW5jIE5ld0FJU2VydmljZShjZmcgKmNvbmZpZy5Db25maWcpICpBSVNlcnZpY2Uge1xuICAgIDU5XHRcdHJldHVybiAmQUlTZXJ2aWNle1xuICAgIDYwXHRcdFx0Y29uZmlnOiBjZmcsXG4gICAgNjFcdFx0XHRjbGllbnQ6ICZodHRwLkNsaWVudHtcbiAgICA2Mlx0XHRcdFx0VGltZW91dDogMzAgKiB0aW1lLlNlY29uZCxcbiAgICA2M1x0XHRcdH0sXG4gICAgNjRcdFx0fVxuICAgIDY1XHR9XG4uLi5cbiAgICA3OFx0XHRcbiAgICA3OVx0XHQvLyBNb2NrIHJlc3BvbnNlcyBiYXNlZCBvbiBhdWRpbyBsZW5ndGggdG8gc2ltdWxhdGUgcmVhbGlzdGljIGJlaGF2aW9yXG4gICAgODBcdFx0bW9ja1Jlc3BvbnNlcyA6PSBbXXN0cmluZ3tcbiAgICA4MVx0XHRcdFwiSGVsbG8sIEkgbmVlZCBoZWxwIHdpdGggbXkgYWNjb3VudFwiLFxuICAgIDgyXHRcdFx0XCJDYW4geW91IGhlbHAgbWUgcmVzZXQgbXkgcGFzc3dvcmQ/XCIsXG4gICAgODNcdFx0XHRcIkknbSBoYXZpbmcgdHJvdWJsZSB3aXRoIG15IGJpbGxpbmdcIixcbiAgICA4NFx0XHRcdFwiV2hhdCBhcmUgeW91ciBidXNpbmVzcyBob3Vycz9cIixcbiAgICA4NVx0XHRcdFwiSSB3YW50IHRvIGNhbmNlbCBteSBzdWJzY3JpcHRpb25cIixcbiAgICA4Nlx0XHRcdFwiSG93IGRvIEkgdXBkYXRlIG15IHBheW1lbnQgbWV0aG9kP1wiLFxuICAgIDg3XHRcdFx0XCJZZXMsIHRoYXQgc291bmRzIGdvb2RcIixcbiAgICA4OFx0XHRcdFwiTm8sIHRoYXQncyBub3Qgd2hhdCBJIG1lYW50XCIsXG4gICAgODlcdFx0XHRcIkNhbiB5b3UgcmVwZWF0IHRoYXQgcGxlYXNlP1wiLFxuICAgIDkwXHRcdFx0XCJUaGFuayB5b3UgZm9yIHlvdXIgaGVscFwiLFxuICAgIDkxXHRcdH1cbiAgICA5Mlx0XHRcbiAgICA5M1x0XHQvLyBTaW1wbGUgbW9jayBzZWxlY3Rpb24gYmFzZWQgb24gYXVkaW8gZGF0YSBsZW5ndGhcbiAgICA5NFx0XHRyZXNwb25zZUluZGV4IDo9IGxlbihhdWRpb0RhdGEpICUgbGVuKG1vY2tSZXNwb25zZXMpXG4gICAgOTVcdFx0XG4gICAgOTZcdFx0cmVzcG9uc2UgOj0gJlNUVFJlc3BvbnNle1xuICAgIDk3XHRcdFx0VGV4dDogICAgICAgbW9ja1Jlc3BvbnNlc1tyZXNwb25zZUluZGV4XSxcbiAgICA5OFx0XHRcdENvbmZpZGVuY2U6IDAuODUgKyBmbG9hdDY0KGxlbihhdWRpb0RhdGEpJTE1KS8xMDAsIC8vIE1vY2sgY29uZmlkZW5jZSBiZXR3ZWVuIDAuODUtMS4wXG4gICAgOTlcdFx0XHRMYW5ndWFnZTogICBcImVuXCIsXG4gICAxMDBcdFx0fVxuICAgMTAxXHRcdFxuICAgMTAyXHRcdGxvZy5QcmludGYoXCLwn5OdIFtNT0NLXSBTVFQgUmVzdWx0OiAnJXMnIChjb25maWRlbmNlOiAlLjJmKVwiLCByZXNwb25zZS5UZXh0LCByZXNwb25zZS5Db25maWRlbmNlKVxuICAgMTAzXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAxMDRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdC8vIEdlbmVyYXRlQUlSZXNwb25zZSBnZW5lcmF0ZXMgQUkgcmVzcG9uc2UgdXNpbmcgTExNXG4gICAxMDdcdGZ1bmMgKGFpICpBSVNlcnZpY2UpIEdlbmVyYXRlQUlSZXNwb25zZShjYWxsICptb2RlbHMuQ2FsbCwgdXNlck1lc3NhZ2Ugc3RyaW5nKSAoc3RyaW5nLCBlcnJvcikge1xuICAgMTA4XHRcdGxvZy5QcmludGYoXCLwn6SWIFByb2Nlc3NpbmcgQUkgcmVzcG9uc2UgZm9yIGNhbGwgJXNcIiwgY2FsbC5JRClcbiAgIDEwOVx0XHRcbiAgIDExMFx0XHQvLyBCdWlsZCBjb252ZXJzYXRpb24gY29udGV4dFxuICAgMTExXHRcdG1lc3NhZ2VzIDo9IGFpLmJ1aWxkQ29udmVyc2F0aW9uQ29udGV4dChjYWxsLCB1c2VyTWVzc2FnZSlcbiAgIDExMlx0XHRcbiAgIDExM1x0XHQvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY2FsbCBPcGVuQUkgQVBJXG4gICAxMTRcdFx0aWYgYWkuY29uZmlnLkFJLk9wZW5BSUFQSUtleSAhPSBcIlwiIHtcbiAgIDExNVx0XHRcdHJldHVybiBhaS5jYWxsT3BlbkFJQVBJKG1lc3NhZ2VzKVxuICAgMTE2XHRcdH1cbiAgIDExN1x0XHRcbiAgIDExOFx0XHQvLyBNb2NrIEFJIHJlc3BvbnNlXG4gICAxMTlcdFx0cmV0dXJuIGFpLmdlbmVyYXRlTW9ja0FJUmVzcG9uc2UodXNlck1lc3NhZ2UsIGNhbGwuTWV0YWRhdGEuUHVycG9zZSlcbiAgIDEyMFx0fVxuLi4uXG4gICAxNDdcdFx0XG4gICAxNDhcdFx0Ly8gQWRkIGN1cnJlbnQgdXNlciBtZXNzYWdlXG4gICAxNDlcdFx0bWVzc2FnZXMgPSBhcHBlbmQobWVzc2FnZXMsIExMTU1lc3NhZ2V7XG4gICAxNTBcdFx0XHRSb2xlOiAgICBcInVzZXJcIixcbiAgIDE1MVx0XHRcdENvbnRlbnQ6IHVzZXJNZXNzYWdlLFxuICAgMTUyXHRcdH0pXG4gICAxNTNcdFx0XG4gICAxNTRcdFx0cmV0dXJuIG1lc3NhZ2VzXG4gICAxNTVcdH1cbiAgIDE1Nlx0XG4gICAxNTdcdC8vIGNhbGxPcGVuQUlBUEkgbWFrZXMgYWN0dWFsIEFQSSBjYWxsIHRvIE9wZW5BSVxuICAgMTU4XHRmdW5jIChhaSAqQUlTZXJ2aWNlKSBjYWxsT3BlbkFJQVBJKG1lc3NhZ2VzIFtdTExNTWVzc2FnZSkgKHN0cmluZywgZXJyb3IpIHtcbiAgIDE1OVx0XHRyZXF1ZXN0IDo9IExMTVJlcXVlc3R7XG4gICAxNjBcdFx0XHRNb2RlbDogICAgICAgYWkuY29uZmlnLkFJLk9wZW5BSU1vZGVsLFxuICAgMTYxXHRcdFx0TWVzc2FnZXM6ICAgIG1lc3NhZ2VzLFxuICAgMTYyXHRcdFx0VGVtcGVyYXR1cmU6IDAuNyxcbiAgIDE2M1x0XHRcdE1heFRva2VuczogICAxNTAsXG4gICAxNjRcdFx0fVxuICAgMTY1XHRcdFxuICAgMTY2XHRcdGpzb25EYXRhLCBlcnIgOj0ganNvbi5NYXJzaGFsKHJlcXVlc3QpXG4gICAxNjdcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxNjhcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byBtYXJzaGFsIHJlcXVlc3Q6ICV3XCIsIGVycilcbiAgIDE2OVx0XHR9XG4gICAxNzBcdFx0XG4gICAxNzFcdFx0cmVxLCBlcnIgOj0gaHR0cC5OZXdSZXF1ZXN0KFwiUE9TVFwiLCBcImh0dHBzOi8vYXBpLm9wZW5haS5jb20vdjEvY2hhdC9jb21wbGV0aW9uc1wiLCBieXRlcy5OZXdCdWZmZXIoanNvbkRhdGEpKVxuICAgMTcyXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTczXHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gY3JlYXRlIHJlcXVlc3Q6ICV3XCIsIGVycilcbiAgIDE3NFx0XHR9XG4uLi5cbiAgIDIxMFx0XHRcbiAgIDIxMVx0XHQvLyBDb250ZXh0LWF3YXJlIHJlc3BvbnNlcyBiYXNlZCBvbiB1c2VyIGlucHV0XG4gICAyMTJcdFx0c3dpdGNoIHtcbiAgIDIxM1x0XHRjYXNlIHN0cmluZ3MuQ29udGFpbnModXNlckxvd2VyLCBcImhlbGxvXCIpIHx8IHN0cmluZ3MuQ29udGFpbnModXNlckxvd2VyLCBcImhpXCIpOlxuICAgMjE0XHRcdFx0cmV0dXJuIGZtdC5TcHJpbnRmKFwiSGVsbG8hIEknbSBoZXJlIHRvIGhlbHAgeW91IHdpdGggJXMuIEhvdyBjYW4gSSBhc3Npc3QgeW91IHRvZGF5P1wiLCBwdXJwb3NlKSwgbmlsXG4gICAyMTVcdFx0Y2FzZSBzdHJpbmdzLkNvbnRhaW5zKHVzZXJMb3dlciwgXCJwYXNzd29yZFwiKSB8fCBzdHJpbmdzLkNvbnRhaW5zKHVzZXJMb3dlciwgXCJyZXNldFwiKTpcbiAgIDIxNlx0XHRcdHJldHVybiBcIkkgY2FuIGhlbHAgeW91IHJlc2V0IHlvdXIgcGFzc3dvcmQuIEknbGwgc2VuZCBhIHJlc2V0IGxpbmsgdG8geW91ciByZWdpc3RlcmVkIGVtYWlsIGFkZHJlc3MuIFBsZWFzZSBjaGVjayB5b3VyIGluYm94IGFuZCBmb2xsb3cgdGhlIGluc3RydWN0aW9ucy5cIiwgbmlsXG4gICAyMTdcdFx0Y2FzZSBzdHJpbmdzLkNvbnRhaW5zKHVzZXJMb3dlciwgXCJiaWxsaW5nXCIpIHx8IHN0cmluZ3MuQ29udGFpbnModXNlckxvd2VyLCBcInBheW1lbnRcIik6XG4gICAyMThcdFx0XHRyZXR1cm4gXCJJIHVuZGVyc3RhbmQgeW91IGhhdmUgYSBiaWxsaW5nIHF1ZXN0aW9uLiBMZXQgbWUgbG9vayB1cCB5b3VyIGFjY291bnQgZGV0YWlscy4gQ2FuIHlvdSBwbGVhc2UgY29uZmlybSB5b3VyIGFjY291bnQgZW1haWwgYWRkcmVzcz9cIiwgbmlsXG4uLi5cbiAgIDI1OVx0XG4gICAyNjBcdC8vIFByb2Nlc3NWQUQgcGVyZm9ybXMgVm9pY2UgQWN0aXZpdHkgRGV0ZWN0aW9uXG4gICAyNjFcdGZ1bmMgKGFpICpBSVNlcnZpY2UpIFByb2Nlc3NWQUQoYXVkaW9EYXRhIFtdYnl0ZSkgKGJvb2wsIGVycm9yKSB7XG4gICAyNjJcdFx0aWYgIWFpLmNvbmZpZy5BSS5FbmFibGVWQUQge1xuICAgMjYzXHRcdFx0cmV0dXJuIHRydWUsIG5pbCAvLyBBbHdheXMgY29uc2lkZXIgYXMgc3BlZWNoIGlmIFZBRCBpcyBkaXNhYmxlZFxuICAgMjY0XHRcdH1cbiAgIDI2NVx0XHRcbiAgIDI2Nlx0XHQvLyBNb2NrIFZBRCBwcm9jZXNzaW5nXG4gICAyNjdcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCB1c2UgQWdvcmEncyBWQUQgb3Igb3RoZXIgVkFEIGFsZ29yaXRobXNcbiAgIDI2OFx0XHRcbiAgIDI2OVx0XHQvLyBTaW1wbGUgbW9jazogY29uc2lkZXIgaXQgc3BlZWNoIGlmIGF1ZGlvIGRhdGEgaGFzIHN1ZmZpY2llbnQgXCJlbmVyZ3lcIlxuICAgMjcwXHRcdGlmIGxlbihhdWRpb0RhdGEpIDwgMTAwIHtcbiAgIDI3MVx0XHRcdHJldHVybiBmYWxzZSwgbmlsIC8vIFRvbyBzaG9ydCB0byBiZSBzcGVlY2hcbiAgIDI3Mlx0XHR9XG4gICAyNzNcdFx0XG4gICAyNzRcdFx0Ly8gTW9jayBlbmVyZ3kgY2FsY3VsYXRpb25cbiAgIDI3NVx0XHR2YXIgZW5lcmd5IGludDY0XG4gICAyNzZcdFx0Zm9yIF8sIHNhbXBsZSA6PSByYW5nZSBhdWRpb0RhdGEge1xuICAgMjc3XHRcdFx0ZW5lcmd5ICs9IGludDY0KHNhbXBsZSAqIHNhbXBsZSlcbiAgIDI3OFx0XHR9XG4gICAyNzlcdFx0XG4gICAyODBcdFx0YXZnRW5lcmd5IDo9IGZsb2F0NjQoZW5lcmd5KSAvIGZsb2F0NjQobGVuKGF1ZGlvRGF0YSkpXG4gICAyODFcdFx0dGhyZXNob2xkIDo9IDEwMDAuMCAqIGFpLmNvbmZpZy5BSS5WQURTZW5zaXRpdml0eVxuICAgMjgyXHRcdFxuICAgMjgzXHRcdGlzU3BlZWNoIDo9IGF2Z0VuZXJneSA+IHRocmVzaG9sZFxuICAgMjg0XHRcdGxvZy5QcmludGYoXCLwn46Z77iPIFtNT0NLXSBWQUQ6IEVuZXJneT0lLjJmLCBUaHJlc2hvbGQ9JS4yZiwgSXNTcGVlY2g9JXZcIiwgYXZnRW5lcmd5LCB0aHJlc2hvbGQsIGlzU3BlZWNoKVxuICAgMjg1XHRcdFxuICAgMjg2XHRcdHJldHVybiBpc1NwZWVjaCwgbmlsXG4gICAyODdcdH1cbi4uLlxuUGF0aDogc2VydmljZXMvbm90aWZpY2F0aW9uX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvbW9kZWxzXCJcbiAgICAgNVx0XHRcImZtdFwiXG4gICAgIDZcdFx0XCJsb2dcIlxuICAgICA3XHRcdFwidGltZVwiXG4gICAgIDhcdClcbiAgICAgOVx0XG4gICAgMTBcdC8vIE5vdGlmaWNhdGlvblNlcnZpY2UgaGFuZGxlcyBzZW5kaW5nIG5vdGlmaWNhdGlvbnMgdG8gdXNlcnNcbiAgICAxMVx0dHlwZSBOb3RpZmljYXRpb25TZXJ2aWNlIHN0cnVjdCB7XG4gICAgMTJcdFx0Ly8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNvbnRhaW46XG4gICAgMTNcdFx0Ly8gLSBQdXNoIG5vdGlmaWNhdGlvbiBzZXJ2aWNlIGNsaWVudHMgKEZDTSwgQVBOUylcbiAgICAxNFx0XHQvLyAtIFNNUyBzZXJ2aWNlIGNsaWVudHNcbiAgICAxNVx0XHQvLyAtIEVtYWlsIHNlcnZpY2UgY2xpZW50c1xuICAgIDE2XHRcdC8vIC0gV2ViU29ja2V0IGNvbm5lY3Rpb25zIGZvciByZWFsLXRpbWUgbm90aWZpY2F0aW9uc1xuICAgIDE3XHR9XG4uLi5cbiAgICAzM1x0XG4gICAgMzRcdC8vIFNlbmRDYWxsTm90aWZpY2F0aW9uIHNlbmRzIGEgbm90aWZpY2F0aW9uIHRvIHRoZSBjdXN0b21lciBhYm91dCBhbiBpbmNvbWluZyBBSSBjYWxsXG4gICAgMzVcdGZ1bmMgKG5zICpOb3RpZmljYXRpb25TZXJ2aWNlKSBTZW5kQ2FsbE5vdGlmaWNhdGlvbihjYWxsICptb2RlbHMuQ2FsbCkgZXJyb3Ige1xuICAgIDM2XHRcdGxvZy5QcmludGYoXCLwn5OxIFtNT0NLXSBTZW5kaW5nIGNhbGwgbm90aWZpY2F0aW9uIHRvIGN1c3RvbWVyOiAlc1wiLCBjYWxsLkN1c3RvbWVySUQpXG4gICAgMzdcdFx0XG4gICAgMzhcdFx0cGF5bG9hZCA6PSBOb3RpZmljYXRpb25QYXlsb2Fke1xuICAgIDM5XHRcdFx0VHlwZTogICAgICAgIFwiaW5jb21pbmdfYWlfY2FsbFwiLFxuICAgIDQwXHRcdFx0VGl0bGU6ICAgICAgIFwiQUkgQ3VzdG9tZXIgU3VwcG9ydCBDYWxsXCIsXG4gICAgNDFcdFx0XHRCb2R5OiAgICAgICAgZm10LlNwcmludGYoXCJZb3UgaGF2ZSBhbiBpbmNvbWluZyBBSSBzdXBwb3J0IGNhbGwgcmVnYXJkaW5nOiAlc1wiLCBjYWxsLk1ldGFkYXRhLlB1cnBvc2UpLFxuICAgIDQyXHRcdFx0Q2FsbElEOiAgICAgIGNhbGwuSUQsXG4gICAgNDNcdFx0XHRDaGFubmVsTmFtZTogY2FsbC5DaGFubmVsTmFtZSxcbiAgICA0NFx0XHRcdERhdGE6IG1hcFtzdHJpbmddaW50ZXJmYWNle317XG4gICAgNDVcdFx0XHRcdFwiY2FsbF9pZFwiOiAgICAgIGNhbGwuSUQsXG4gICAgNDZcdFx0XHRcdFwiY2hhbm5lbF9uYW1lXCI6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAgNDdcdFx0XHRcdFwicHVycG9zZVwiOiAgICAgIGNhbGwuTWV0YWRhdGEuUHVycG9zZSxcbiAgICA0OFx0XHRcdFx0XCJjdXN0b21lcl9pZFwiOiAgY2FsbC5DdXN0b21lcklELFxuICAgIDQ5XHRcdFx0XHRcInRpbWVzdGFtcFwiOiAgICB0aW1lLk5vdygpLlVuaXgoKSxcbiAgICA1MFx0XHRcdH0sXG4gICAgNTFcdFx0fVxuICAgIDUyXHRcbiAgICA1M1x0XHQvLyBNb2NrIGRpZmZlcmVudCBub3RpZmljYXRpb24gY2hhbm5lbHNcbiAgICA1NFx0XHRpZiBlcnIgOj0gbnMuc2VuZFB1c2hOb3RpZmljYXRpb24oY2FsbC5DdXN0b21lcklELCBwYXlsb2FkKTsgZXJyICE9IG5pbCB7XG4gICAgNTVcdFx0XHRsb2cuUHJpbnRmKFwiRmFpbGVkIHRvIHNlbmQgcHVzaCBub3RpZmljYXRpb246ICV2XCIsIGVycilcbiAgICA1Nlx0XHR9XG4gICAgNTdcdFxuICAgIDU4XHRcdGlmIGVyciA6PSBucy5zZW5kU01TTm90aWZpY2F0aW9uKGNhbGwuQ3VzdG9tZXJJRCwgcGF5bG9hZCk7IGVyciAhPSBuaWwge1xuICAgIDU5XHRcdFx0bG9nLlByaW50ZihcIkZhaWxlZCB0byBzZW5kIFNNUyBub3RpZmljYXRpb246ICV2XCIsIGVycilcbiAgICA2MFx0XHR9XG4gICAgNjFcdFxuICAgIDYyXHRcdGlmIGVyciA6PSBucy5zZW5kRW1haWxOb3RpZmljYXRpb24oY2FsbC5DdXN0b21lcklELCBwYXlsb2FkKTsgZXJyICE9IG5pbCB7XG4gICAgNjNcdFx0XHRsb2cuUHJpbnRmKFwiRmFpbGVkIHRvIHNlbmQgZW1haWwgbm90aWZpY2F0aW9uOiAldlwiLCBlcnIpXG4gICAgNjRcdFx0fVxuICAgIDY1XHRcbiAgICA2Nlx0XHQvLyBTaW11bGF0ZSByZWFsLXRpbWUgbm90aWZpY2F0aW9uIHZpYSBXZWJTb2NrZXRcbiAgICA2N1x0XHRpZiBlcnIgOj0gbnMuc2VuZFdlYlNvY2tldE5vdGlmaWNhdGlvbihjYWxsLkN1c3RvbWVySUQsIHBheWxvYWQpOyBlcnIgIT0gbmlsIHtcbiAgICA2OFx0XHRcdGxvZy5QcmludGYoXCJGYWlsZWQgdG8gc2VuZCBXZWJTb2NrZXQgbm90aWZpY2F0aW9uOiAldlwiLCBlcnIpXG4gICAgNjlcdFx0fVxuICAgIDcwXHRcbiAgICA3MVx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBDYWxsIG5vdGlmaWNhdGlvbiBzZW50IHN1Y2Nlc3NmdWxseSB0byBjdXN0b21lcjogJXNcIiwgY2FsbC5DdXN0b21lcklEKVxuICAgIDcyXHRcdHJldHVybiBuaWxcbiAgICA3M1x0fVxuLi4uXG5QYXRoOiBSRUFETUUubWRcbi4uLlxuICAgIDMwXHTilIIg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYIOKUgiAgICDilIIg4pSM4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSQIOKUgiAgICDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJhcbiAgICAzMVx04pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYICAgIOKUgiDilIIgTm90aWZpY2F0aW9u4pSCIOKUgiAgICDilIzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJBcbiAgICAzMlx0ICAgICAgICAgICAgICAgICAgICAgICDilIIg4pSCIFNlcnZpY2UgICAgIOKUgiDilILil4TilIDilIDilrrilIIgUHVzaC9TTVMvRW1haWwgIOKUglxuICAgIDMzXHQgICAgICAgICAgICAgICAgICAgICAgIOKUgiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJgg4pSCICAgIOKUgiBTZXJ2aWNlcyAgICAgICAg4pSCXG4gICAgMzRcdCAgICAgICAgICAgICAgICAgICAgICAg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYICAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmFxuICAgIDM1XHRgYGBcbiAgICAzNlx0XG4gICAgMzdcdCMjIPCfk4sgUHJlcmVxdWlzaXRlc1xuICAgIDM4XHRcbiAgICAzOVx0LSBHbyAxLjIyIG9yIGhpZ2hlclxuICAgIDQwXHQtIEFnb3JhLmlvIGFjY291bnQgYW5kIEFwcCBJRFxuICAgIDQxXHQtIE9wZW5BSSBBUEkga2V5IChvcHRpb25hbCwgdXNlcyBtb2NrIGJ5IGRlZmF1bHQpXG4gICAgNDJcdFxuICAgIDQzXHQjIyDwn5ug77iPIFNldHVwXG4gICAgNDRcdFxuICAgIDQ1XHQxLiAqKkNsb25lIGFuZCBzZXR1cCoqOlxuICAgIDQ2XHQgICBgYGBiYXNoXG4gICAgNDdcdCAgIGdpdCBjbG9uZSA8cmVwb3NpdG9yeT5cbiAgICA0OFx0ICAgY2QgQmFja2VuZFxuICAgIDQ5XHQgICBgYGBcbiAgICA1MFx0XG4gICAgNTFcdDIuICoqSW5zdGFsbCBkZXBlbmRlbmNpZXMqKjpcbiAgICA1Mlx0ICAgYGBgYmFzaFxuICAgIDUzXHQgICBnbyBtb2QgdGlkeVxuICAgIDU0XHQgICBgYGBcbiAgICA1NVx0XG4gICAgNTZcdDMuICoqQ29uZmlndXJlIGVudmlyb25tZW50Kio6XG4gICAgNTdcdCAgIGBgYGJhc2hcbiAgICA1OFx0ICAgY3AgLmVudiAuZW52XG4gICAgNTlcdCAgICMgRWRpdCAuZW52IHdpdGggeW91ciBjb25maWd1cmF0aW9uXG4gICAgNjBcdCAgIGBgYFxuICAgIDYxXHRcbiAgICA2Mlx0NC4gKipSZXF1aXJlZCBlbnZpcm9ubWVudCB2YXJpYWJsZXMqKjpcbiAgICA2M1x0ICAgYGBgZW52XG4gICAgNjRcdCAgIEFHT1JBX0FQUF9JRD15b3VyX2Fnb3JhX2FwcF9pZF9oZXJlXG4gICAgNjVcdCAgIEFHT1JBX0FQUF9DRVJUSUZJQ0FURT15b3VyX2Fnb3JhX2FwcF9jZXJ0aWZpY2F0ZV9oZXJlXG4gICAgNjZcdCAgIE9QRU5BSV9BUElfS0VZPXlvdXJfb3BlbmFpX2FwaV9rZXlfaGVyZSAgIyBPcHRpb25hbFxuICAgIDY3XHQgICBgYGBcbiAgICA2OFx0XG4gICAgNjlcdDUuICoqUnVuIHRoZSBzZXJ2ZXIqKjpcbiAgICA3MFx0ICAgYGBgYmFzaFxuICAgIDcxXHQgICBnbyBydW4gbWFpbi5nb1xuICAgIDcyXHQgICBgYGBcbiAgICA3M1x0XG4gICAgNzRcdCMjIPCflIwgQVBJIEVuZHBvaW50c1xuICAgIDc1XHRcbiAgICA3Nlx0IyMjIENvcmUgRW5kcG9pbnRzXG4gICAgNzdcdFxuICAgIDc4XHR8IE1ldGhvZCB8IEVuZHBvaW50IHwgRGVzY3JpcHRpb24gfFxuICAgIDc5XHR8LS0tLS0tLS18LS0tLS0tLS0tLXwtLS0tLS0tLS0tLS0tfFxuICAgIDgwXHR8IGBHRVRgIHwgYC9gIHwgV2VsY29tZSBtZXNzYWdlIGFuZCBBUEkgb3ZlcnZpZXcgfFxuICAgIDgxXHR8IGBHRVRgIHwgYC9oZWFsdGhgIHwgSGVhbHRoIGNoZWNrIGFuZCBzeXN0ZW0gc3RhdHVzIHxcbiAgICA4Mlx0XG4gICAgODNcdCMjIyBDYWxsIE1hbmFnZW1lbnRcbi4uLlxuICAgMjM0XHRcbiAgIDIzNVx0IyMg8J+UpyBDb25maWd1cmF0aW9uXG4gICAyMzZcdFxuICAgMjM3XHQjIyMgU2VydmVyIFNldHRpbmdzXG4gICAyMzhcdGBgYGVudlxuICAgMjM5XHRTRVJWRVJfUE9SVD04MDgwXG4gICAyNDBcdFNFUlZFUl9IT1NUPTAuMC4wLjBcbiAgIDI0MVx0YGBgXG4gICAyNDJcdFxuICAgMjQzXHQjIyMgQWdvcmEgU2V0dGluZ3NcbiAgIDI0NFx0YGBgZW52XG4gICAyNDVcdEFHT1JBX0FQUF9JRD15b3VyX2FwcF9pZFxuICAgMjQ2XHRBR09SQV9BUFBfQ0VSVElGSUNBVEU9eW91cl9jZXJ0aWZpY2F0ZVxuICAgMjQ3XHRBR09SQV9DSEFOTkVMX1BSRUZJWD1haV9zdXBwb3J0X1xuICAgMjQ4XHRgYGBcbiAgIDI0OVx0XG4gICAyNTBcdCMjIyBBSSBTZXR0aW5nc1xuICAgMjUxXHRgYGBlbnZcbiAgIDI1Mlx0T1BFTkFJX0FQSV9LRVk9eW91cl9rZXlcbiAgIDI1M1x0T1BFTkFJX01PREVMPWdwdC00XG4gICAyNTRcdFNUVF9QUk9WSURFUj1vcGVuYWlcbiAgIDI1NVx0VFRTX1BST1ZJREVSPW9wZW5haVxuICAgMjU2XHRUVFNfVk9JQ0VfSUQ9YWxsb3lcbiAgIDI1N1x0RU5BQkxFX1ZBRD10cnVlXG4gICAyNThcdFZBRF9TRU5TSVRJVklUWT0wLjdcbiAgIDI1OVx0YGBgXG4gICAyNjBcdFxuICAgMjYxXHQjIyDwn6eqIERldmVsb3BtZW50XG4gICAyNjJcdFxuICAgMjYzXHQjIyMgTW9jayBTZXJ2aWNlc1xuICAgMjY0XHRcbiAgIDI2NVx0VGhlIGFwcGxpY2F0aW9uIGluY2x1ZGVzIG1vY2sgaW1wbGVtZW50YXRpb25zIGZvcjpcbiAgIDI2Nlx0LSAqKk5vdGlmaWNhdGlvbiBTZXJ2aWNlKio6IFNpbXVsYXRlcyBwdXNoL1NNUy9lbWFpbCBub3RpZmljYXRpb25zXG4gICAyNjdcdC0gKipBSSBTZXJ2aWNlKio6IE1vY2sgU1RULCBMTE0sIGFuZCBUVFMgcmVzcG9uc2VzXG4gICAyNjhcdC0gKipBZ29yYSBTZXJ2aWNlKio6IE1vY2sgdG9rZW4gZ2VuZXJhdGlvbiBhbmQgY2hhbm5lbCBtYW5hZ2VtZW50XG4gICAyNjlcdFxuICAgMjcwXHQjIyMgVGVzdGluZ1xuICAgMjcxXHRcbiAgIDI3Mlx0YGBgYmFzaFxuICAgMjczXHQjIFRlc3QgaGVhbHRoIGVuZHBvaW50XG4gICAyNzRcdGN1cmwgaHR0cDovL2xvY2FsaG9zdDo4MDgwL2hlYWx0aFxuLi4uXG5QYXRoOiBtYWluLmdvXG4uLi5cbiAgICAxN1x0XG4gICAgMThcdGZ1bmMgbWFpbigpIHtcbiAgICAxOVx0XHQvLyBMb2FkIGNvbmZpZ3VyYXRpb25cbiAgICAyMFx0XHRjb25maWcuTG9hZENvbmZpZygpXG4gICAgMjFcdFx0Y2ZnIDo9IGNvbmZpZy5BcHBDb25maWdcbiAgICAyMlx0XG4gICAgMjNcdFx0bG9nLlByaW50ZihcIvCfmoAgU3RhcnRpbmcgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXCIpXG4gICAgMjRcdFx0bG9nLlByaW50ZihcIvCfk4sgU2VydmVyOiAlczolc1wiLCBjZmcuU2VydmVyLkhvc3QsIGNmZy5TZXJ2ZXIuUG9ydClcbiAgICAyNVx0XHRsb2cuUHJpbnRmKFwi8J+OryBBZ29yYSBBcHAgSUQ6ICVzXCIsIGNmZy5BZ29yYS5BcHBJRClcbiAgICAyNlx0XG4gICAgMjdcdFx0Ly8gSW5pdGlhbGl6ZSBzZXJ2aWNlc1xuICAgIDI4XHRcdG5vdGlmaWNhdGlvblNlcnZpY2UgOj0gc2VydmljZXMuTmV3Tm90aWZpY2F0aW9uU2VydmljZSgpXG4gICAgMjlcdFx0YWdvcmFTZXJ2aWNlIDo9IHNlcnZpY2VzLk5ld0Fnb3JhU2VydmljZShjZmcpXG4gICAgMzBcdFx0YWlTZXJ2aWNlIDo9IHNlcnZpY2VzLk5ld0FJU2VydmljZShjZmcpXG4gICAgMzFcdFx0Y2FsbE1hbmFnZXIgOj0gc2VydmljZXMuTmV3Q2FsbE1hbmFnZXIobm90aWZpY2F0aW9uU2VydmljZSwgYWlTZXJ2aWNlLCBhZ29yYVNlcnZpY2UpXG4gICAgMzJcdFxuICAgIDMzXHRcdC8vIEluaXRpYWxpemUgaGFuZGxlcnNcbiAgICAzNFx0XHRjYWxsSGFuZGxlciA6PSBoYW5kbGVycy5OZXdDYWxsSGFuZGxlcihjYWxsTWFuYWdlciwgYWdvcmFTZXJ2aWNlKVxuICAgIDM1XHRcbiAgICAzNlx0XHQvLyBTZXR1cCBHaW4gcm91dGVyXG4gICAgMzdcdFx0cm91dGVyIDo9IHNldHVwUm91dGVyKGNhbGxIYW5kbGVyKVxuICAgIDM4XHRcbiAgICAzOVx0XHQvLyBTZXR1cCBncmFjZWZ1bCBzaHV0ZG93blxuICAgIDQwXHRcdHNldHVwR3JhY2VmdWxTaHV0ZG93bihhZ29yYVNlcnZpY2UpXG4uLi5cblBhdGg6IGNvbmZpZy9jb25maWcuZ29cbiAgICAgMVx0cGFja2FnZSBjb25maWdcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJsb2dcIlxuICAgICA1XHRcdFwib3NcIlxuICAgICA2XHRcdFwic3RyY29udlwiXG4gICAgIDdcdFxuICAgICA4XHRcdFwiZ2l0aHViLmNvbS9qb2hvL2dvZG90ZW52XCJcbiAgICAgOVx0KVxuICAgIDEwXHRcbiAgICAxMVx0dHlwZSBDb25maWcgc3RydWN0IHtcbiAgICAxMlx0XHRTZXJ2ZXIgU2VydmVyQ29uZmlnXG4gICAgMTNcdFx0QWdvcmEgIEFnb3JhQ29uZmlnXG4gICAgMTRcdFx0QUkgICAgIEFJQ29uZmlnXG4gICAgMTVcdH1cbiAgICAxNlx0XG4gICAgMTdcdHR5cGUgU2VydmVyQ29uZmlnIHN0cnVjdCB7XG4gICAgMThcdFx0UG9ydCBzdHJpbmdcbiAgICAxOVx0XHRIb3N0IHN0cmluZ1xuICAgIDIwXHR9XG4gICAgMjFcdFxuICAgIDIyXHR0eXBlIEFnb3JhQ29uZmlnIHN0cnVjdCB7XG4gICAgMjNcdFx0QXBwSUQgICAgICAgICAgc3RyaW5nXG4gICAgMjRcdFx0QXBwQ2VydGlmaWNhdGUgc3RyaW5nXG4gICAgMjVcdFx0Q2hhbm5lbFByZWZpeCAgc3RyaW5nXG4gICAgMjZcdH1cbiAgICAyN1x0XG4gICAgMjhcdHR5cGUgQUlDb25maWcgc3RydWN0IHtcbiAgICAyOVx0XHRPcGVuQUlBUElLZXkgICAgc3RyaW5nXG4gICAgMzBcdFx0T3BlbkFJTW9kZWwgICAgIHN0cmluZ1xuICAgIDMxXHRcdFNUVFByb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzMlx0XHRUVFNQcm92aWRlciAgICAgc3RyaW5nXG4gICAgMzNcdFx0Vm9pY2VJRCAgICAgICAgIHN0cmluZ1xuICAgIDM0XHRcdEVuYWJsZVZBRCAgICAgICBib29sXG4gICAgMzVcdFx0VkFEU2Vuc2l0aXZpdHkgIGZsb2F0NjRcbiAgICAzNlx0fVxuICAgIDM3XHRcbiAgICAzOFx0dmFyIEFwcENvbmZpZyAqQ29uZmlnXG4gICAgMzlcdFxuICAgIDQwXHRmdW5jIExvYWRDb25maWcoKSB7XG4gICAgNDFcdFx0Ly8gTG9hZCAuZW52IGZpbGUgaWYgaXQgZXhpc3RzXG4gICAgNDJcdFx0aWYgZXJyIDo9IGdvZG90ZW52LkxvYWQoKTsgZXJyICE9IG5pbCB7XG4gICAgNDNcdFx0XHRsb2cuUHJpbnRsbihcIk5vIC5lbnYgZmlsZSBmb3VuZCwgdXNpbmcgZW52aXJvbm1lbnQgdmFyaWFibGVzXCIpXG4gICAgNDRcdFx0fVxuICAgIDQ1XHRcbiAgICA0Nlx0XHRBcHBDb25maWcgPSAmQ29uZmlne1xuICAgIDQ3XHRcdFx0U2VydmVyOiBTZXJ2ZXJDb25maWd7XG4gICAgNDhcdFx0XHRcdFBvcnQ6IGdldEVudihcIlNFUlZFUl9QT1JUXCIsIFwiODA4MFwiKSxcbiAgICA0OVx0XHRcdFx0SG9zdDogZ2V0RW52KFwiU0VSVkVSX0hPU1RcIiwgXCIwLjAuMC4wXCIpLFxuICAgIDUwXHRcdFx0fSxcbiAgICA1MVx0XHRcdEFnb3JhOiBBZ29yYUNvbmZpZ3tcbiAgICA1Mlx0XHRcdFx0QXBwSUQ6ICAgICAgICAgIGdldEVudihcIkFHT1JBX0FQUF9JRFwiLCBcIlwiKSxcbiAgICA1M1x0XHRcdFx0QXBwQ2VydGlmaWNhdGU6IGdldEVudihcIkFHT1JBX0FQUF9DRVJUSUZJQ0FURVwiLCBcIlwiKSxcbiAgICA1NFx0XHRcdFx0Q2hhbm5lbFByZWZpeDogIGdldEVudihcIkFHT1JBX0NIQU5ORUxfUFJFRklYXCIsIFwiYWlfc3VwcG9ydF9cIiksXG4gICAgNTVcdFx0XHR9LFxuICAgIDU2XHRcdFx0QUk6IEFJQ29uZmlne1xuICAgIDU3XHRcdFx0XHRPcGVuQUlBUElLZXk6ICAgIGdldEVudihcIk9QRU5BSV9BUElfS0VZXCIsIFwiXCIpLFxuICAgIDU4XHRcdFx0XHRPcGVuQUlNb2RlbDogICAgIGdldEVudihcIk9QRU5BSV9NT0RFTFwiLCBcImdwdC00XCIpLFxuICAgIDU5XHRcdFx0XHRTVFRQcm92aWRlcjogICAgIGdldEVudihcIlNUVF9QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MFx0XHRcdFx0VFRTUHJvdmlkZXI6ICAgICBnZXRFbnYoXCJUVFNfUFJPVklERVJcIiwgXCJvcGVuYWlcIiksXG4gICAgNjFcdFx0XHRcdFZvaWNlSUQ6ICAgICAgICAgZ2V0RW52KFwiVFRTX1ZPSUNFX0lEXCIsIFwiYWxsb3lcIiksXG4gICAgNjJcdFx0XHRcdEVuYWJsZVZBRDogICAgICAgZ2V0RW52Qm9vbChcIkVOQUJMRV9WQURcIiwgdHJ1ZSksXG4gICAgNjNcdFx0XHRcdFZBRFNlbnNpdGl2aXR5OiAgZ2V0RW52RmxvYXQoXCJWQURfU0VOU0lUSVZJVFlcIiwgMC43KSxcbiAgICA2NFx0XHRcdH0sXG4gICAgNjVcdFx0fVxuLi4uXG5QYXRoOiBtb2RlbHMvY2FsbC5nb1xuLi4uXG4gICAgNDJcdFxuICAgIDQzXHQvLyBNZXNzYWdlIHJlcHJlc2VudHMgYSBzaW5nbGUgbWVzc2FnZSBpbiB0aGUgY29udmVyc2F0aW9uXG4gICAgNDRcdHR5cGUgTWVzc2FnZSBzdHJ1Y3Qge1xuICAgIDQ1XHRcdElEICAgICAgICBzdHJpbmcgICAgYGpzb246XCJpZFwiYFxuICAgIDQ2XHRcdFNwZWFrZXIgICBzdHJpbmcgICAgYGpzb246XCJzcGVha2VyXCJgIC8vIFwiYWlcIiBvciBcImN1c3RvbWVyXCJcbiAgICA0N1x0XHRDb250ZW50ICAgc3RyaW5nICAgIGBqc29uOlwiY29udGVudFwiYFxuICAgIDQ4XHRcdFRpbWVzdGFtcCB0aW1lLlRpbWUgYGpzb246XCJ0aW1lc3RhbXBcImBcbiAgICA0OVx0XHRUeXBlICAgICAgc3RyaW5nICAgIGBqc29uOlwidHlwZVwiYCAvLyBcInRleHRcIiwgXCJhdWRpb1wiXG4gICAgNTBcdH1cbiAgICA1MVx0XG4gICAgNTJcdC8vIENhbGxNZXRhZGF0YSBjb250YWlucyBhZGRpdGlvbmFsIGluZm9ybWF0aW9uIGFib3V0IHRoZSBjYWxsXG4gICAgNTNcdHR5cGUgQ2FsbE1ldGFkYXRhIHN0cnVjdCB7XG4gICAgNTRcdFx0UHVycG9zZSAgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcInB1cnBvc2VcImBcbiAgICA1NVx0XHRDdXN0b21lckluZm8gIEN1c3RvbWVySW5mbyAgICAgIGBqc29uOlwiY3VzdG9tZXJfaW5mb1wiYFxuICAgIDU2XHRcdEFJUGVyc29uYWxpdHkgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJhaV9wZXJzb25hbGl0eVwiYFxuICAgIDU3XHRcdExhbmd1YWdlICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgIDU4XHRcdFRhZ3MgICAgICAgICAgW11zdHJpbmcgICAgICAgICAgYGpzb246XCJ0YWdzXCJgXG4gICAgNTlcdFx0Q3VzdG9tRmllbGRzICBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcImN1c3RvbV9maWVsZHNcImBcbiAgICA2MFx0fVxuICAgIDYxXHRcbiAgICA2Mlx0Ly8gQ3VzdG9tZXJJbmZvIGNvbnRhaW5zIGN1c3RvbWVyIGRldGFpbHNcbiAgICA2M1x0dHlwZSBDdXN0b21lckluZm8gc3RydWN0IHtcbiAgICA2NFx0XHROYW1lICAgICAgICBzdHJpbmcgYGpzb246XCJuYW1lXCJgXG4gICAgNjVcdFx0RW1haWwgICAgICAgc3RyaW5nIGBqc29uOlwiZW1haWxcImBcbiAgICA2Nlx0XHRQaG9uZSAgICAgICBzdHJpbmcgYGpzb246XCJwaG9uZVwiYFxuICAgIDY3XHRcdEFjY291bnRJRCAgIHN0cmluZyBganNvbjpcImFjY291bnRfaWRcImBcbiAgICA2OFx0XHRQcmVmZXJlbmNlcyBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcInByZWZlcmVuY2VzXCJgXG4gICAgNjlcdH1cbiAgICA3MFx0XG4gICAgNzFcdC8vIEluaXRpYXRlQ2FsbFJlcXVlc3QgcmVwcmVzZW50cyB0aGUgcmVxdWVzdCB0byBzdGFydCBhIG5ldyBjYWxsXG4gICAgNzJcdHR5cGUgSW5pdGlhdGVDYWxsUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDczXHRcdEN1c3RvbWVySUQgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJjdXN0b21lcl9pZFwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgIDc0XHRcdFB1cnBvc2UgICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJwdXJwb3NlXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAgNzVcdFx0Q3VzdG9tZXJJbmZvICBDdXN0b21lckluZm8gICAgICBganNvbjpcImN1c3RvbWVyX2luZm9cImBcbiAgICA3Nlx0XHRBSVBlcnNvbmFsaXR5IHN0cmluZyAgICAgICAgICAgIGBqc29uOlwiYWlfcGVyc29uYWxpdHlcImBcbiAgICA3N1x0XHRMYW5ndWFnZSAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwibGFuZ3VhZ2VcImBcbiAgICA3OFx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgIDc5XHR9XG4uLi5cbiAgIDEwMlx0XG4gICAxMDNcdC8vIEluaXRpYXRlQ3VzdG9tZXJDYWxsUmVxdWVzdCByZXByZXNlbnRzIHRoZSByZXF1ZXN0IHdoZW4gY3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwgdG8gQUlcbiAgIDEwNFx0dHlwZSBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlcXVlc3Qgc3RydWN0IHtcbiAgIDEwNVx0XHRVc2VySUQgICAgICAgIGludCAgICAgICAgICAgICAgIGBqc29uOlwidXNlcl9pZFwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgMTA2XHRcdFB1cnBvc2UgICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJwdXJwb3NlXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAxMDdcdFx0Q3VzdG9tZXJJbmZvICBDdXN0b21lckluZm8gICAgICBganNvbjpcImN1c3RvbWVyX2luZm9cImBcbiAgIDEwOFx0XHRBSVBlcnNvbmFsaXR5IHN0cmluZyAgICAgICAgICAgIGBqc29uOlwiYWlfcGVyc29uYWxpdHlcImBcbiAgIDEwOVx0XHRMYW5ndWFnZSAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwibGFuZ3VhZ2VcImBcbiAgIDExMFx0XHRTY2VuYXJpbyAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwic2NlbmFyaW9cImAgICAgICAvLyBlLmcuLCBcImJpbGxpbmdcIiwgXCJ0ZWNobmljYWxcIiwgXCJnZW5lcmFsXCJcbiAgIDExMVx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgMTEyXHR9XG4gICAxMTNcdFxuICAgMTE0XHQvLyBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIHJlc3BvbnNlIHdoZW4gY3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwgdG8gQUlcbiAgIDExNVx0dHlwZSBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNlIHN0cnVjdCB7XG4gICAxMTZcdFx0Q2FsbElEICAgICAgc3RyaW5nIGBqc29uOlwiY2FsbF9pZFwiYFxuICAgMTE3XHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgMTE4XHRcdFRva2VuICAgICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAxMTlcdFx0VXNlcklEICAgICAgaW50ICAgIGBqc29uOlwidXNlcl9pZFwiYFxuICAgMTIwXHRcdFN0YXR1cyAgICAgIHN0cmluZyBganNvbjpcInN0YXR1c1wiYFxuICAgMTIxXHRcdE1lc3NhZ2UgICAgIHN0cmluZyBganNvbjpcIm1lc3NhZ2VcImBcbiAgIDEyMlx0XHRBSUluZm8gICAgICBBSUluZm8gYGpzb246XCJhaV9pbmZvXCJgXG4gICAxMjNcdH1cbiAgIDEyNFx0XG4gICAxMjVcdC8vIEFJSW5mbyBjb250YWlucyBpbmZvcm1hdGlvbiBhYm91dCB0aGUgQUkgYWdlbnRcbiAgIDEyNlx0dHlwZSBBSUluZm8gc3RydWN0IHtcbiAgIDEyN1x0XHROYW1lICAgICAgICBzdHJpbmcgYGpzb246XCJuYW1lXCJgXG4gICAxMjhcdFx0UGVyc29uYWxpdHkgc3RyaW5nIGBqc29uOlwicGVyc29uYWxpdHlcImBcbiAgIDEyOVx0XHRMYW5ndWFnZSAgICBzdHJpbmcgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgMTMwXHRcdENhcGFiaWxpdGllcyBbXXN0cmluZyBganNvbjpcImNhcGFiaWxpdGllc1wiYFxuICAgMTMxXHR9XG4uLi5cbiAgIDE0MFx0XG4gICAxNDFcdC8vIE5ld0NhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlXG4gICAxNDJcdGZ1bmMgTmV3Q2FsbChjdXN0b21lcklELCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTQzXHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNDRcdFx0Y2hhbm5lbE5hbWUgOj0gXCJhaV9zdXBwb3J0X1wiICsgY2FsbElEXG4gICAxNDVcdFxuICAgMTQ2XHRcdHJldHVybiAmQ2FsbHtcbiAgIDE0N1x0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNDhcdFx0XHRDdXN0b21lcklEOiAgY3VzdG9tZXJJRCxcbiAgIDE0OVx0XHRcdENoYW5uZWxOYW1lOiBjaGFubmVsTmFtZSxcbiAgIDE1MFx0XHRcdFN0YXR1czogICAgICBDYWxsU3RhdHVzUGVuZGluZyxcbiAgIDE1MVx0XHRcdFR5cGU6ICAgICAgICBDYWxsVHlwZUFJVG9DdXN0b21lciwgLy8gRGVmYXVsdCB0byBBSSBjYWxsaW5nIGN1c3RvbWVyXG4gICAxNTJcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE1M1x0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNTRcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTU1XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTU2XHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxNTdcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTU4XHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxNTlcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTYwXHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE2MVx0XHRcdH0sXG4gICAxNjJcdFx0fVxuICAgMTYzXHR9XG4gICAxNjRcdFxuICAgMTY1XHQvLyBOZXdDdXN0b21lckNhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlIHdoZXJlIGN1c3RvbWVyIGNhbGxzIEFJXG4gICAxNjZcdGZ1bmMgTmV3Q3VzdG9tZXJDYWxsKHVzZXJJRCBpbnQsIHB1cnBvc2Ugc3RyaW5nLCBjdXN0b21lckluZm8gQ3VzdG9tZXJJbmZvKSAqQ2FsbCB7XG4gICAxNjdcdFx0Y2FsbElEIDo9IHV1aWQuTmV3KCkuU3RyaW5nKClcbiAgIDE2OFx0XHRjaGFubmVsTmFtZSA6PSBcImN1c3RvbWVyX2FpX1wiICsgY2FsbElEXG4gICAxNjlcdFxuICAgMTcwXHRcdHJldHVybiAmQ2FsbHtcbiAgIDE3MVx0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNzJcdFx0XHRDdXN0b21lcklEOiAgZm10LlNwcmludGYoXCJ1c2VyXyVkXCIsIHVzZXJJRCksIC8vIENvbnZlcnQgaW50IHVzZXJJRCB0byBzdHJpbmcgZm9yIGludGVybmFsIHVzZVxuICAgMTczXHRcdFx0Q2hhbm5lbE5hbWU6IGNoYW5uZWxOYW1lLFxuICAgMTc0XHRcdFx0U3RhdHVzOiAgICAgIENhbGxTdGF0dXNQZW5kaW5nLFxuICAgMTc1XHRcdFx0VHlwZTogICAgICAgIENhbGxUeXBlQ3VzdG9tZXJUb0FJLCAvLyBDdXN0b21lciBjYWxsaW5nIEFJXG4gICAxNzZcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE3N1x0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNzhcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTc5XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTgwXHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxODFcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTgyXHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxODNcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTg0XHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE4NVx0XHRcdH0sXG4gICAxODZcdFx0fVxuICAgMTg3XHR9XG4uLi5cblBhdGg6IC5lbnZcbiAgICAgMVx0IyBTZXJ2ZXIgQ29uZmlndXJhdGlvblxuICAgICAyXHRTRVJWRVJfUE9SVD04MDgwXG4gICAgIDNcdFNFUlZFUl9IT1NUPTAuMC4wLjBcbiAgICAgNFx0XG4gICAgIDVcdCMgQWdvcmEgQ29uZmlndXJhdGlvblxuICAgICA2XHRBR09SQV9BUFBfSUQ9MDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcbiAgICAgN1x0QUdPUkFfQVBQX0NFUlRJRklDQVRFPWU3ZjM3ZTY3ZDgyYjRhZmY4NDYzMTc3NGMxYjVkZTYxXG4gICAgIDhcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAgIDlcdFxuICAgIDEwXHQjIEFJIENvbmZpZ3VyYXRpb25cbiAgICAxMVx0T1BFTkFJX0FQSV9LRVk9ay1wcm9qLXp4M1RXWFItTEtjTVBUbTJMd3EwdzJnb3B1elZPZjR0dWlneFFINWlwaU95UkNLOXpGT09nRU1Cam1xZjBBN1FWZEMxSEc3MWtNVDNCbGJrRkoyTGNnaUJBdXBsNVU5YkI1ckVUeVBNUmxFVl8xR1ota1pzV3MxSmNUcEJZUy1saGthRURETUF2RXFmbnRZUUFVZExLZW5QMzZVQVxuICAgIDEyXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgICAxM1x0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgIDE0XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAgMTVcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgIDE2XHRFTkFCTEVfVkFEPXRydWVcbiAgICAxN1x0VkFEX1NFTlNJVElWSVRZPTAuN1xuLi4uXG4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFZUExESGpkZnBTaW5uSDNWWnZhbVhwIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODowODoxMS44MzNaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBsZXQgbWUgY2hlY2sgaWYgdGhlcmUgYXJlIGFueSBvdGhlciBmaWxlcyB0aGF0IG1pZ2h0IGJlIHJlbGF0ZWQgdG8gdGhlIGN1cnJlbnQgc3RydWN0dXJlOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6ImFmNjJiMTM4LTg2YWYtNDViYi1iM2JiLTg2MTQ5NWI3NDk4NCIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTJOQ25oM05LZlhlZFBoUzczRnYxWkQiLCJ0b29sX25hbWUiOiJsYXVuY2gtcHJvY2VzcyIsImlucHV0X2pzb24iOiJ7XCJjb21tYW5kXCI6IFwiZmluZCAuIC1uYW1lIFxcXCIqLmdvXFxcIiAtbyAtbmFtZSBcXFwiKi5tZFxcXCIgLW8gLW5hbWUgXFxcIiouZW52XFxcIiB8IGhlYWQgLTIwXCIsIFwid2FpdFwiOiB0cnVlLCBcIm1heF93YWl0X3NlY29uZHNcIjogMTB9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiTGV0IG1lIGNoZWNrIHRoZSBjdXJyZW50IHdvcmtpbmcgZGlyZWN0b3J5IGFuZCBzZWUgdGhlIGZpbGUgc3RydWN0dXJlOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiSGVyZSdzIHRoZSBmaWxlcyBhbmQgZGlyZWN0b3JpZXMgdXAgdG8gMiBsZXZlbHMgZGVlcCBpbiAuLCBleGNsdWRpbmcgaGlkZGVuIGl0ZW1zOlxuKGVtcHR5IGRpcmVjdG9yeSlcbiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMU1KMjQxQ0xRbnl6ZkhIYTllREdBZGUifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIiwiZm9sZGVyUm9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlfX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA4OjA4OjI3LjE1MloiLCJyZXNwb25zZV90ZXh0IjoiTGV0IG1lIGNoZWNrIHRoZSBjdXJyZW50IHdvcmtpbmcgZGlyZWN0b3J5IGFuZCBzZWUgdGhlIGZpbGUgc3RydWN0dXJlOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6ImU0OTRhODAzLTVhYzQtNGVjNy05MjRkLTBmYTJlZmMxMzY4YSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUxoU2thalk1cWdLcUFNWFd4QWNDZTIiLCJ0b29sX25hbWUiOiJzdHItcmVwbGFjZS1lZGl0b3IiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcInN0cl9yZXBsYWNlXCIsIFwicGF0aFwiOiBcIkJhY2tlbmQvY29uZmlnL2NvbmZpZy5nb1wiLCBcImluc3RydWN0aW9uX3JlbWluZGVyXCI6IFwiQUxXQVlTIEJSRUFLIERPV04gRURJVFMgSU5UTyBTTUFMTEVSIENIVU5LUyBPRiBBVCBNT1NUIDIwMCBMSU5FUyBFQUNILlwiLCBcIm9sZF9zdHJfMVwiOiBcInR5cGUgQ29uZmlnIHN0cnVjdCB7XFxuXFx0U2VydmVyIFNlcnZlckNvbmZpZ1xcblxcdEFnb3JhICBBZ29yYUNvbmZpZ1xcblxcdEFJICAgICBBSUNvbmZpZ1xcbn1cIiwgXCJuZXdfc3RyXzFcIjogXCJ0eXBlIENvbmZpZyBzdHJ1Y3Qge1xcblxcdFNlcnZlciBTZXJ2ZXJDb25maWdcXG5cXHRBZ29yYSAgQWdvcmFDb25maWdcXG59XCIsIFwib2xkX3N0cl9zdGFydF9saW5lX251bWJlcl8xXCI6IDExLCBcIm9sZF9zdHJfZW5kX2xpbmVfbnVtYmVyXzFcIjogMTV9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiTm93IEkgaGF2ZSBhIGNsZWFyIHVuZGVyc3RhbmRpbmcgb2YgdGhlIGN1cnJlbnQgYmFja2VuZCBzdHJ1Y3R1cmUuIExldCBtZSBjcmVhdGUgYSBkZXRhaWxlZCBwbGFuIHRvIHJlZmFjdG9yIHRoZSBiYWNrZW5kIHRvIHJlbW92ZSBBSSBmZWF0dXJlcyBhbmQgaW1wbGVtZW50IHVzZXItdG8tdXNlciBjYWxsaW5nIGZ1bmN0aW9uYWxpdHkuXG5cbiMjIEFuYWx5c2lzIGFuZCBQbGFuXG5cbkJhc2VkIG9uIG15IGFuYWx5c2lzLCB0aGUgY3VycmVudCBiYWNrZW5kIGlzIGhlYXZpbHkgZm9jdXNlZCBvbiBBSSBjdXN0b21lciBzdXBwb3J0IGNhbGxzLiBIZXJlJ3Mgd2hhdCBuZWVkcyB0byBiZSBjaGFuZ2VkOlxuXG4jIyMgQ3VycmVudCBBSS1yZWxhdGVkIGNvbXBvbmVudHMgdG8gcmVtb3ZlL21vZGlmeTpcbjEuICoqQUkgU2VydmljZSoqIChgc2VydmljZXMvYWlfc2VydmljZS5nb2ApIC0gRW50aXJlIGZpbGUgY2FuIGJlIHJlbW92ZWRcbjIuICoqQUktcmVsYXRlZCBlbmRwb2ludHMqKiAtIFJlbW92ZSBBSS10by1jdXN0b21lciBhbmQgY3VzdG9tZXItdG8tQUkgY2FsbCBlbmRwb2ludHNcbjMuICoqQUktcmVsYXRlZCBtb2RlbHMqKiAtIFJlbW92ZSBBSS1zcGVjaWZpYyBmaWVsZHMgYW5kIHN0cnVjdHVyZXNcbjQuICoqQUkgY29uZmlndXJhdGlvbioqIC0gUmVtb3ZlIEFJIGNvbmZpZyBmcm9tIGBjb25maWcvY29uZmlnLmdvYFxuNS4gKipBSSBkZXBlbmRlbmNpZXMqKiAtIFJlbW92ZSBmcm9tIGNhbGwgbWFuYWdlciBhbmQgaGFuZGxlcnNcblxuIyMjIE5ldyB1c2VyLXRvLXVzZXIgY2FsbGluZyBmZWF0dXJlcyB0byBpbXBsZW1lbnQ6XG4xLiAqKlVzZXItdG8tdXNlciBjYWxsIGluaXRpYXRpb24qKiAtIE9uZSB1c2VyIGNhbGxzIGFub3RoZXIgdXNlclxuMi4gKipDYWxsIG5vdGlmaWNhdGlvbnMqKiAtIE5vdGlmeSB0aGUgcmVjZWl2aW5nIHVzZXIgYWJvdXQgaW5jb21pbmcgY2FsbHNcbjMuICoqU2ltcGxpZmllZCBjYWxsIG1hbmFnZW1lbnQqKiAtIFJlbW92ZSBBSSBwcm9jZXNzaW5nIGxvZ2ljXG40LiAqKlVwZGF0ZWQgbW9kZWxzKiogLSBTdXBwb3J0IGNhbGxlciBhbmQgcmVjZWl2ZXIgdXNlciBJRHNcblxuIyMgRGV0YWlsZWQgUmVmYWN0b3JpbmcgUGxhblxuXG4jIyMgRmlsZXMgdG8gbW9kaWZ5OlxuLSBgbWFpbi5nb2AgLSBSZW1vdmUgQUkgc2VydmljZSBpbml0aWFsaXphdGlvblxuLSBgY29uZmlnL2NvbmZpZy5nb2AgLSBSZW1vdmUgQUkgY29uZmlndXJhdGlvblxuLSBgc2VydmljZXMvY2FsbF9tYW5hZ2VyLmdvYCAtIFJlbW92ZSBBSSBzZXJ2aWNlIGRlcGVuZGVuY3kgYW5kIEFJLXJlbGF0ZWQgbWV0aG9kc1xuLSBgaGFuZGxlcnMvY2FsbF9oYW5kbGVyLmdvYCAtIFJlcGxhY2UgQUkgZW5kcG9pbnRzIHdpdGggdXNlci10by11c2VyIGVuZHBvaW50c1xuLSBgbW9kZWxzL2NhbGwuZ29gIC0gVXBkYXRlIG1vZGVscyBmb3IgdXNlci10by11c2VyIGNhbGxzXG4tIGBzZXJ2aWNlcy9ub3RpZmljYXRpb25fc2VydmljZS5nb2AgLSBVcGRhdGUgZm9yIHVzZXItdG8tdXNlciBub3RpZmljYXRpb25zXG4tIGBSRUFETUUubWRgIC0gVXBkYXRlIGRvY3VtZW50YXRpb25cbi0gYC5lbnZgIC0gUmVtb3ZlIEFJLXJlbGF0ZWQgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5cbiMjIyBGaWxlcyB0byByZW1vdmU6XG4tIGBzZXJ2aWNlcy9haV9zZXJ2aWNlLmdvYCAtIENvbXBsZXRlIEFJIHNlcnZpY2VcblxuTGV0IG1lIHN0YXJ0IGltcGxlbWVudGluZyB0aGVzZSBjaGFuZ2VzOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG4gICAgICAgIDxyZXR1cm4tY29kZT5cbiAgICAgICAgMFxuICAgICAgICA8L3JldHVybi1jb2RlPlxuICAgICAgICA8c3Rkb3V0PlxuICAgICAgICAuL0JhY2tlbmQvbWlkZGxld2FyZS9jb3JzLmdvXG4uL0JhY2tlbmQvY29uZmlnL2NvbmZpZy5nb1xuLi9CYWNrZW5kL21vZGVscy9jYWxsLmdvXG4uL0JhY2tlbmQvUkVBRE1FLm1kXG4uL0JhY2tlbmQvLmVudlxuLi9CYWNrZW5kL2hhbmRsZXJzL2NhbGxfaGFuZGxlci5nb1xuLi9CYWNrZW5kL21haW4uZ29cbi4vQmFja2VuZC9zZXJ2aWNlcy9jYWxsX21hbmFnZXIuZ29cbi4vQmFja2VuZC9zZXJ2aWNlcy9haV9zZXJ2aWNlLmdvXG4uL0JhY2tlbmQvc2VydmljZXMvbm90aWZpY2F0aW9uX3NlcnZpY2UuZ29cbi4vQmFja2VuZC9zZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvXG4uL01vYmlsZSBhcHAvaW9zL1J1bm5lci9Bc3NldHMueGNhc3NldHMvTGF1bmNoSW1hZ2UuaW1hZ2VzZXQvUkVBRE1FLm1kXG4uL01vYmlsZSBhcHAvUkVBRE1FLm1kXG5cbiAgICAgICAgPC9zdGRvdXQ+XG4gICAgICAgIDxzdGRlcnI+XG4gICAgICAgIFxuICAgICAgICA8L3N0ZGVycj4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDEyTkNuaDNOS2ZYZWRQaFM3M0Z2MVpEIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMyLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODowODo1NS45NjFaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBJIGhhdmUgYSBjbGVhciB1bmRlcnN0YW5kaW5nIG9mIHRoZSBjdXJyZW50IGJhY2tlbmQgc3RydWN0dXJlLiBMZXQgbWUgY3JlYXRlIGEgZGV0YWlsZWQgcGxhbiB0byByZWZhY3RvciB0aGUgYmFja2VuZCB0byByZW1vdmUgQUkgZmVhdHVyZXMgYW5kIGltcGxlbWVudCB1c2VyLXRvLXVzZXIgY2FsbGluZyBmdW5jdGlvbmFsaXR5LlxuXG4jIyBBbmFseXNpcyBhbmQgUGxhblxuXG5CYXNlZCBvbiBteSBhbmFseXNpcywgdGhlIGN1cnJlbnQgYmFja2VuZCBpcyBoZWF2aWx5IGZvY3VzZWQgb24gQUkgY3VzdG9tZXIgc3VwcG9ydCBjYWxscy4gSGVyZSdzIHdoYXQgbmVlZHMgdG8gYmUgY2hhbmdlZDpcblxuIyMjIEN1cnJlbnQgQUktcmVsYXRlZCBjb21wb25lbnRzIHRvIHJlbW92ZS9tb2RpZnk6XG4xLiAqKkFJIFNlcnZpY2UqKiAoYHNlcnZpY2VzL2FpX3NlcnZpY2UuZ29gKSAtIEVudGlyZSBmaWxlIGNhbiBiZSByZW1vdmVkXG4yLiAqKkFJLXJlbGF0ZWQgZW5kcG9pbnRzKiogLSBSZW1vdmUgQUktdG8tY3VzdG9tZXIgYW5kIGN1c3RvbWVyLXRvLUFJIGNhbGwgZW5kcG9pbnRzXG4zLiAqKkFJLXJlbGF0ZWQgbW9kZWxzKiogLSBSZW1vdmUgQUktc3BlY2lmaWMgZmllbGRzIGFuZCBzdHJ1Y3R1cmVzXG40LiAqKkFJIGNvbmZpZ3VyYXRpb24qKiAtIFJlbW92ZSBBSSBjb25maWcgZnJvbSBgY29uZmlnL2NvbmZpZy5nb2BcbjUuICoqQUkgZGVwZW5kZW5jaWVzKiogLSBSZW1vdmUgZnJvbSBjYWxsIG1hbmFnZXIgYW5kIGhhbmRsZXJzXG5cbiMjIyBOZXcgdXNlci10by11c2VyIGNhbGxpbmcgZmVhdHVyZXMgdG8gaW1wbGVtZW50OlxuMS4gKipVc2VyLXRvLXVzZXIgY2FsbCBpbml0aWF0aW9uKiogLSBPbmUgdXNlciBjYWxscyBhbm90aGVyIHVzZXJcbjIuICoqQ2FsbCBub3RpZmljYXRpb25zKiogLSBOb3RpZnkgdGhlIHJlY2VpdmluZyB1c2VyIGFib3V0IGluY29taW5nIGNhbGxzXG4zLiAqKlNpbXBsaWZpZWQgY2FsbCBtYW5hZ2VtZW50KiogLSBSZW1vdmUgQUkgcHJvY2Vzc2luZyBsb2dpY1xuNC4gKipVcGRhdGVkIG1vZGVscyoqIC0gU3VwcG9ydCBjYWxsZXIgYW5kIHJlY2VpdmVyIHVzZXIgSURzXG5cbiMjIERldGFpbGVkIFJlZmFjdG9yaW5nIFBsYW5cblxuIyMjIEZpbGVzIHRvIG1vZGlmeTpcbi0gYG1haW4uZ29gIC0gUmVtb3ZlIEFJIHNlcnZpY2UgaW5pdGlhbGl6YXRpb25cbi0gYGNvbmZpZy9jb25maWcuZ29gIC0gUmVtb3ZlIEFJIGNvbmZpZ3VyYXRpb25cbi0gYHNlcnZpY2VzL2NhbGxfbWFuYWdlci5nb2AgLSBSZW1vdmUgQUkgc2VydmljZSBkZXBlbmRlbmN5IGFuZCBBSS1yZWxhdGVkIG1ldGhvZHNcbi0gYGhhbmRsZXJzL2NhbGxfaGFuZGxlci5nb2AgLSBSZXBsYWNlIEFJIGVuZHBvaW50cyB3aXRoIHVzZXItdG8tdXNlciBlbmRwb2ludHNcbi0gYG1vZGVscy9jYWxsLmdvYCAtIFVwZGF0ZSBtb2RlbHMgZm9yIHVzZXItdG8tdXNlciBjYWxsc1xuLSBgc2VydmljZXMvbm90aWZpY2F0aW9uX3NlcnZpY2UuZ29gIC0gVXBkYXRlIGZvciB1c2VyLXRvLXVzZXIgbm90aWZpY2F0aW9uc1xuLSBgUkVBRE1FLm1kYCAtIFVwZGF0ZSBkb2N1bWVudGF0aW9uXG4tIGAuZW52YCAtIFJlbW92ZSBBSS1yZWxhdGVkIGVudmlyb25tZW50IHZhcmlhYmxlc1xuXG4jIyMgRmlsZXMgdG8gcmVtb3ZlOlxuLSBgc2VydmljZXMvYWlfc2VydmljZS5nb2AgLSBDb21wbGV0ZSBBSSBzZXJ2aWNlXG5cbkxldCBtZSBzdGFydCBpbXBsZW1lbnRpbmcgdGhlc2UgY2hhbmdlczpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI5YTFkZWUyOS1iZmNjLTRiNmUtOTkwYi04ZDllNTIwY2VjN2MiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFUNVppN0ZSYkJjZ0JDNU5mR3B0NmtZIiwidG9vbF9uYW1lIjoidmlldyIsImlucHV0X2pzb24iOiJ7XCJwYXRoXCI6IFwiQmFja2VuZC9jb25maWcvY29uZmlnLmdvXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBub3QgZm91bmQ6IEJhY2tlbmQvY29uZmlnL2NvbmZpZy5nby4gRGlkIHlvdSBtZWFuIG9uZSBvZiB0aGVzZT8gQmFja2VuZC9jb25maWcvY29uZmlnLmdvIiwiaXNfZXJyb3IiOnRydWUsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFMaFNrYWpZNXFnS3FBTVhXeEFjQ2UyIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMyLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODoxMToxMy40MTFaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiIwMDk5Y2NhYi0wNTE0LTRhMTMtYjQ1My0xN2NhOTMwOGU0YmIiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFITjhtYVE3bmVERDVENzRjQVZjRWc1IiwidG9vbF9uYW1lIjoibGF1bmNoLXByb2Nlc3MiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcImxzIC1sYSBCYWNrZW5kL1wiLCBcIndhaXRcIjogdHJ1ZSwgXCJtYXhfd2FpdF9zZWNvbmRzXCI6IDEwfSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IkxldCBtZSBjaGVjayB0aGUgY29ycmVjdCBwYXRoOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBub3QgZm91bmQ6IEJhY2tlbmQvY29uZmlnL2NvbmZpZy5nby4gRGlkIHlvdSBtZWFuIG9uZSBvZiB0aGVzZT8gQmFja2VuZC9jb25maWcvY29uZmlnLmdvIiwiaXNfZXJyb3IiOnRydWUsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFUNVppN0ZSYkJjZ0JDNU5mR3B0NmtZIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMyLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODoxMToxOC4wMTRaIiwicmVzcG9uc2VfdGV4dCI6IkxldCBtZSBjaGVjayB0aGUgY29ycmVjdCBwYXRoOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjRkNTBmYTBmLWU0ZDUtNDgyMy04NTBmLWVmYzJkNGRlZWNmOSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMU1uS0puaWdteU1nZWlKTGtEQVJyVksiLCJ0b29sX25hbWUiOiJ2aWV3IiwiaW5wdXRfanNvbiI6IntcInBhdGhcIjogXCJCYWNrZW5kL2NvbmZpZ1wifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkhlcmUgYXJlIHRoZSByZXN1bHRzIGZyb20gZXhlY3V0aW5nIHRoZSBjb21tYW5kLlxuICAgICAgICA8cmV0dXJuLWNvZGU+XG4gICAgICAgIDBcbiAgICAgICAgPC9yZXR1cm4tY29kZT5cbiAgICAgICAgPHN0ZG91dD5cbiAgICAgICAgdG90YWwgNDc2MTZcbmRyd3hyLXhyLXhAIDE2IHF1YW4ubmdvICBzdGFmZiAgICAgICA1MTIgTWF5IDI2IDE0OjM2IC5cbmRyd3hyLXhyLXhAICA3IHF1YW4ubmdvICBzdGFmZiAgICAgICAyMjQgTWF5IDI2IDE0OjM3IC4uXG4tcnctci0tci0tQCAgMSBxdWFuLm5nbyAgc3RhZmYgICAgICA2MTQ4IE1heSAyNiAxMTo1NCAuRFNfU3RvcmVcbi1ydy1yLS1yLS1AICAxIHF1YW4ubmdvICBzdGFmZiAgICAgICA1MzAgTWF5IDI2IDExOjUzIC5lbnZcbi1ydy1yLS1yLS1AICAxIHF1YW4ubmdvICBzdGFmZiAgICAgIDk1MjggTWF5IDI2IDExOjUyIFJFQURNRS5tZFxuLXJ3eHIteHIteEAgIDEgcXVhbi5uZ28gIHN0YWZmICAxMjA4ODQ1MCBNYXkgMjYgMTE6MzggYWktc3VwcG9ydC1iYWNrZW5kXG5kcnd4ci14ci14QCAgMyBxdWFuLm5nbyAgc3RhZmYgICAgICAgIDk2IE1heSAyNiAxMToyMiBjb25maWdcbi1ydy1yLS1yLS1AICAxIHF1YW4ubmdvICBzdGFmZiAgICAgIDE0NzAgTWF5IDI2IDEzOjM2IGdvLm1vZFxuLXJ3LXItLXItLUAgIDEgcXVhbi5uZ28gIHN0YWZmICAgICAgODQyNSBNYXkgMjYgMTM6MzYgZ28uc3VtXG5kcnd4ci14ci14QCAgMyBxdWFuLm5nbyAgc3RhZmYgICAgICAgIDk2IE1heSAyNiAxMToyNSBoYW5kbGVyc1xuLXJ3eHIteHIteEAgIDEgcXVhbi5uZ28gIHN0YWZmICAxMjIzMTI5OCBNYXkgMjYgMTQ6MzYgbWFpblxuLXJ3LXItLXItLUAgIDEgcXVhbi5uZ28gIHN0YWZmICAgICAgMzUwNCBNYXkgMjYgMTE6MzggbWFpbi5nb1xuZHJ3eHIteHIteEAgIDMgcXVhbi5uZ28gIHN0YWZmICAgICAgICA5NiBNYXkgMjYgMTE6MjUgbWlkZGxld2FyZVxuZHJ3eHIteHIteEAgIDMgcXVhbi5uZ28gIHN0YWZmICAgICAgICA5NiBNYXkgMjYgMTE6MjMgbW9kZWxzXG5kcnd4ci14ci14QCAgNiBxdWFuLm5nbyAgc3RhZmYgICAgICAgMTkyIE1heSAyNiAxNDowMyBzZXJ2aWNlc1xuLXJ3eHIteHIteEAgIDEgcXVhbi5uZ28gIHN0YWZmICAgICAgNDY5MyBNYXkgMjYgMTE6NDAgdGVzdF9hcGkuc2hcblxuICAgICAgICA8L3N0ZG91dD5cbiAgICAgICAgPHN0ZGVycj5cbiAgICAgICAgXG4gICAgICAgIDwvc3RkZXJyPiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUhOOG1hUTduZURENUQ3NGNBVmNFZzUifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIiwiZm9sZGVyUm9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MzMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTI2VDA4OjExOjM2LjcyMVoiLCJyZXNwb25zZV90ZXh0IjoiIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjIyMzkyOWFjLTFhZTAtNDRhMy05ODRmLTNmYTk4MjY0NWYwNSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUp1a2V6UlJYdE15TEJRWU1BMkJ5aTUiLCJ0b29sX25hbWUiOiJsYXVuY2gtcHJvY2VzcyIsImlucHV0X2pzb24iOiJ7XCJjb21tYW5kXCI6IFwiZmluZCBCYWNrZW5kIC1uYW1lIFxcXCJjb25maWcuZ29cXFwiXCIsIFwid2FpdFwiOiB0cnVlLCBcIm1heF93YWl0X3NlY29uZHNcIjogMTB9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBub3QgZm91bmQ6IEJhY2tlbmQvY29uZmlnLiBEaWQgeW91IG1lYW4gb25lIG9mIHRoZXNlPyBCYWNrZW5kL2NvbmZpZyIsImlzX2Vycm9yIjp0cnVlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxTW5LSm5pZ215TWdlaUpMa0RBUnJWSyJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozMywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMjZUMDg6MTE6NTkuNzkxWiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiOWRlNzMwYjctODFiNC00M2QxLTgxNGEtYTg2NDY4M2FhYWQyIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxRWZDaGdnUUtSQW80UEhnYjd0NzJmdSIsInRvb2xfbmFtZSI6InZpZXciLCJpbnB1dF9qc29uIjoie1wicGF0aFwiOiBcIkJhY2tlbmQvY29uZmlnL2NvbmZpZy5nb1wifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkhlcmUgYXJlIHRoZSByZXN1bHRzIGZyb20gZXhlY3V0aW5nIHRoZSBjb21tYW5kLlxuPHJldHVybi1jb2RlPlxuMFxuPC9yZXR1cm4tY29kZT5cbjxzdGRvdXQ+XG5CYWNrZW5kL2NvbmZpZy9jb25maWcuZ29cblxuPC9zdGRvdXQ+XG48c3RkZXJyPlxuXG48L3N0ZGVycj4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFKdWtlelJSWHRNeUxCUVlNQTJCeWk1In19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjM0LCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODoxMjowMy44NDBaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJmNDNiMzQzOS05MmRhLTRkNTMtODcyMS1lZWY1ZDFmMWU2ZjgiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDE3RlR3Umd4VUtxTTNWVGgxWWs5REhwIiwidG9vbF9uYW1lIjoibGF1bmNoLXByb2Nlc3MiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcImNhdCBCYWNrZW5kL2NvbmZpZy9jb25maWcuZ29cIiwgXCJ3YWl0XCI6IHRydWUsIFwibWF4X3dhaXRfc2Vjb25kc1wiOiAxMH0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiJMZXQgbWUgdHJ5IGEgZGlmZmVyZW50IGFwcHJvYWNoIHRvIGFjY2VzcyB0aGUgZmlsZTpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgbm90IGZvdW5kOiBCYWNrZW5kL2NvbmZpZy9jb25maWcuZ28uIERpZCB5b3UgbWVhbiBvbmUgb2YgdGhlc2U/IEJhY2tlbmQvY29uZmlnL2NvbmZpZy5nbyIsImlzX2Vycm9yIjp0cnVlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxRWZDaGdnUUtSQW80UEhnYjd0NzJmdSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUiLCJmb2xkZXJSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozNCwiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMjZUMDg6MTI6MjYuMTc4WiIsInJlc3BvbnNlX3RleHQiOiJMZXQgbWUgdHJ5IGEgZGlmZmVyZW50IGFwcHJvYWNoIHRvIGFjY2VzcyB0aGUgZmlsZTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoiY2FuY2VsbGVkIiwicmVxdWVzdF9pZCI6InRlbXAtZmUtZDBlOGM1NWEtYmY5Mi00NTcxLWI5YjUtZjA0Y2M5NzllOWEwIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJIZXJlIGFyZSB0aGUgcmVzdWx0cyBmcm9tIGV4ZWN1dGluZyB0aGUgY29tbWFuZC5cbiAgICAgICAgPHJldHVybi1jb2RlPlxuICAgICAgICAwXG4gICAgICAgIDwvcmV0dXJuLWNvZGU+XG4gICAgICAgIDxzdGRvdXQ+XG4gICAgICAgIHBhY2thZ2UgY29uZmlnXG5cbmltcG9ydCAoXG5cdFwibG9nXCJcblx0XCJvc1wiXG5cdFwic3RyY29udlwiXG5cblx0XCJnaXRodWIuY29tL2pvaG8vZ29kb3RlbnZcIlxuKVxuXG50eXBlIENvbmZpZyBzdHJ1Y3Qge1xuXHRTZXJ2ZXIgU2VydmVyQ29uZmlnXG5cdEFnb3JhICBBZ29yYUNvbmZpZ1xuXHRBSSAgICAgQUlDb25maWdcbn1cblxudHlwZSBTZXJ2ZXJDb25maWcgc3RydWN0IHtcblx0UG9ydCBzdHJpbmdcblx0SG9zdCBzdHJpbmdcbn1cblxudHlwZSBBZ29yYUNvbmZpZyBzdHJ1Y3Qge1xuXHRBcHBJRCAgICAgICAgICBzdHJpbmdcblx0QXBwQ2VydGlmaWNhdGUgc3RyaW5nXG5cdENoYW5uZWxQcmVmaXggIHN0cmluZ1xufVxuXG50eXBlIEFJQ29uZmlnIHN0cnVjdCB7XG5cdE9wZW5BSUFQSUtleSAgICBzdHJpbmdcblx0T3BlbkFJTW9kZWwgICAgIHN0cmluZ1xuXHRTVFRQcm92aWRlciAgICAgc3RyaW5nXG5cdFRUU1Byb3ZpZGVyICAgICBzdHJpbmdcblx0Vm9pY2VJRCAgICAgICAgIHN0cmluZ1xuXHRFbmFibGVWQUQgICAgICAgYm9vbFxuXHRWQURTZW5zaXRpdml0eSAgZmxvYXQ2NFxufVxuXG52YXIgQXBwQ29uZmlnICpDb25maWdcblxuZnVuYyBMb2FkQ29uZmlnKCkge1xuXHQvLyBMb2FkIC5lbnYgZmlsZSBpZiBpdCBleGlzdHNcblx0aWYgZXJyIDo9IGdvZG90ZW52LkxvYWQoKTsgZXJyICE9IG5pbCB7XG5cdFx0bG9nLlByaW50bG4oXCJObyAuZW52IGZpbGUgZm91bmQsIHVzaW5nIGVudmlyb25tZW50IHZhcmlhYmxlc1wiKVxuXHR9XG5cblx0QXBwQ29uZmlnID0gJkNvbmZpZ3tcblx0XHRTZXJ2ZXI6IFNlcnZlckNvbmZpZ3tcblx0XHRcdFBvcnQ6IGdldEVudihcIlNFUlZFUl9QT1JUXCIsIFwiODA4MFwiKSxcblx0XHRcdEhvc3Q6IGdldEVudihcIlNFUlZFUl9IT1NUXCIsIFwiMC4wLjAuMFwiKSxcblx0XHR9LFxuXHRcdEFnb3JhOiBBZ29yYUNvbmZpZ3tcblx0XHRcdEFwcElEOiAgICAgICAgICBnZXRFbnYoXCJBR09SQV9BUFBfSURcIiwgXCJcIiksXG5cdFx0XHRBcHBDZXJ0aWZpY2F0ZTogZ2V0RW52KFwiQUdPUkFfQVBQX0NFUlRJRklDQVRFXCIsIFwiXCIpLFxuXHRcdFx0Q2hhbm5lbFByZWZpeDogIGdldEVudihcIkFHT1JBX0NIQU5ORUxfUFJFRklYXCIsIFwiYWlfc3VwcG9ydF9cIiksXG5cdFx0fSxcblx0XHRBSTogQUlDb25maWd7XG5cdFx0XHRPcGVuQUlBUElLZXk6ICAgIGdldEVudihcIk9QRU5BSV9BUElfS0VZXCIsIFwiXCIpLFxuXHRcdFx0T3BlbkFJTW9kZWw6ICAgICBnZXRFbnYoXCJPUEVOQUlfTU9ERUxcIiwgXCJncHQtNFwiKSxcblx0XHRcdFNUVFByb3ZpZGVyOiAgICAgZ2V0RW52KFwiU1RUX1BST1ZJREVSXCIsIFwib3BlbmFpXCIpLFxuXHRcdFx0VFRTUHJvdmlkZXI6ICAgICBnZXRFbnYoXCJUVFNfUFJPVklERVJcIiwgXCJvcGVuYWlcIiksXG5cdFx0XHRWb2ljZUlEOiAgICAgICAgIGdldEVudihcIlRUU19WT0lDRV9JRFwiLCBcImFsbG95XCIpLFxuXHRcdFx0RW5hYmxlVkFEOiAgICAgICBnZXRFbnZCb29sKFwiRU5BQkxFX1ZBRFwiLCB0cnVlKSxcblx0XHRcdFZBRFNlbnNpdGl2aXR5OiAgZ2V0RW52RmxvYXQoXCJWQURfU0VOU0lUSVZJVFlcIiwgMC43KSxcblx0XHR9LFxuXHR9XG5cblx0Ly8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG5cdGlmIEFwcENvbmZpZy5BZ29yYS5BcHBJRCA9PSBcIlwiIHtcblx0XHRsb2cuRmF0YWwoXCJBR09SQV9BUFBfSUQgaXMgcmVxdWlyZWRcIilcblx0fVxufVxuXG5mdW5jIGdldEVudihrZXksIGRlZmF1bHRWYWx1ZSBzdHJpbmcpIHN0cmluZyB7XG5cdGlmIHZhbHVlIDo9IG9zLkdldGVudihrZXkpOyB2YWx1ZSAhPSBcIlwiIHtcblx0XHRyZXR1cm4gdmFsdWVcblx0fVxuXHRyZXR1cm4gZGVmYXVsdFZhbHVlXG59XG5cbmZ1bmMgZ2V0RW52Qm9vbChrZXkgc3RyaW5nLCBkZWZhdWx0VmFsdWUgYm9vbCkgYm9vbCB7XG5cdGlmIHZhbHVlIDo9IG9zLkdldGVudihrZXkpOyB2YWx1ZSAhPSBcIlwiIHtcblx0XHRpZiBwYXJzZWQsIGVyciA6PSBzdHJjb252LlBhcnNlQm9vbCh2YWx1ZSk7IGVyciA9PSBuaWwge1xuXHRcdFx0cmV0dXJuIHBhcnNlZFxuXHRcdH1cblx0fVxuXHRyZXR1cm4gZGVmYXVsdFZhbHVlXG59XG5cbmZ1bmMgZ2V0RW52RmxvYXQoa2V5IHN0cmluZywgZGVmYXVsdFZhbHVlIGZsb2F0NjQpIGZsb2F0NjQge1xuXHRpZiB2YWx1ZSA6PSBvcy5HZXRlbnYoa2V5KTsgdmFsdWUgIT0gXCJcIiB7XG5cdFx0aWYgcGFyc2VkLCBlcnIgOj0gc3RyY29udi5QYXJzZUZsb2F0KHZhbHVlLCA2NCk7IGVyciA9PSBuaWwge1xuXHRcdFx0cmV0dXJuIHBhcnNlZFxuXHRcdH1cblx0fVxuXHRyZXR1cm4gZGVmYXVsdFZhbHVlXG59XG5cbiAgICAgICAgPC9zdGRvdXQ+XG4gICAgICAgIDxzdGRlcnI+XG4gICAgICAgIFxuICAgICAgICA8L3N0ZGVycj4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDE3RlR3Umd4VUtxTTNWVGgxWWs5REhwIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL1VzZXJzL3F1YW4ubmdvL0Rlc2t0b3AvaW5fYXBwX2NhbGxfZmVhdHVyZSIsImZvbGRlclJvb3QiOiIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjM1LCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9Vc2Vycy9xdWFuLm5nby9EZXNrdG9wL2luX2FwcF9jYWxsX2ZlYXR1cmUifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0yNlQwODoxMzowOC4zOTJaIn0seyJyZXF1ZXN0X2lkIjoiZWNjNWRjMGEtMjA4Mi00YTUxLWI4YWYtMmM1ODYwMWZkZTYyIiwic3RhdHVzIjoiY2FuY2VsbGVkIiwiY2hhdEl0ZW1UeXBlIjoiYWdlbnRpYy10dXJuLWRlbGltaXRlciJ9XSwiZmVlZGJhY2tTdGF0ZXMiOnsidGVtcC1mZS1lMzBmYTYwMS1kZjgyLTRhOTgtOWM3MC1kODRkYjYwOTZjOGIiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWJhZWU0MDk2LTdkNGUtNGMyYS05MjBhLWExNWE3NmRiZTM1NiI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtNDM0NDlkYWMtZDQ2Mi00YTRkLWFlYTUtNTA0OTUwNTA1OGYzIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS03ZjMzMTE3NC1mNDY5LTRjZTctODBiMi0yNjQ1NjVhNTUyYjciOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTY0N2ZhYzRlLTk1NGUtNDExOS04MGMwLWRhM2JmNzFhOWY2MCI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMWJkNTkzNmMtZjAyNy00ODQ2LWJmNTItMDM5MzJjMzgxNTUwIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS0yY2VhYzdlNC0xMmExLTQ2NzktOGQ5OC02N2JlYWFiYjk0ZmYiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTA4YjAwYTc1LTJkZmYtNDNkOC1hM2Y3LTk4YTM3OTY4ZWI3YSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtZGFhZTFjMTAtYTZhMy00ZDBkLWJhOTQtMzMwMWY0YzM1ZjQzIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS0zY2Y2MzU0MC02ZWY5LTQ2MzktYTcwYS1lNjU5YTZkZmNmZGEiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWYyNTE1YTQ2LWY4MzMtNDk3OC05YzU2LTM4OGFmZjAwYTI3MSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtZDBlOGM1NWEtYmY5Mi00NTcxLWI5YjUtZjA0Y2M5NzllOWEwIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifX0sInRvb2xVc2VTdGF0ZXMiOnsiMjg3MzQxMmQtYTAwMi00MWJhLTliM2ItY2FhOTExMjg4NGQyO3Rvb2x1X3ZydHhfMDFXVHZzdGZRVFV4VWNjcmF6dFZZN0pUIjp7InJlcXVlc3RJZCI6IjI4NzM0MTJkLWEwMDItNDFiYS05YjNiLWNhYTkxMTI4ODRkMiIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFXVHZzdGZRVFV4VWNjcmF6dFZZN0pUIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogbWFpbi5nb1xuLi4uXG4gICAgMTdcdFxuICAgIDE4XHRmdW5jIG1haW4oKSB7XG4gICAgMTlcdFx0Ly8gTG9hZCBjb25maWd1cmF0aW9uXG4gICAgMjBcdFx0Y29uZmlnLkxvYWRDb25maWcoKVxuICAgIDIxXHRcdGNmZyA6PSBjb25maWcuQXBwQ29uZmlnXG4gICAgMjJcdFxuICAgIDIzXHRcdGxvZy5QcmludGYoXCLwn5qAIFN0YXJ0aW5nIEFJIEN1c3RvbWVyIFN1cHBvcnQgQmFja2VuZFwiKVxuICAgIDI0XHRcdGxvZy5QcmludGYoXCLwn5OLIFNlcnZlcjogJXM6JXNcIiwgY2ZnLlNlcnZlci5Ib3N0LCBjZmcuU2VydmVyLlBvcnQpXG4gICAgMjVcdFx0bG9nLlByaW50ZihcIvCfjq8gQWdvcmEgQXBwIElEOiAlc1wiLCBjZmcuQWdvcmEuQXBwSUQpXG4gICAgMjZcdFxuICAgIDI3XHRcdC8vIEluaXRpYWxpemUgc2VydmljZXNcbiAgICAyOFx0XHRub3RpZmljYXRpb25TZXJ2aWNlIDo9IHNlcnZpY2VzLk5ld05vdGlmaWNhdGlvblNlcnZpY2UoKVxuICAgIDI5XHRcdGFnb3JhU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdBZ29yYVNlcnZpY2UoY2ZnKVxuICAgIDMwXHRcdGFpU2VydmljZSA6PSBzZXJ2aWNlcy5OZXdBSVNlcnZpY2UoY2ZnKVxuICAgIDMxXHRcdGNhbGxNYW5hZ2VyIDo9IHNlcnZpY2VzLk5ld0NhbGxNYW5hZ2VyKG5vdGlmaWNhdGlvblNlcnZpY2UsIGFpU2VydmljZSwgYWdvcmFTZXJ2aWNlKVxuICAgIDMyXHRcbiAgICAzM1x0XHQvLyBJbml0aWFsaXplIGhhbmRsZXJzXG4gICAgMzRcdFx0Y2FsbEhhbmRsZXIgOj0gaGFuZGxlcnMuTmV3Q2FsbEhhbmRsZXIoY2FsbE1hbmFnZXIsIGFnb3JhU2VydmljZSlcbiAgICAzNVx0XG4gICAgMzZcdFx0Ly8gU2V0dXAgR2luIHJvdXRlclxuICAgIDM3XHRcdHJvdXRlciA6PSBzZXR1cFJvdXRlcihjYWxsSGFuZGxlcilcbiAgICAzOFx0XG4gICAgMzlcdFx0Ly8gU2V0dXAgZ3JhY2VmdWwgc2h1dGRvd25cbiAgICA0MFx0XHRzZXR1cEdyYWNlZnVsU2h1dGRvd24oYWdvcmFTZXJ2aWNlKVxuLi4uXG4gICAgNjVcdFxuICAgIDY2XHRcdC8vIEFQSSByb3V0ZXNcbiAgICA2N1x0XHRhcGkgOj0gcm91dGVyLkdyb3VwKFwiL2FwaVwiKVxuICAgIDY4XHRcdHtcbiAgICA2OVx0XHRcdC8vIENhbGwgbWFuYWdlbWVudCByb3V0ZXNcbiAgICA3MFx0XHRcdGNhbGxzIDo9IGFwaS5Hcm91cChcIi9jYWxsc1wiKVxuICAgIDcxXHRcdFx0e1xuICAgIDcyXHRcdFx0XHRjYWxscy5QT1NUKFwiL2luaXRpYXRlXCIsIGNhbGxIYW5kbGVyLkluaXRpYXRlQ2FsbCkgICAgICAgICAgICAgICAgICAgIC8vIEFJIGNhbGxzIGN1c3RvbWVyXG4gICAgNzNcdFx0XHRcdGNhbGxzLlBPU1QoXCIvY3VzdG9tZXItaW5pdGlhdGVcIiwgY2FsbEhhbmRsZXIuSW5pdGlhdGVDdXN0b21lckNhbGwpICAgLy8gQ3VzdG9tZXIgY2FsbHMgQUlcbiAgICA3NFx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC9qb2luXCIsIGNhbGxIYW5kbGVyLkpvaW5DYWxsKVxuICAgIDc1XHRcdFx0XHRjYWxscy5HRVQoXCIvOmNhbGxfaWRcIiwgY2FsbEhhbmRsZXIuR2V0Q2FsbClcbiAgICA3Nlx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC9lbmRcIiwgY2FsbEhhbmRsZXIuRW5kQ2FsbClcbiAgICA3N1x0XHRcdFx0Y2FsbHMuR0VUKFwiL2FjdGl2ZVwiLCBjYWxsSGFuZGxlci5HZXRBY3RpdmVDYWxscylcbiAgICA3OFx0XHRcdFx0Y2FsbHMuR0VUKFwiLzpjYWxsX2lkL3N0YXRzXCIsIGNhbGxIYW5kbGVyLkdldENhbGxTdGF0cylcbiAgICA3OVx0XHRcdFx0Y2FsbHMuUE9TVChcIi86Y2FsbF9pZC90b2tlblwiLCBjYWxsSGFuZGxlci5HZW5lcmF0ZVRva2VuKVxuICAgIDgwXHRcdFx0fVxuICAgIDgxXHRcdH1cbiAgICA4Mlx0XG4gICAgODNcdFx0Ly8gV2VsY29tZSByb3V0ZVxuICAgIDg0XHRcdHJvdXRlci5HRVQoXCIvXCIsIGZ1bmMoYyAqZ2luLkNvbnRleHQpIHtcbiAgICA4NVx0XHRcdGMuSlNPTihodHRwLlN0YXR1c09LLCBnaW4uSHtcbiAgICA4Nlx0XHRcdFx0XCJtZXNzYWdlXCI6IFwi8J+kliBBSSBDdXN0b21lciBTdXBwb3J0IEJhY2tlbmRcIixcbiAgICA4N1x0XHRcdFx0XCJ2ZXJzaW9uXCI6IFwiMS4wLjBcIixcbiAgICA4OFx0XHRcdFx0XCJzdGF0dXNcIjogIFwicnVubmluZ1wiLFxuICAgIDg5XHRcdFx0XHRcImVuZHBvaW50c1wiOiBnaW4uSHtcbiAgICA5MFx0XHRcdFx0XHRcImhlYWx0aFwiOiAgICAgICAgICAgICAgICBcIkdFVCAvaGVhbHRoXCIsXG4gICAgOTFcdFx0XHRcdFx0XCJpbml0aWF0ZV9jYWxsXCI6ICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvaW5pdGlhdGVcIixcbiAgICA5Mlx0XHRcdFx0XHRcImN1c3RvbWVyX2luaXRpYXRlXCI6ICAgICBcIlBPU1QgL2FwaS9jYWxscy9jdXN0b21lci1pbml0aWF0ZVwiLFxuICAgIDkzXHRcdFx0XHRcdFwiam9pbl9jYWxsXCI6ICAgICAgICAgICAgIFwiUE9TVCAvYXBpL2NhbGxzLzpjYWxsX2lkL2pvaW5cIixcbiAgICA5NFx0XHRcdFx0XHRcImdldF9jYWxsXCI6ICAgICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkXCIsXG4gICAgOTVcdFx0XHRcdFx0XCJlbmRfY2FsbFwiOiAgICAgICAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvZW5kXCIsXG4gICAgOTZcdFx0XHRcdFx0XCJhY3RpdmVfY2FsbHNcIjogICAgICAgICAgXCJHRVQgL2FwaS9jYWxscy9hY3RpdmVcIixcbiAgICA5N1x0XHRcdFx0XHRcImNhbGxfc3RhdHNcIjogICAgICAgICAgICBcIkdFVCAvYXBpL2NhbGxzLzpjYWxsX2lkL3N0YXRzXCIsXG4gICAgOThcdFx0XHRcdFx0XCJnZW5lcmF0ZV90b2tlblwiOiAgICAgICAgXCJQT1NUIC9hcGkvY2FsbHMvOmNhbGxfaWQvdG9rZW5cIixcbiAgICA5OVx0XHRcdFx0fSxcbiAgIDEwMFx0XHRcdH0pXG4gICAxMDFcdFx0fSlcbiAgIDEwMlx0XG4gICAxMDNcdFx0bG9nLlByaW50ZihcIvCfm6PvuI8gUm91dGVzIGNvbmZpZ3VyZWQgc3VjY2Vzc2Z1bGx5XCIpXG4gICAxMDRcdFx0cmV0dXJuIHJvdXRlclxuICAgMTA1XHR9XG4uLi5cblBhdGg6IGhhbmRsZXJzL2NhbGxfaGFuZGxlci5nb1xuICAgICAxXHRwYWNrYWdlIGhhbmRsZXJzXG4gICAgIDJcdFxuICAgICAzXHRpbXBvcnQgKFxuICAgICA0XHRcdFwiQmFja2VuZC9tb2RlbHNcIlxuICAgICA1XHRcdFwiQmFja2VuZC9zZXJ2aWNlc1wiXG4gICAgIDZcdFx0XCJmbXRcIlxuICAgICA3XHRcdFwibmV0L2h0dHBcIlxuICAgICA4XHRcdFwidGltZVwiXG4gICAgIDlcdFxuICAgIDEwXHRcdFwiZ2l0aHViLmNvbS9naW4tZ29uaWMvZ2luXCJcbiAgICAxMVx0KVxuICAgIDEyXHRcbiAgICAxM1x0Ly8gQ2FsbEhhbmRsZXIgaGFuZGxlcyBIVFRQIHJlcXVlc3RzIHJlbGF0ZWQgdG8gY2FsbHNcbiAgICAxNFx0dHlwZSBDYWxsSGFuZGxlciBzdHJ1Y3Qge1xuICAgIDE1XHRcdGNhbGxNYW5hZ2VyICAqc2VydmljZXMuQ2FsbE1hbmFnZXJcbiAgICAxNlx0XHRhZ29yYVNlcnZpY2UgKnNlcnZpY2VzLkFnb3JhU2VydmljZVxuICAgIDE3XHR9XG4gICAgMThcdFxuICAgIDE5XHQvLyBOZXdDYWxsSGFuZGxlciBjcmVhdGVzIGEgbmV3IGNhbGwgaGFuZGxlciBpbnN0YW5jZVxuICAgIDIwXHRmdW5jIE5ld0NhbGxIYW5kbGVyKGNhbGxNYW5hZ2VyICpzZXJ2aWNlcy5DYWxsTWFuYWdlciwgYWdvcmFTZXJ2aWNlICpzZXJ2aWNlcy5BZ29yYVNlcnZpY2UpICpDYWxsSGFuZGxlciB7XG4gICAgMjFcdFx0cmV0dXJuICZDYWxsSGFuZGxlcntcbiAgICAyMlx0XHRcdGNhbGxNYW5hZ2VyOiAgY2FsbE1hbmFnZXIsXG4gICAgMjNcdFx0XHRhZ29yYVNlcnZpY2U6IGFnb3JhU2VydmljZSxcbiAgICAyNFx0XHR9XG4gICAgMjVcdH1cbi4uLlxuICAgIDYyXHRcbiAgICA2M1x0XHQvLyBHZW5lcmF0ZSBBZ29yYSB0b2tlbiBmb3IgdGhlIGNoYW5uZWxcbiAgICA2NFx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIFwiYWlfYWdlbnRcIiwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAgNjVcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAgNjZcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgICA2N1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIEFnb3JhIHRva2VuXCIsXG4gICAgNjhcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgICA2OVx0XHRcdH0pXG4gICAgNzBcdFx0XHRyZXR1cm5cbiAgICA3MVx0XHR9XG4gICAgNzJcdFxuICAgIDczXHRcdC8vIEpvaW4gdGhlIGNoYW5uZWwgYXMgQUkgYWdlbnRcbiAgICA3NFx0XHRpZiBlcnIgOj0gY2guYWdvcmFTZXJ2aWNlLkpvaW5DaGFubmVsKGNhbGwuQ2hhbm5lbE5hbWUsIFwiYWlfYWdlbnRcIik7IGVyciAhPSBuaWwge1xuICAgIDc1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAgNzZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBqb2luIEFnb3JhIGNoYW5uZWxcIixcbiAgICA3N1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgIDc4XHRcdFx0fSlcbiAgICA3OVx0XHRcdHJldHVyblxuICAgIDgwXHRcdH1cbiAgICA4MVx0XG4gICAgODJcdFx0Ly8gU2V0IHVwIGF1ZGlvIHByb2Nlc3NpbmcgZm9yIHRoaXMgY2FsbFxuICAgIDgzXHRcdGF1ZGlvQ2FsbGJhY2sgOj0gZnVuYyhhdWRpb0RhdGEgW11ieXRlKSBlcnJvciB7XG4gICAgODRcdFx0XHRyZXR1cm4gY2guY2FsbE1hbmFnZXIuUHJvY2Vzc0F1ZGlvTWVzc2FnZShjYWxsLklELCBhdWRpb0RhdGEpXG4gICAgODVcdFx0fVxuLi4uXG4gICAxMzFcdFxuICAgMTMyXHRcdC8vIEluaXRpYXRlIHRoZSBjdXN0b21lci10by1BSSBjYWxsXG4gICAxMzNcdFx0Y2FsbCwgZXJyIDo9IGNoLmNhbGxNYW5hZ2VyLkluaXRpYXRlQ3VzdG9tZXJDYWxsKHJlcXVlc3QpXG4gICAxMzRcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxMzVcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDEzNlx0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGluaXRpYXRlIGN1c3RvbWVyIGNhbGxcIixcbiAgIDEzN1x0XHRcdFx0XCJkZXRhaWxzXCI6IGVyci5FcnJvcigpLFxuICAgMTM4XHRcdFx0fSlcbiAgIDEzOVx0XHRcdHJldHVyblxuICAgMTQwXHRcdH1cbiAgIDE0MVx0XG4gICAxNDJcdFx0Ly8gR2VuZXJhdGUgQWdvcmEgdG9rZW4gZm9yIHRoZSBjdXN0b21lclxuICAgMTQzXHRcdGN1c3RvbWVyVG9rZW4sIGVyciA6PSBjaC5hZ29yYVNlcnZpY2UuR2VuZXJhdGVUb2tlbihjYWxsLkNoYW5uZWxOYW1lLCByZXF1ZXN0LkN1c3RvbWVySUQsIDEpIC8vIFJvbGUgMSA9IHB1Ymxpc2hlclxuICAgMTQ0XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTQ1XHRcdFx0Yy5KU09OKGh0dHAuU3RhdHVzSW50ZXJuYWxTZXJ2ZXJFcnJvciwgZ2luLkh7XG4gICAxNDZcdFx0XHRcdFwiZXJyb3JcIjogICBcIkZhaWxlZCB0byBnZW5lcmF0ZSBjdXN0b21lciBBZ29yYSB0b2tlblwiLFxuICAgMTQ3XHRcdFx0XHRcImRldGFpbHNcIjogZXJyLkVycm9yKCksXG4gICAxNDhcdFx0XHR9KVxuICAgMTQ5XHRcdFx0cmV0dXJuXG4gICAxNTBcdFx0fVxuLi4uXG4gICAxNzNcdFxuICAgMTc0XHRcdC8vIEFkZCBpbml0aWFsIEFJIGdyZWV0aW5nXG4gICAxNzVcdFx0Z3JlZXRpbmcgOj0gY2guZ2VuZXJhdGVDdXN0b21lckNhbGxHcmVldGluZyhjYWxsKVxuICAgMTc2XHRcdGNhbGwuQWRkTWVzc2FnZShcImFpXCIsIGdyZWV0aW5nLCBcInRleHRcIilcbiAgIDE3N1x0XG4gICAxNzhcdFx0Ly8gUHJlcGFyZSBBSSBpbmZvXG4gICAxNzlcdFx0YWlJbmZvIDo9IG1vZGVscy5BSUluZm97XG4gICAxODBcdFx0XHROYW1lOiAgICAgICAgXCJBSSBTdXBwb3J0IEFzc2lzdGFudFwiLFxuICAgMTgxXHRcdFx0UGVyc29uYWxpdHk6IGNhbGwuTWV0YWRhdGEuQUlQZXJzb25hbGl0eSxcbiAgIDE4Mlx0XHRcdExhbmd1YWdlOiAgICBjYWxsLk1ldGFkYXRhLkxhbmd1YWdlLFxuICAgMTgzXHRcdFx0Q2FwYWJpbGl0aWVzOiBbXXN0cmluZ3tcbiAgIDE4NFx0XHRcdFx0XCJiaWxsaW5nX3N1cHBvcnRcIixcbiAgIDE4NVx0XHRcdFx0XCJ0ZWNobmljYWxfc3VwcG9ydFwiLFxuICAgMTg2XHRcdFx0XHRcImFjY291bnRfbWFuYWdlbWVudFwiLFxuICAgMTg3XHRcdFx0XHRcImdlbmVyYWxfaW5xdWlyaWVzXCIsXG4gICAxODhcdFx0XHRcdFwicGFzc3dvcmRfcmVzZXRcIixcbiAgIDE4OVx0XHRcdFx0XCJzdWJzY3JpcHRpb25fbWFuYWdlbWVudFwiLFxuICAgMTkwXHRcdFx0fSxcbiAgIDE5MVx0XHR9XG4gICAxOTJcdFxuICAgMTkzXHRcdHJlc3BvbnNlIDo9IG1vZGVscy5Jbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNle1xuICAgMTk0XHRcdFx0Q2FsbElEOiAgICAgIGNhbGwuSUQsXG4gICAxOTVcdFx0XHRDaGFubmVsTmFtZTogY2FsbC5DaGFubmVsTmFtZSxcbiAgIDE5Nlx0XHRcdFRva2VuOiAgICAgICBjdXN0b21lclRva2VuLlRva2VuLFxuICAgMTk3XHRcdFx0U3RhdHVzOiAgICAgIHN0cmluZyhjYWxsLlN0YXR1cyksXG4gICAxOThcdFx0XHRNZXNzYWdlOiAgICAgXCJDdXN0b21lciBjYWxsIGluaXRpYXRlZCBzdWNjZXNzZnVsbHkuIEFJIGlzIHJlYWR5IHRvIGFzc2lzdC5cIixcbiAgIDE5OVx0XHRcdEFJSW5mbzogICAgICBhaUluZm8sXG4gICAyMDBcdFx0fVxuICAgMjAxXHRcbiAgIDIwMlx0XHRjLkpTT04oaHR0cC5TdGF0dXNDcmVhdGVkLCByZXNwb25zZSlcbiAgIDIwM1x0fVxuLi4uXG4gICAyNDhcdFxuICAgMjQ5XHRcdC8vIEpvaW4gdGhlIGNhbGxcbiAgIDI1MFx0XHRjYWxsLCBlcnIgOj0gY2guY2FsbE1hbmFnZXIuSm9pbkNhbGwocmVxdWVzdC5DYWxsSUQsIHJlcXVlc3QuQ3VzdG9tZXJJRClcbiAgIDI1MVx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgIDI1Mlx0XHRcdGMuSlNPTihodHRwLlN0YXR1c0JhZFJlcXVlc3QsIGdpbi5Ie1xuICAgMjUzXHRcdFx0XHRcImVycm9yXCI6ICAgXCJGYWlsZWQgdG8gam9pbiBjYWxsXCIsXG4gICAyNTRcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDI1NVx0XHRcdH0pXG4gICAyNTZcdFx0XHRyZXR1cm5cbiAgIDI1N1x0XHR9XG4gICAyNThcdFxuICAgMjU5XHRcdC8vIEdlbmVyYXRlIEFnb3JhIHRva2VuIGZvciB0aGUgY3VzdG9tZXJcbiAgIDI2MFx0XHR0b2tlbiwgZXJyIDo9IGNoLmFnb3JhU2VydmljZS5HZW5lcmF0ZVRva2VuKGNhbGwuQ2hhbm5lbE5hbWUsIHJlcXVlc3QuQ3VzdG9tZXJJRCwgMSkgLy8gUm9sZSAxID0gcHVibGlzaGVyXG4gICAyNjFcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAyNjJcdFx0XHRjLkpTT04oaHR0cC5TdGF0dXNJbnRlcm5hbFNlcnZlckVycm9yLCBnaW4uSHtcbiAgIDI2M1x0XHRcdFx0XCJlcnJvclwiOiAgIFwiRmFpbGVkIHRvIGdlbmVyYXRlIEFnb3JhIHRva2VuXCIsXG4gICAyNjRcdFx0XHRcdFwiZGV0YWlsc1wiOiBlcnIuRXJyb3IoKSxcbiAgIDI2NVx0XHRcdH0pXG4gICAyNjZcdFx0XHRyZXR1cm5cbiAgIDI2N1x0XHR9XG4gICAyNjhcdFxuICAgMjY5XHRcdHJlc3BvbnNlIDo9IG1vZGVscy5Kb2luQ2FsbFJlc3BvbnNle1xuICAgMjcwXHRcdFx0Q2hhbm5lbE5hbWU6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAyNzFcdFx0XHRUb2tlbjogICAgICAgdG9rZW4uVG9rZW4sXG4gICAyNzJcdFx0XHRDYWxsSW5mbzogICAgKmNhbGwsXG4gICAyNzNcdFx0fVxuICAgMjc0XHRcbiAgIDI3NVx0XHRjLkpTT04oaHR0cC5TdGF0dXNPSywgcmVzcG9uc2UpXG4gICAyNzZcdH1cbi4uLlxuUGF0aDogc2VydmljZXMvY2FsbF9tYW5hZ2VyLmdvXG4gICAgIDFcdHBhY2thZ2Ugc2VydmljZXNcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJCYWNrZW5kL21vZGVsc1wiXG4gICAgIDVcdFx0XCJmbXRcIlxuICAgICA2XHRcdFwibG9nXCJcbiAgICAgN1x0XHRcInN5bmNcIlxuICAgICA4XHRcdFwidGltZVwiXG4gICAgIDlcdClcbiAgICAxMFx0XG4gICAgMTFcdC8vIENhbGxNYW5hZ2VyIG1hbmFnZXMgYWN0aXZlIGNhbGxzIGFuZCB0aGVpciBsaWZlY3ljbGVcbiAgICAxMlx0dHlwZSBDYWxsTWFuYWdlciBzdHJ1Y3Qge1xuICAgIDEzXHRcdGNhbGxzICAgICAgICAgICAgICAgbWFwW3N0cmluZ10qbW9kZWxzLkNhbGxcbiAgICAxNFx0XHRtdXRleCAgICAgICAgICAgICAgIHN5bmMuUldNdXRleFxuICAgIDE1XHRcdG5vdGlmaWNhdGlvblNlcnZpY2UgKk5vdGlmaWNhdGlvblNlcnZpY2VcbiAgICAxNlx0XHRhaVNlcnZpY2UgICAgICAgICAgKkFJU2VydmljZVxuICAgIDE3XHRcdGFnb3JhU2VydmljZSAgICAgICAqQWdvcmFTZXJ2aWNlXG4gICAgMThcdH1cbiAgICAxOVx0XG4gICAgMjBcdC8vIE5ld0NhbGxNYW5hZ2VyIGNyZWF0ZXMgYSBuZXcgY2FsbCBtYW5hZ2VyIGluc3RhbmNlXG4gICAgMjFcdGZ1bmMgTmV3Q2FsbE1hbmFnZXIobm90aWZpY2F0aW9uU2VydmljZSAqTm90aWZpY2F0aW9uU2VydmljZSwgYWlTZXJ2aWNlICpBSVNlcnZpY2UsIGFnb3JhU2VydmljZSAqQWdvcmFTZXJ2aWNlKSAqQ2FsbE1hbmFnZXIge1xuICAgIDIyXHRcdHJldHVybiAmQ2FsbE1hbmFnZXJ7XG4gICAgMjNcdFx0XHRjYWxsczogICAgICAgICAgICAgICBtYWtlKG1hcFtzdHJpbmddKm1vZGVscy5DYWxsKSxcbiAgICAyNFx0XHRcdG5vdGlmaWNhdGlvblNlcnZpY2U6IG5vdGlmaWNhdGlvblNlcnZpY2UsXG4gICAgMjVcdFx0XHRhaVNlcnZpY2U6ICAgICAgICAgIGFpU2VydmljZSxcbiAgICAyNlx0XHRcdGFnb3JhU2VydmljZTogICAgICAgYWdvcmFTZXJ2aWNlLFxuICAgIDI3XHRcdH1cbiAgICAyOFx0fVxuLi4uXG4gICAgNjlcdFxuICAgIDcwXHQvLyBJbml0aWF0ZUN1c3RvbWVyQ2FsbCBzdGFydHMgYSBuZXcgY3VzdG9tZXItdG8tQUkgY2FsbCAoY3VzdG9tZXIgY2FsbHMgQUkpXG4gICAgNzFcdGZ1bmMgKGNtICpDYWxsTWFuYWdlcikgSW5pdGlhdGVDdXN0b21lckNhbGwocmVxdWVzdCBtb2RlbHMuSW5pdGlhdGVDdXN0b21lckNhbGxSZXF1ZXN0KSAoKm1vZGVscy5DYWxsLCBlcnJvcikge1xuICAgIDcyXHRcdGNtLm11dGV4LkxvY2soKVxuICAgIDczXHRcdGRlZmVyIGNtLm11dGV4LlVubG9jaygpXG4gICAgNzRcdFxuICAgIDc1XHRcdGxvZy5QcmludGYoXCLwn5OeIEluaXRpYXRpbmcgbmV3IGN1c3RvbWVyLXRvLUFJIGNhbGwgZm9yIHVzZXIgSUQ6ICVkLCBwdXJwb3NlOiAlc1wiLCByZXF1ZXN0LlVzZXJJRCwgcmVxdWVzdC5QdXJwb3NlKVxuICAgIDc2XHRcbiAgICA3N1x0XHQvLyBDcmVhdGUgbmV3IGN1c3RvbWVyIGNhbGxcbiAgICA3OFx0XHRjYWxsIDo9IG1vZGVscy5OZXdDdXN0b21lckNhbGwocmVxdWVzdC5Vc2VySUQsIHJlcXVlc3QuUHVycG9zZSwgcmVxdWVzdC5DdXN0b21lckluZm8pXG4gICAgNzlcdFxuICAgIDgwXHRcdC8vIFNldCBhZGRpdGlvbmFsIG1ldGFkYXRhXG4gICAgODFcdFx0aWYgcmVxdWVzdC5BSVBlcnNvbmFsaXR5ICE9IFwiXCIge1xuICAgIDgyXHRcdFx0Y2FsbC5NZXRhZGF0YS5BSVBlcnNvbmFsaXR5ID0gcmVxdWVzdC5BSVBlcnNvbmFsaXR5XG4gICAgODNcdFx0fVxuICAgIDg0XHRcdGlmIHJlcXVlc3QuTGFuZ3VhZ2UgIT0gXCJcIiB7XG4gICAgODVcdFx0XHRjYWxsLk1ldGFkYXRhLkxhbmd1YWdlID0gcmVxdWVzdC5MYW5ndWFnZVxuICAgIDg2XHRcdH1cbiAgICA4N1x0XHRpZiByZXF1ZXN0LkN1c3RvbUZpZWxkcyAhPSBuaWwge1xuICAgIDg4XHRcdFx0Y2FsbC5NZXRhZGF0YS5DdXN0b21GaWVsZHMgPSByZXF1ZXN0LkN1c3RvbUZpZWxkc1xuICAgIDg5XHRcdH1cbi4uLlxuUGF0aDogUkVBRE1FLm1kXG4gICAgIDFcdCMgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXG4gICAgIDJcdFxuICAgICAzXHRBIEdvIGJhY2tlbmQgc2VydmljZSB0aGF0IHByb3ZpZGVzIEFJLXBvd2VyZWQgY3VzdG9tZXIgc3VwcG9ydCB0aHJvdWdoIHZvaWNlIGNhbGxzIHVzaW5nIEFnb3JhIFJUQyBBUEkuIFRoZSBBSSBjYW4gaW5pdGlhdGUgY2FsbHMgdG8gY3VzdG9tZXJzIGFuZCBwcm92aWRlIHJlYWwtdGltZSB2b2ljZSBhc3Npc3RhbmNlLlxuICAgICA0XHRcbiAgICAgNVx0IyMg8J+agCBGZWF0dXJlc1xuLi4uXG4gICAgODRcdFxuICAgIDg1XHR8IE1ldGhvZCB8IEVuZHBvaW50IHwgRGVzY3JpcHRpb24gfFxuICAgIDg2XHR8LS0tLS0tLS18LS0tLS0tLS0tLXwtLS0tLS0tLS0tLS0tfFxuICAgIDg3XHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzL2luaXRpYXRlYCB8IPCfpJbinqHvuI/wn5GkIEluaXRpYXRlIEFJIGNhbGwgdG8gY3VzdG9tZXIgfFxuICAgIDg4XHR8IGBQT1NUYCB8IGAvYXBpL2NhbGxzL2N1c3RvbWVyLWluaXRpYXRlYCB8IPCfkaTinqHvuI/wn6SWIEN1c3RvbWVyIGluaXRpYXRlcyBjYWxsIHRvIEFJIHxcbiAgICA4OVx0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC9qb2luYCB8IEN1c3RvbWVyIGpvaW5zIGFuIGV4aXN0aW5nIGNhbGwgfFxuICAgIDkwXHR8IGBHRVRgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWRgIHwgR2V0IGNhbGwgZGV0YWlscyB8XG4gICAgOTFcdHwgYFBPU1RgIHwgYC9hcGkvY2FsbHMvOmNhbGxfaWQvZW5kYCB8IEVuZCBhIGNhbGwgfFxuICAgIDkyXHR8IGBHRVRgIHwgYC9hcGkvY2FsbHMvYWN0aXZlYCB8IExpc3QgYWxsIGFjdGl2ZSBjYWxscyB8XG4gICAgOTNcdHwgYEdFVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC9zdGF0c2AgfCBHZXQgY2FsbCBzdGF0aXN0aWNzIHxcbiAgICA5NFx0fCBgUE9TVGAgfCBgL2FwaS9jYWxscy86Y2FsbF9pZC90b2tlbmAgfCBHZW5lcmF0ZSBBZ29yYSB0b2tlbiB8XG4gICAgOTVcdFxuICAgIDk2XHQjIyDwn5OWIFVzYWdlIEV4YW1wbGVzXG4gICAgOTdcdFxuICAgIDk4XHQjIyMgMS4g8J+kluKeoe+4j/CfkaQgSW5pdGlhdGUgQUktdG8tQ3VzdG9tZXIgQ2FsbFxuLi4uXG5QYXRoOiBzZXJ2aWNlcy9haV9zZXJ2aWNlLmdvXG4uLi5cbiAgICA3OFx0XHRcbiAgICA3OVx0XHQvLyBNb2NrIHJlc3BvbnNlcyBiYXNlZCBvbiBhdWRpbyBsZW5ndGggdG8gc2ltdWxhdGUgcmVhbGlzdGljIGJlaGF2aW9yXG4gICAgODBcdFx0bW9ja1Jlc3BvbnNlcyA6PSBbXXN0cmluZ3tcbiAgICA4MVx0XHRcdFwiSGVsbG8sIEkgbmVlZCBoZWxwIHdpdGggbXkgYWNjb3VudFwiLFxuICAgIDgyXHRcdFx0XCJDYW4geW91IGhlbHAgbWUgcmVzZXQgbXkgcGFzc3dvcmQ/XCIsXG4gICAgODNcdFx0XHRcIkknbSBoYXZpbmcgdHJvdWJsZSB3aXRoIG15IGJpbGxpbmdcIixcbiAgICA4NFx0XHRcdFwiV2hhdCBhcmUgeW91ciBidXNpbmVzcyBob3Vycz9cIixcbiAgICA4NVx0XHRcdFwiSSB3YW50IHRvIGNhbmNlbCBteSBzdWJzY3JpcHRpb25cIixcbiAgICA4Nlx0XHRcdFwiSG93IGRvIEkgdXBkYXRlIG15IHBheW1lbnQgbWV0aG9kP1wiLFxuICAgIDg3XHRcdFx0XCJZZXMsIHRoYXQgc291bmRzIGdvb2RcIixcbiAgICA4OFx0XHRcdFwiTm8sIHRoYXQncyBub3Qgd2hhdCBJIG1lYW50XCIsXG4gICAgODlcdFx0XHRcIkNhbiB5b3UgcmVwZWF0IHRoYXQgcGxlYXNlP1wiLFxuICAgIDkwXHRcdFx0XCJUaGFuayB5b3UgZm9yIHlvdXIgaGVscFwiLFxuICAgIDkxXHRcdH1cbiAgICA5Mlx0XHRcbiAgICA5M1x0XHQvLyBTaW1wbGUgbW9jayBzZWxlY3Rpb24gYmFzZWQgb24gYXVkaW8gZGF0YSBsZW5ndGhcbiAgICA5NFx0XHRyZXNwb25zZUluZGV4IDo9IGxlbihhdWRpb0RhdGEpICUgbGVuKG1vY2tSZXNwb25zZXMpXG4gICAgOTVcdFx0XG4gICAgOTZcdFx0cmVzcG9uc2UgOj0gJlNUVFJlc3BvbnNle1xuICAgIDk3XHRcdFx0VGV4dDogICAgICAgbW9ja1Jlc3BvbnNlc1tyZXNwb25zZUluZGV4XSxcbiAgICA5OFx0XHRcdENvbmZpZGVuY2U6IDAuODUgKyBmbG9hdDY0KGxlbihhdWRpb0RhdGEpJTE1KS8xMDAsIC8vIE1vY2sgY29uZmlkZW5jZSBiZXR3ZWVuIDAuODUtMS4wXG4gICAgOTlcdFx0XHRMYW5ndWFnZTogICBcImVuXCIsXG4gICAxMDBcdFx0fVxuICAgMTAxXHRcdFxuICAgMTAyXHRcdGxvZy5QcmludGYoXCLwn5OdIFtNT0NLXSBTVFQgUmVzdWx0OiAnJXMnIChjb25maWRlbmNlOiAlLjJmKVwiLCByZXNwb25zZS5UZXh0LCByZXNwb25zZS5Db25maWRlbmNlKVxuICAgMTAzXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAxMDRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdC8vIEdlbmVyYXRlQUlSZXNwb25zZSBnZW5lcmF0ZXMgQUkgcmVzcG9uc2UgdXNpbmcgTExNXG4gICAxMDdcdGZ1bmMgKGFpICpBSVNlcnZpY2UpIEdlbmVyYXRlQUlSZXNwb25zZShjYWxsICptb2RlbHMuQ2FsbCwgdXNlck1lc3NhZ2Ugc3RyaW5nKSAoc3RyaW5nLCBlcnJvcikge1xuICAgMTA4XHRcdGxvZy5QcmludGYoXCLwn6SWIFByb2Nlc3NpbmcgQUkgcmVzcG9uc2UgZm9yIGNhbGwgJXNcIiwgY2FsbC5JRClcbiAgIDEwOVx0XHRcbiAgIDExMFx0XHQvLyBCdWlsZCBjb252ZXJzYXRpb24gY29udGV4dFxuICAgMTExXHRcdG1lc3NhZ2VzIDo9IGFpLmJ1aWxkQ29udmVyc2F0aW9uQ29udGV4dChjYWxsLCB1c2VyTWVzc2FnZSlcbiAgIDExMlx0XHRcbiAgIDExM1x0XHQvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY2FsbCBPcGVuQUkgQVBJXG4gICAxMTRcdFx0aWYgYWkuY29uZmlnLkFJLk9wZW5BSUFQSUtleSAhPSBcIlwiIHtcbiAgIDExNVx0XHRcdHJldHVybiBhaS5jYWxsT3BlbkFJQVBJKG1lc3NhZ2VzKVxuICAgMTE2XHRcdH1cbiAgIDExN1x0XHRcbiAgIDExOFx0XHQvLyBNb2NrIEFJIHJlc3BvbnNlXG4gICAxMTlcdFx0cmV0dXJuIGFpLmdlbmVyYXRlTW9ja0FJUmVzcG9uc2UodXNlck1lc3NhZ2UsIGNhbGwuTWV0YWRhdGEuUHVycG9zZSlcbiAgIDEyMFx0fVxuLi4uXG4gICAyNTlcdFxuICAgMjYwXHQvLyBQcm9jZXNzVkFEIHBlcmZvcm1zIFZvaWNlIEFjdGl2aXR5IERldGVjdGlvblxuICAgMjYxXHRmdW5jIChhaSAqQUlTZXJ2aWNlKSBQcm9jZXNzVkFEKGF1ZGlvRGF0YSBbXWJ5dGUpIChib29sLCBlcnJvcikge1xuICAgMjYyXHRcdGlmICFhaS5jb25maWcuQUkuRW5hYmxlVkFEIHtcbiAgIDI2M1x0XHRcdHJldHVybiB0cnVlLCBuaWwgLy8gQWx3YXlzIGNvbnNpZGVyIGFzIHNwZWVjaCBpZiBWQUQgaXMgZGlzYWJsZWRcbiAgIDI2NFx0XHR9XG4gICAyNjVcdFx0XG4gICAyNjZcdFx0Ly8gTW9jayBWQUQgcHJvY2Vzc2luZ1xuICAgMjY3XHRcdC8vIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgdXNlIEFnb3JhJ3MgVkFEIG9yIG90aGVyIFZBRCBhbGdvcml0aG1zXG4gICAyNjhcdFx0XG4gICAyNjlcdFx0Ly8gU2ltcGxlIG1vY2s6IGNvbnNpZGVyIGl0IHNwZWVjaCBpZiBhdWRpbyBkYXRhIGhhcyBzdWZmaWNpZW50IFwiZW5lcmd5XCJcbiAgIDI3MFx0XHRpZiBsZW4oYXVkaW9EYXRhKSA8IDEwMCB7XG4gICAyNzFcdFx0XHRyZXR1cm4gZmFsc2UsIG5pbCAvLyBUb28gc2hvcnQgdG8gYmUgc3BlZWNoXG4gICAyNzJcdFx0fVxuICAgMjczXHRcdFxuICAgMjc0XHRcdC8vIE1vY2sgZW5lcmd5IGNhbGN1bGF0aW9uXG4gICAyNzVcdFx0dmFyIGVuZXJneSBpbnQ2NFxuICAgMjc2XHRcdGZvciBfLCBzYW1wbGUgOj0gcmFuZ2UgYXVkaW9EYXRhIHtcbiAgIDI3N1x0XHRcdGVuZXJneSArPSBpbnQ2NChzYW1wbGUgKiBzYW1wbGUpXG4gICAyNzhcdFx0fVxuICAgMjc5XHRcdFxuICAgMjgwXHRcdGF2Z0VuZXJneSA6PSBmbG9hdDY0KGVuZXJneSkgLyBmbG9hdDY0KGxlbihhdWRpb0RhdGEpKVxuICAgMjgxXHRcdHRocmVzaG9sZCA6PSAxMDAwLjAgKiBhaS5jb25maWcuQUkuVkFEU2Vuc2l0aXZpdHlcbiAgIDI4Mlx0XHRcbiAgIDI4M1x0XHRpc1NwZWVjaCA6PSBhdmdFbmVyZ3kgPiB0aHJlc2hvbGRcbiAgIDI4NFx0XHRsb2cuUHJpbnRmKFwi8J+Ome+4jyBbTU9DS10gVkFEOiBFbmVyZ3k9JS4yZiwgVGhyZXNob2xkPSUuMmYsIElzU3BlZWNoPSV2XCIsIGF2Z0VuZXJneSwgdGhyZXNob2xkLCBpc1NwZWVjaClcbiAgIDI4NVx0XHRcbiAgIDI4Nlx0XHRyZXR1cm4gaXNTcGVlY2gsIG5pbFxuICAgMjg3XHR9XG4uLi5cblBhdGg6IHNlcnZpY2VzL2Fnb3JhX3NlcnZpY2UuZ29cbi4uLlxuICAgIDYwXHRcbiAgICA2MVx0XHRpZiBhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUgIT0gXCJcIiB7XG4gICAgNjJcdFx0XHQvLyBHZW5lcmF0ZSByZWFsIHRva2VuIHdpdGggb2ZmaWNpYWwgQWdvcmEgbGlicmFyeVxuICAgIDYzXHRcdFx0dG9rZW4sIGVyciA9IGFzLmdlbmVyYXRlUmVhbFRva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUsIHVpbnQzMihleHBpcmF0aW9uVGltZSkpXG4gICAgNjRcdFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA2NVx0XHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlbjogJXdcIiwgZXJyKVxuICAgIDY2XHRcdFx0fVxuICAgIDY3XHRcdFx0bG9nLlByaW50ZihcIuKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXMsIHRva2VuIGlzOiAlc1wiLCBjaGFubmVsTmFtZSwgdG9rZW4pXG4gICAgNjhcdFx0fSBlbHNlIHtcbiAgICA2OVx0XHRcdC8vIEdlbmVyYXRlIG1vY2sgdG9rZW4gZm9yIGRldmVsb3BtZW50XG4gICAgNzBcdFx0XHR0b2tlbiA9IGFzLmdlbmVyYXRlTW9ja1Rva2VuKGNoYW5uZWxOYW1lLCB1c2VySUQsIHJvbGUpXG4gICAgNzFcdFx0XHRsb2cuUHJpbnRmKFwi4pqg77iPIFtNT0NLXSBVc2luZyBtb2NrIHRva2VuIChubyBhcHAgY2VydGlmaWNhdGUgcHJvdmlkZWQpXCIpXG4gICAgNzJcdFx0fVxuICAgIDczXHRcbiAgICA3NFx0XHRyZXNwb25zZSA6PSAmVG9rZW5SZXNwb25zZXtcbiAgICA3NVx0XHRcdFRva2VuOiAgICAgdG9rZW4sXG4gICAgNzZcdFx0XHRFeHBpcmVzQXQ6IGV4cGlyYXRpb25UaW1lLFxuICAgIDc3XHRcdH1cbiAgICA3OFx0XG4gICAgNzlcdFx0bG9nLlByaW50ZihcIuKchSBUb2tlbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5IGZvciBjaGFubmVsOiAlc1wiLCBjaGFubmVsTmFtZSlcbiAgICA4MFx0XHRyZXR1cm4gcmVzcG9uc2UsIG5pbFxuICAgIDgxXHR9XG4uLi5cbiAgICA5Mlx0XG4gICAgOTNcdFx0aWYgYXMuY29uZmlnLkFnb3JhLkFwcENlcnRpZmljYXRlICE9IFwiXCIge1xuICAgIDk0XHRcdFx0Ly8gR2VuZXJhdGUgcmVhbCB0b2tlbiB3aXRoIG9mZmljaWFsIEFnb3JhIGxpYnJhcnkgdXNpbmcgVUlEIGRpcmVjdGx5XG4gICAgOTVcdFx0XHR0b2tlbiwgZXJyID0gYXMuZ2VuZXJhdGVSZWFsVG9rZW5XaXRoVUlEKGNoYW5uZWxOYW1lLCB1aWQsIHJvbGUsIHVpbnQzMihleHBpcmF0aW9uVGltZSkpXG4gICAgOTZcdFx0XHRpZiBlcnIgIT0gbmlsIHtcbiAgICA5N1x0XHRcdFx0cmV0dXJuIG5pbCwgZm10LkVycm9yZihcImZhaWxlZCB0byBnZW5lcmF0ZSB0b2tlbjogJXdcIiwgZXJyKVxuICAgIDk4XHRcdFx0fVxuICAgIDk5XHRcdFx0bG9nLlByaW50ZihcIuKchSBHZW5lcmF0ZWQgcmVhbCBBZ29yYSB0b2tlbiBmb3IgY2hhbm5lbDogJXMsIFVJRDogJWQsIHRva2VuOiAlc1wiLCBjaGFubmVsTmFtZSwgdWlkLCB0b2tlbilcbiAgIDEwMFx0XHR9IGVsc2Uge1xuICAgMTAxXHRcdFx0Ly8gR2VuZXJhdGUgbW9jayB0b2tlbiBmb3IgZGV2ZWxvcG1lbnRcbiAgIDEwMlx0XHRcdHRva2VuID0gYXMuZ2VuZXJhdGVNb2NrVG9rZW5XaXRoVUlEKGNoYW5uZWxOYW1lLCB1aWQsIHJvbGUpXG4gICAxMDNcdFx0XHRsb2cuUHJpbnRmKFwi4pqg77iPIFtNT0NLXSBVc2luZyBtb2NrIHRva2VuIChubyBhcHAgY2VydGlmaWNhdGUgcHJvdmlkZWQpXCIpXG4gICAxMDRcdFx0fVxuICAgMTA1XHRcbiAgIDEwNlx0XHRyZXNwb25zZSA6PSAmVG9rZW5SZXNwb25zZXtcbiAgIDEwN1x0XHRcdFRva2VuOiAgICAgdG9rZW4sXG4gICAxMDhcdFx0XHRFeHBpcmVzQXQ6IGV4cGlyYXRpb25UaW1lLFxuICAgMTA5XHRcdH1cbiAgIDExMFx0XG4gICAxMTFcdFx0bG9nLlByaW50ZihcIuKchSBUb2tlbiBnZW5lcmF0ZWQgc3VjY2Vzc2Z1bGx5IGZvciBjaGFubmVsOiAlcywgVUlEOiAlZFwiLCBjaGFubmVsTmFtZSwgdWlkKVxuICAgMTEyXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAxMTNcdH1cbi4uLlxuICAgMTU1XHRcbiAgIDE1Nlx0Ly8gZ2VuZXJhdGVSZWFsVG9rZW5XaXRoVUlEIGdlbmVyYXRlcyBhIHJlYWwgQWdvcmEgdG9rZW4gdXNpbmcgVUlEIGRpcmVjdGx5XG4gICAxNTdcdGZ1bmMgKGFzICpBZ29yYVNlcnZpY2UpIGdlbmVyYXRlUmVhbFRva2VuV2l0aFVJRChjaGFubmVsTmFtZSBzdHJpbmcsIHVpZCB1aW50MzIsIHJvbGUgaW50LCBleHBpcmF0aW9uIHVpbnQzMikgKHN0cmluZywgZXJyb3IpIHtcbiAgIDE1OFx0XHQvLyBDb252ZXJ0IHJvbGUgdG8gQWdvcmEgcm9sZSB0eXBlXG4gICAxNTlcdFx0dmFyIGFnb3JhUm9sZSBydGN0b2tlbmJ1aWxkZXIyLlJvbGVcbiAgIDE2MFx0XHRpZiByb2xlID09IDEge1xuICAgMTYxXHRcdFx0YWdvcmFSb2xlID0gcnRjdG9rZW5idWlsZGVyMi5Sb2xlUHVibGlzaGVyXG4gICAxNjJcdFx0fSBlbHNlIHtcbiAgIDE2M1x0XHRcdGFnb3JhUm9sZSA9IHJ0Y3Rva2VuYnVpbGRlcjIuUm9sZVN1YnNjcmliZXJcbiAgIDE2NFx0XHR9XG4gICAxNjVcdFxuICAgMTY2XHRcdC8vIEdlbmVyYXRlIHRva2VuIHVzaW5nIG9mZmljaWFsIEFnb3JhIGxpYnJhcnkgd2l0aCBVSUQgZGlyZWN0bHlcbiAgIDE2N1x0XHR0b2tlbiwgZXJyIDo9IHJ0Y3Rva2VuYnVpbGRlcjIuQnVpbGRUb2tlbldpdGhVaWQoXG4gICAxNjhcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwSUQsXG4gICAxNjlcdFx0XHRhcy5jb25maWcuQWdvcmEuQXBwQ2VydGlmaWNhdGUsXG4gICAxNzBcdFx0XHRjaGFubmVsTmFtZSxcbiAgIDE3MVx0XHRcdHVpZCxcbiAgIDE3Mlx0XHRcdGFnb3JhUm9sZSxcbiAgIDE3M1x0XHRcdGV4cGlyYXRpb24sXG4gICAxNzRcdFx0XHRleHBpcmF0aW9uLCAvLyBQcml2aWxlZ2UgZXhwaXJlZCB0aW1lc3RhbXAgKHNhbWUgYXMgdG9rZW4gZXhwaXJhdGlvbilcbiAgIDE3NVx0XHQpXG4gICAxNzZcdFxuICAgMTc3XHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTc4XHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gYnVpbGQgQWdvcmEgdG9rZW46ICV3XCIsIGVycilcbiAgIDE3OVx0XHR9XG4uLi5cbiAgIDE5NVx0XG4gICAxOTZcdC8vIEpvaW5DaGFubmVsIGpvaW5zIGFuIEFnb3JhIGNoYW5uZWwgZm9yIEFJIHByb2Nlc3NpbmdcbiAgIDE5N1x0ZnVuYyAoYXMgKkFnb3JhU2VydmljZSkgSm9pbkNoYW5uZWwoY2hhbm5lbE5hbWUsIHVzZXJJRCBzdHJpbmcpIGVycm9yIHtcbiAgIDE5OFx0XHRsb2cuUHJpbnRmKFwi8J+UlyBbTU9DS10gQUkgam9pbmluZyBBZ29yYSBjaGFubmVsOiAlcyBhcyB1c2VyOiAlc1wiLCBjaGFubmVsTmFtZSwgdXNlcklEKVxuICAgMTk5XHRcbiAgIDIwMFx0XHQvLyBJbiByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkOlxuICAgMjAxXHRcdC8vIDEuIENyZWF0ZSBSVEMgY29ubmVjdGlvblxuICAgMjAyXHRcdC8vIDIuIFNldCB1cCBsb2NhbCB1c2VyXG4gICAyMDNcdFx0Ly8gMy4gQ3JlYXRlIGF1ZGlvIHRyYWNrIGZvciBBSSB2b2ljZVxuICAgMjA0XHRcdC8vIDQuIFJlZ2lzdGVyIGF1ZGlvIGZyYW1lIG9ic2VydmVycyBmb3IgcHJvY2Vzc2luZyBpbmNvbWluZyBhdWRpb1xuICAgMjA1XHRcdC8vIDUuIEpvaW4gdGhlIGNoYW5uZWxcbiAgIDIwNlx0XHQvLyA2LiBTdGFydCBhdWRpbyBwcm9jZXNzaW5nIHBpcGVsaW5lXG4gICAyMDdcdFxuICAgMjA4XHRcdC8vIE1vY2sgaW1wbGVtZW50YXRpb25cbiAgIDIwOVx0XHR0aW1lLlNsZWVwKDUwMCAqIHRpbWUuTWlsbGlzZWNvbmQpIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gdGltZVxuICAgMjEwXHRcbiAgIDIxMVx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBBSSBzdWNjZXNzZnVsbHkgam9pbmVkIGNoYW5uZWw6ICVzXCIsIGNoYW5uZWxOYW1lKVxuICAgMjEyXHRcdHJldHVybiBuaWxcbiAgIDIxM1x0fVxuLi4uXG5QYXRoOiBtb2RlbHMvY2FsbC5nb1xuLi4uXG4gICAgMjhcdFxuICAgIDI5XHQvLyBDYWxsIHJlcHJlc2VudHMgYSBjdXN0b21lciBzdXBwb3J0IGNhbGwgc2Vzc2lvblxuICAgIDMwXHR0eXBlIENhbGwgc3RydWN0IHtcbiAgICAzMVx0XHRJRCAgICAgICAgICBzdHJpbmcgICAgICAgYGpzb246XCJpZFwiYFxuICAgIDMyXHRcdEN1c3RvbWVySUQgIHN0cmluZyAgICAgICBganNvbjpcImN1c3RvbWVyX2lkXCJgXG4gICAgMzNcdFx0Q2hhbm5lbE5hbWUgc3RyaW5nICAgICAgIGBqc29uOlwiY2hhbm5lbF9uYW1lXCJgXG4gICAgMzRcdFx0U3RhdHVzICAgICAgQ2FsbFN0YXR1cyAgIGBqc29uOlwic3RhdHVzXCJgXG4gICAgMzVcdFx0VHlwZSAgICAgICAgQ2FsbFR5cGUgICAgIGBqc29uOlwidHlwZVwiYFxuICAgIDM2XHRcdFN0YXJ0VGltZSAgIHRpbWUuVGltZSAgICBganNvbjpcInN0YXJ0X3RpbWVcImBcbiAgICAzN1x0XHRFbmRUaW1lICAgICAqdGltZS5UaW1lICAgYGpzb246XCJlbmRfdGltZSxvbWl0ZW1wdHlcImBcbiAgICAzOFx0XHREdXJhdGlvbiAgICBpbnQ2NCAgICAgICAgYGpzb246XCJkdXJhdGlvblwiYCAvLyBpbiBzZWNvbmRzXG4gICAgMzlcdFx0VHJhbnNjcmlwdCAgW11NZXNzYWdlICAgIGBqc29uOlwidHJhbnNjcmlwdFwiYFxuICAgIDQwXHRcdE1ldGFkYXRhICAgIENhbGxNZXRhZGF0YSBganNvbjpcIm1ldGFkYXRhXCJgXG4gICAgNDFcdH1cbiAgICA0Mlx0XG4gICAgNDNcdC8vIE1lc3NhZ2UgcmVwcmVzZW50cyBhIHNpbmdsZSBtZXNzYWdlIGluIHRoZSBjb252ZXJzYXRpb25cbiAgICA0NFx0dHlwZSBNZXNzYWdlIHN0cnVjdCB7XG4gICAgNDVcdFx0SUQgICAgICAgIHN0cmluZyAgICBganNvbjpcImlkXCJgXG4gICAgNDZcdFx0U3BlYWtlciAgIHN0cmluZyAgICBganNvbjpcInNwZWFrZXJcImAgLy8gXCJhaVwiIG9yIFwiY3VzdG9tZXJcIlxuICAgIDQ3XHRcdENvbnRlbnQgICBzdHJpbmcgICAgYGpzb246XCJjb250ZW50XCJgXG4gICAgNDhcdFx0VGltZXN0YW1wIHRpbWUuVGltZSBganNvbjpcInRpbWVzdGFtcFwiYFxuICAgIDQ5XHRcdFR5cGUgICAgICBzdHJpbmcgICAgYGpzb246XCJ0eXBlXCJgIC8vIFwidGV4dFwiLCBcImF1ZGlvXCJcbiAgICA1MFx0fVxuICAgIDUxXHRcbiAgICA1Mlx0Ly8gQ2FsbE1ldGFkYXRhIGNvbnRhaW5zIGFkZGl0aW9uYWwgaW5mb3JtYXRpb24gYWJvdXQgdGhlIGNhbGxcbiAgICA1M1x0dHlwZSBDYWxsTWV0YWRhdGEgc3RydWN0IHtcbiAgICA1NFx0XHRQdXJwb3NlICAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwicHVycG9zZVwiYFxuICAgIDU1XHRcdEN1c3RvbWVySW5mbyAgQ3VzdG9tZXJJbmZvICAgICAgYGpzb246XCJjdXN0b21lcl9pbmZvXCJgXG4gICAgNTZcdFx0QUlQZXJzb25hbGl0eSBzdHJpbmcgICAgICAgICAgICBganNvbjpcImFpX3BlcnNvbmFsaXR5XCJgXG4gICAgNTdcdFx0TGFuZ3VhZ2UgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcImxhbmd1YWdlXCJgXG4gICAgNThcdFx0VGFncyAgICAgICAgICBbXXN0cmluZyAgICAgICAgICBganNvbjpcInRhZ3NcImBcbiAgICA1OVx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgIDYwXHR9XG4uLi5cbiAgIDE0MFx0XG4gICAxNDFcdC8vIE5ld0NhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlXG4gICAxNDJcdGZ1bmMgTmV3Q2FsbChjdXN0b21lcklELCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTQzXHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNDRcdFx0Y2hhbm5lbE5hbWUgOj0gXCJhaV9zdXBwb3J0X1wiICsgY2FsbElEXG4gICAxNDVcdFxuICAgMTQ2XHRcdHJldHVybiAmQ2FsbHtcbiAgIDE0N1x0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNDhcdFx0XHRDdXN0b21lcklEOiAgY3VzdG9tZXJJRCxcbiAgIDE0OVx0XHRcdENoYW5uZWxOYW1lOiBjaGFubmVsTmFtZSxcbiAgIDE1MFx0XHRcdFN0YXR1czogICAgICBDYWxsU3RhdHVzUGVuZGluZyxcbiAgIDE1MVx0XHRcdFR5cGU6ICAgICAgICBDYWxsVHlwZUFJVG9DdXN0b21lciwgLy8gRGVmYXVsdCB0byBBSSBjYWxsaW5nIGN1c3RvbWVyXG4gICAxNTJcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE1M1x0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNTRcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTU1XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTU2XHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxNTdcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTU4XHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxNTlcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTYwXHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE2MVx0XHRcdH0sXG4gICAxNjJcdFx0fVxuICAgMTYzXHR9XG4gICAxNjRcdFxuICAgMTY1XHQvLyBOZXdDdXN0b21lckNhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlIHdoZXJlIGN1c3RvbWVyIGNhbGxzIEFJXG4gICAxNjZcdGZ1bmMgTmV3Q3VzdG9tZXJDYWxsKHVzZXJJRCBpbnQsIHB1cnBvc2Ugc3RyaW5nLCBjdXN0b21lckluZm8gQ3VzdG9tZXJJbmZvKSAqQ2FsbCB7XG4gICAxNjdcdFx0Y2FsbElEIDo9IHV1aWQuTmV3KCkuU3RyaW5nKClcbiAgIDE2OFx0XHRjaGFubmVsTmFtZSA6PSBcImN1c3RvbWVyX2FpX1wiICsgY2FsbElEXG4gICAxNjlcdFxuICAgMTcwXHRcdHJldHVybiAmQ2FsbHtcbiAgIDE3MVx0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNzJcdFx0XHRDdXN0b21lcklEOiAgZm10LlNwcmludGYoXCJ1c2VyXyVkXCIsIHVzZXJJRCksIC8vIENvbnZlcnQgaW50IHVzZXJJRCB0byBzdHJpbmcgZm9yIGludGVybmFsIHVzZVxuICAgMTczXHRcdFx0Q2hhbm5lbE5hbWU6IGNoYW5uZWxOYW1lLFxuICAgMTc0XHRcdFx0U3RhdHVzOiAgICAgIENhbGxTdGF0dXNQZW5kaW5nLFxuICAgMTc1XHRcdFx0VHlwZTogICAgICAgIENhbGxUeXBlQ3VzdG9tZXJUb0FJLCAvLyBDdXN0b21lciBjYWxsaW5nIEFJXG4gICAxNzZcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE3N1x0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNzhcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTc5XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTgwXHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxODFcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTgyXHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxODNcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTg0XHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE4NVx0XHRcdH0sXG4gICAxODZcdFx0fVxuICAgMTg3XHR9XG4uLi5cbiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjQxOTM5ZDljLTljYTQtNDYwNy1iNmMzLTg2OGNlNmFiOGJjMTt0b29sdV92cnR4XzAxWVBMREhqZGZwU2lubkgzVlp2YW1YcCI6eyJyZXF1ZXN0SWQiOiI0MTkzOWQ5Yy05Y2E0LTQ2MDctYjZjMy04NjhjZTZhYjhiYzEiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxWVBMREhqZGZwU2lubkgzVlp2YW1YcCIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IHNlcnZpY2VzL2FpX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvY29uZmlnXCJcbiAgICAgNVx0XHRcIkJhY2tlbmQvbW9kZWxzXCJcbiAgICAgNlx0XHRcImJ5dGVzXCJcbiAgICAgN1x0XHRcImVuY29kaW5nL2pzb25cIlxuICAgICA4XHRcdFwiZm10XCJcbiAgICAgOVx0XHRcImlvXCJcbiAgICAxMFx0XHRcImxvZ1wiXG4gICAgMTFcdFx0XCJuZXQvaHR0cFwiXG4gICAgMTJcdFx0XCJzdHJpbmdzXCJcbiAgICAxM1x0XHRcInRpbWVcIlxuICAgIDE0XHQpXG4gICAgMTVcdFxuICAgIDE2XHQvLyBBSVNlcnZpY2UgaGFuZGxlcyBhbGwgQUktcmVsYXRlZCBvcGVyYXRpb25zXG4gICAgMTdcdHR5cGUgQUlTZXJ2aWNlIHN0cnVjdCB7XG4gICAgMThcdFx0Y29uZmlnICpjb25maWcuQ29uZmlnXG4gICAgMTlcdFx0Y2xpZW50ICpodHRwLkNsaWVudFxuICAgIDIwXHR9XG4gICAgMjFcdFxuICAgIDIyXHQvLyBTVFRSZXNwb25zZSByZXByZXNlbnRzIHNwZWVjaC10by10ZXh0IHJlc3BvbnNlXG4gICAgMjNcdHR5cGUgU1RUUmVzcG9uc2Ugc3RydWN0IHtcbiAgICAyNFx0XHRUZXh0ICAgICAgIHN0cmluZyAgYGpzb246XCJ0ZXh0XCJgXG4gICAgMjVcdFx0Q29uZmlkZW5jZSBmbG9hdDY0IGBqc29uOlwiY29uZmlkZW5jZVwiYFxuICAgIDI2XHRcdExhbmd1YWdlICAgc3RyaW5nICBganNvbjpcImxhbmd1YWdlXCJgXG4gICAgMjdcdH1cbiAgICAyOFx0XG4gICAgMjlcdC8vIExMTVJlcXVlc3QgcmVwcmVzZW50cyBhIHJlcXVlc3QgdG8gdGhlIGxhbmd1YWdlIG1vZGVsXG4gICAgMzBcdHR5cGUgTExNUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDMxXHRcdE1vZGVsICAgIHN0cmluZyAgICBganNvbjpcIm1vZGVsXCJgXG4gICAgMzJcdFx0TWVzc2FnZXMgW11MTE1NZXNzYWdlIGBqc29uOlwibWVzc2FnZXNcImBcbiAgICAzM1x0XHRUZW1wZXJhdHVyZSBmbG9hdDY0IGBqc29uOlwidGVtcGVyYXR1cmVcImBcbiAgICAzNFx0XHRNYXhUb2tlbnMgICBpbnQgICAgIGBqc29uOlwibWF4X3Rva2Vuc1wiYFxuICAgIDM1XHR9XG4gICAgMzZcdFxuICAgIDM3XHQvLyBMTE1NZXNzYWdlIHJlcHJlc2VudHMgYSBtZXNzYWdlIGluIHRoZSBjb252ZXJzYXRpb25cbiAgICAzOFx0dHlwZSBMTE1NZXNzYWdlIHN0cnVjdCB7XG4gICAgMzlcdFx0Um9sZSAgICBzdHJpbmcgYGpzb246XCJyb2xlXCJgXG4gICAgNDBcdFx0Q29udGVudCBzdHJpbmcgYGpzb246XCJjb250ZW50XCJgXG4gICAgNDFcdH1cbiAgICA0Mlx0XG4gICAgNDNcdC8vIExMTVJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIGxhbmd1YWdlIG1vZGVsIHJlc3BvbnNlXG4gICAgNDRcdHR5cGUgTExNUmVzcG9uc2Ugc3RydWN0IHtcbiAgICA0NVx0XHRDaG9pY2VzIFtdc3RydWN0IHtcbiAgICA0Nlx0XHRcdE1lc3NhZ2UgTExNTWVzc2FnZSBganNvbjpcIm1lc3NhZ2VcImBcbiAgICA0N1x0XHR9IGBqc29uOlwiY2hvaWNlc1wiYFxuICAgIDQ4XHR9XG4gICAgNDlcdFxuICAgIDUwXHQvLyBUVFNSZXF1ZXN0IHJlcHJlc2VudHMgdGV4dC10by1zcGVlY2ggcmVxdWVzdFxuICAgIDUxXHR0eXBlIFRUU1JlcXVlc3Qgc3RydWN0IHtcbiAgICA1Mlx0XHRNb2RlbCBzdHJpbmcgYGpzb246XCJtb2RlbFwiYFxuICAgIDUzXHRcdElucHV0IHN0cmluZyBganNvbjpcImlucHV0XCJgXG4gICAgNTRcdFx0Vm9pY2Ugc3RyaW5nIGBqc29uOlwidm9pY2VcImBcbiAgICA1NVx0fVxuICAgIDU2XHRcbiAgICA1N1x0Ly8gTmV3QUlTZXJ2aWNlIGNyZWF0ZXMgYSBuZXcgQUkgc2VydmljZSBpbnN0YW5jZVxuICAgIDU4XHRmdW5jIE5ld0FJU2VydmljZShjZmcgKmNvbmZpZy5Db25maWcpICpBSVNlcnZpY2Uge1xuICAgIDU5XHRcdHJldHVybiAmQUlTZXJ2aWNle1xuICAgIDYwXHRcdFx0Y29uZmlnOiBjZmcsXG4gICAgNjFcdFx0XHRjbGllbnQ6ICZodHRwLkNsaWVudHtcbiAgICA2Mlx0XHRcdFx0VGltZW91dDogMzAgKiB0aW1lLlNlY29uZCxcbiAgICA2M1x0XHRcdH0sXG4gICAgNjRcdFx0fVxuICAgIDY1XHR9XG4uLi5cbiAgICA3OFx0XHRcbiAgICA3OVx0XHQvLyBNb2NrIHJlc3BvbnNlcyBiYXNlZCBvbiBhdWRpbyBsZW5ndGggdG8gc2ltdWxhdGUgcmVhbGlzdGljIGJlaGF2aW9yXG4gICAgODBcdFx0bW9ja1Jlc3BvbnNlcyA6PSBbXXN0cmluZ3tcbiAgICA4MVx0XHRcdFwiSGVsbG8sIEkgbmVlZCBoZWxwIHdpdGggbXkgYWNjb3VudFwiLFxuICAgIDgyXHRcdFx0XCJDYW4geW91IGhlbHAgbWUgcmVzZXQgbXkgcGFzc3dvcmQ/XCIsXG4gICAgODNcdFx0XHRcIkknbSBoYXZpbmcgdHJvdWJsZSB3aXRoIG15IGJpbGxpbmdcIixcbiAgICA4NFx0XHRcdFwiV2hhdCBhcmUgeW91ciBidXNpbmVzcyBob3Vycz9cIixcbiAgICA4NVx0XHRcdFwiSSB3YW50IHRvIGNhbmNlbCBteSBzdWJzY3JpcHRpb25cIixcbiAgICA4Nlx0XHRcdFwiSG93IGRvIEkgdXBkYXRlIG15IHBheW1lbnQgbWV0aG9kP1wiLFxuICAgIDg3XHRcdFx0XCJZZXMsIHRoYXQgc291bmRzIGdvb2RcIixcbiAgICA4OFx0XHRcdFwiTm8sIHRoYXQncyBub3Qgd2hhdCBJIG1lYW50XCIsXG4gICAgODlcdFx0XHRcIkNhbiB5b3UgcmVwZWF0IHRoYXQgcGxlYXNlP1wiLFxuICAgIDkwXHRcdFx0XCJUaGFuayB5b3UgZm9yIHlvdXIgaGVscFwiLFxuICAgIDkxXHRcdH1cbiAgICA5Mlx0XHRcbiAgICA5M1x0XHQvLyBTaW1wbGUgbW9jayBzZWxlY3Rpb24gYmFzZWQgb24gYXVkaW8gZGF0YSBsZW5ndGhcbiAgICA5NFx0XHRyZXNwb25zZUluZGV4IDo9IGxlbihhdWRpb0RhdGEpICUgbGVuKG1vY2tSZXNwb25zZXMpXG4gICAgOTVcdFx0XG4gICAgOTZcdFx0cmVzcG9uc2UgOj0gJlNUVFJlc3BvbnNle1xuICAgIDk3XHRcdFx0VGV4dDogICAgICAgbW9ja1Jlc3BvbnNlc1tyZXNwb25zZUluZGV4XSxcbiAgICA5OFx0XHRcdENvbmZpZGVuY2U6IDAuODUgKyBmbG9hdDY0KGxlbihhdWRpb0RhdGEpJTE1KS8xMDAsIC8vIE1vY2sgY29uZmlkZW5jZSBiZXR3ZWVuIDAuODUtMS4wXG4gICAgOTlcdFx0XHRMYW5ndWFnZTogICBcImVuXCIsXG4gICAxMDBcdFx0fVxuICAgMTAxXHRcdFxuICAgMTAyXHRcdGxvZy5QcmludGYoXCLwn5OdIFtNT0NLXSBTVFQgUmVzdWx0OiAnJXMnIChjb25maWRlbmNlOiAlLjJmKVwiLCByZXNwb25zZS5UZXh0LCByZXNwb25zZS5Db25maWRlbmNlKVxuICAgMTAzXHRcdHJldHVybiByZXNwb25zZSwgbmlsXG4gICAxMDRcdH1cbiAgIDEwNVx0XG4gICAxMDZcdC8vIEdlbmVyYXRlQUlSZXNwb25zZSBnZW5lcmF0ZXMgQUkgcmVzcG9uc2UgdXNpbmcgTExNXG4gICAxMDdcdGZ1bmMgKGFpICpBSVNlcnZpY2UpIEdlbmVyYXRlQUlSZXNwb25zZShjYWxsICptb2RlbHMuQ2FsbCwgdXNlck1lc3NhZ2Ugc3RyaW5nKSAoc3RyaW5nLCBlcnJvcikge1xuICAgMTA4XHRcdGxvZy5QcmludGYoXCLwn6SWIFByb2Nlc3NpbmcgQUkgcmVzcG9uc2UgZm9yIGNhbGwgJXNcIiwgY2FsbC5JRClcbiAgIDEwOVx0XHRcbiAgIDExMFx0XHQvLyBCdWlsZCBjb252ZXJzYXRpb24gY29udGV4dFxuICAgMTExXHRcdG1lc3NhZ2VzIDo9IGFpLmJ1aWxkQ29udmVyc2F0aW9uQ29udGV4dChjYWxsLCB1c2VyTWVzc2FnZSlcbiAgIDExMlx0XHRcbiAgIDExM1x0XHQvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY2FsbCBPcGVuQUkgQVBJXG4gICAxMTRcdFx0aWYgYWkuY29uZmlnLkFJLk9wZW5BSUFQSUtleSAhPSBcIlwiIHtcbiAgIDExNVx0XHRcdHJldHVybiBhaS5jYWxsT3BlbkFJQVBJKG1lc3NhZ2VzKVxuICAgMTE2XHRcdH1cbiAgIDExN1x0XHRcbiAgIDExOFx0XHQvLyBNb2NrIEFJIHJlc3BvbnNlXG4gICAxMTlcdFx0cmV0dXJuIGFpLmdlbmVyYXRlTW9ja0FJUmVzcG9uc2UodXNlck1lc3NhZ2UsIGNhbGwuTWV0YWRhdGEuUHVycG9zZSlcbiAgIDEyMFx0fVxuLi4uXG4gICAxNDdcdFx0XG4gICAxNDhcdFx0Ly8gQWRkIGN1cnJlbnQgdXNlciBtZXNzYWdlXG4gICAxNDlcdFx0bWVzc2FnZXMgPSBhcHBlbmQobWVzc2FnZXMsIExMTU1lc3NhZ2V7XG4gICAxNTBcdFx0XHRSb2xlOiAgICBcInVzZXJcIixcbiAgIDE1MVx0XHRcdENvbnRlbnQ6IHVzZXJNZXNzYWdlLFxuICAgMTUyXHRcdH0pXG4gICAxNTNcdFx0XG4gICAxNTRcdFx0cmV0dXJuIG1lc3NhZ2VzXG4gICAxNTVcdH1cbiAgIDE1Nlx0XG4gICAxNTdcdC8vIGNhbGxPcGVuQUlBUEkgbWFrZXMgYWN0dWFsIEFQSSBjYWxsIHRvIE9wZW5BSVxuICAgMTU4XHRmdW5jIChhaSAqQUlTZXJ2aWNlKSBjYWxsT3BlbkFJQVBJKG1lc3NhZ2VzIFtdTExNTWVzc2FnZSkgKHN0cmluZywgZXJyb3IpIHtcbiAgIDE1OVx0XHRyZXF1ZXN0IDo9IExMTVJlcXVlc3R7XG4gICAxNjBcdFx0XHRNb2RlbDogICAgICAgYWkuY29uZmlnLkFJLk9wZW5BSU1vZGVsLFxuICAgMTYxXHRcdFx0TWVzc2FnZXM6ICAgIG1lc3NhZ2VzLFxuICAgMTYyXHRcdFx0VGVtcGVyYXR1cmU6IDAuNyxcbiAgIDE2M1x0XHRcdE1heFRva2VuczogICAxNTAsXG4gICAxNjRcdFx0fVxuICAgMTY1XHRcdFxuICAgMTY2XHRcdGpzb25EYXRhLCBlcnIgOj0ganNvbi5NYXJzaGFsKHJlcXVlc3QpXG4gICAxNjdcdFx0aWYgZXJyICE9IG5pbCB7XG4gICAxNjhcdFx0XHRyZXR1cm4gXCJcIiwgZm10LkVycm9yZihcImZhaWxlZCB0byBtYXJzaGFsIHJlcXVlc3Q6ICV3XCIsIGVycilcbiAgIDE2OVx0XHR9XG4gICAxNzBcdFx0XG4gICAxNzFcdFx0cmVxLCBlcnIgOj0gaHR0cC5OZXdSZXF1ZXN0KFwiUE9TVFwiLCBcImh0dHBzOi8vYXBpLm9wZW5haS5jb20vdjEvY2hhdC9jb21wbGV0aW9uc1wiLCBieXRlcy5OZXdCdWZmZXIoanNvbkRhdGEpKVxuICAgMTcyXHRcdGlmIGVyciAhPSBuaWwge1xuICAgMTczXHRcdFx0cmV0dXJuIFwiXCIsIGZtdC5FcnJvcmYoXCJmYWlsZWQgdG8gY3JlYXRlIHJlcXVlc3Q6ICV3XCIsIGVycilcbiAgIDE3NFx0XHR9XG4uLi5cbiAgIDIxMFx0XHRcbiAgIDIxMVx0XHQvLyBDb250ZXh0LWF3YXJlIHJlc3BvbnNlcyBiYXNlZCBvbiB1c2VyIGlucHV0XG4gICAyMTJcdFx0c3dpdGNoIHtcbiAgIDIxM1x0XHRjYXNlIHN0cmluZ3MuQ29udGFpbnModXNlckxvd2VyLCBcImhlbGxvXCIpIHx8IHN0cmluZ3MuQ29udGFpbnModXNlckxvd2VyLCBcImhpXCIpOlxuICAgMjE0XHRcdFx0cmV0dXJuIGZtdC5TcHJpbnRmKFwiSGVsbG8hIEknbSBoZXJlIHRvIGhlbHAgeW91IHdpdGggJXMuIEhvdyBjYW4gSSBhc3Npc3QgeW91IHRvZGF5P1wiLCBwdXJwb3NlKSwgbmlsXG4gICAyMTVcdFx0Y2FzZSBzdHJpbmdzLkNvbnRhaW5zKHVzZXJMb3dlciwgXCJwYXNzd29yZFwiKSB8fCBzdHJpbmdzLkNvbnRhaW5zKHVzZXJMb3dlciwgXCJyZXNldFwiKTpcbiAgIDIxNlx0XHRcdHJldHVybiBcIkkgY2FuIGhlbHAgeW91IHJlc2V0IHlvdXIgcGFzc3dvcmQuIEknbGwgc2VuZCBhIHJlc2V0IGxpbmsgdG8geW91ciByZWdpc3RlcmVkIGVtYWlsIGFkZHJlc3MuIFBsZWFzZSBjaGVjayB5b3VyIGluYm94IGFuZCBmb2xsb3cgdGhlIGluc3RydWN0aW9ucy5cIiwgbmlsXG4gICAyMTdcdFx0Y2FzZSBzdHJpbmdzLkNvbnRhaW5zKHVzZXJMb3dlciwgXCJiaWxsaW5nXCIpIHx8IHN0cmluZ3MuQ29udGFpbnModXNlckxvd2VyLCBcInBheW1lbnRcIik6XG4gICAyMThcdFx0XHRyZXR1cm4gXCJJIHVuZGVyc3RhbmQgeW91IGhhdmUgYSBiaWxsaW5nIHF1ZXN0aW9uLiBMZXQgbWUgbG9vayB1cCB5b3VyIGFjY291bnQgZGV0YWlscy4gQ2FuIHlvdSBwbGVhc2UgY29uZmlybSB5b3VyIGFjY291bnQgZW1haWwgYWRkcmVzcz9cIiwgbmlsXG4uLi5cbiAgIDI1OVx0XG4gICAyNjBcdC8vIFByb2Nlc3NWQUQgcGVyZm9ybXMgVm9pY2UgQWN0aXZpdHkgRGV0ZWN0aW9uXG4gICAyNjFcdGZ1bmMgKGFpICpBSVNlcnZpY2UpIFByb2Nlc3NWQUQoYXVkaW9EYXRhIFtdYnl0ZSkgKGJvb2wsIGVycm9yKSB7XG4gICAyNjJcdFx0aWYgIWFpLmNvbmZpZy5BSS5FbmFibGVWQUQge1xuICAgMjYzXHRcdFx0cmV0dXJuIHRydWUsIG5pbCAvLyBBbHdheXMgY29uc2lkZXIgYXMgc3BlZWNoIGlmIFZBRCBpcyBkaXNhYmxlZFxuICAgMjY0XHRcdH1cbiAgIDI2NVx0XHRcbiAgIDI2Nlx0XHQvLyBNb2NrIFZBRCBwcm9jZXNzaW5nXG4gICAyNjdcdFx0Ly8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCB1c2UgQWdvcmEncyBWQUQgb3Igb3RoZXIgVkFEIGFsZ29yaXRobXNcbiAgIDI2OFx0XHRcbiAgIDI2OVx0XHQvLyBTaW1wbGUgbW9jazogY29uc2lkZXIgaXQgc3BlZWNoIGlmIGF1ZGlvIGRhdGEgaGFzIHN1ZmZpY2llbnQgXCJlbmVyZ3lcIlxuICAgMjcwXHRcdGlmIGxlbihhdWRpb0RhdGEpIDwgMTAwIHtcbiAgIDI3MVx0XHRcdHJldHVybiBmYWxzZSwgbmlsIC8vIFRvbyBzaG9ydCB0byBiZSBzcGVlY2hcbiAgIDI3Mlx0XHR9XG4gICAyNzNcdFx0XG4gICAyNzRcdFx0Ly8gTW9jayBlbmVyZ3kgY2FsY3VsYXRpb25cbiAgIDI3NVx0XHR2YXIgZW5lcmd5IGludDY0XG4gICAyNzZcdFx0Zm9yIF8sIHNhbXBsZSA6PSByYW5nZSBhdWRpb0RhdGEge1xuICAgMjc3XHRcdFx0ZW5lcmd5ICs9IGludDY0KHNhbXBsZSAqIHNhbXBsZSlcbiAgIDI3OFx0XHR9XG4gICAyNzlcdFx0XG4gICAyODBcdFx0YXZnRW5lcmd5IDo9IGZsb2F0NjQoZW5lcmd5KSAvIGZsb2F0NjQobGVuKGF1ZGlvRGF0YSkpXG4gICAyODFcdFx0dGhyZXNob2xkIDo9IDEwMDAuMCAqIGFpLmNvbmZpZy5BSS5WQURTZW5zaXRpdml0eVxuICAgMjgyXHRcdFxuICAgMjgzXHRcdGlzU3BlZWNoIDo9IGF2Z0VuZXJneSA+IHRocmVzaG9sZFxuICAgMjg0XHRcdGxvZy5QcmludGYoXCLwn46Z77iPIFtNT0NLXSBWQUQ6IEVuZXJneT0lLjJmLCBUaHJlc2hvbGQ9JS4yZiwgSXNTcGVlY2g9JXZcIiwgYXZnRW5lcmd5LCB0aHJlc2hvbGQsIGlzU3BlZWNoKVxuICAgMjg1XHRcdFxuICAgMjg2XHRcdHJldHVybiBpc1NwZWVjaCwgbmlsXG4gICAyODdcdH1cbi4uLlxuUGF0aDogc2VydmljZXMvbm90aWZpY2F0aW9uX3NlcnZpY2UuZ29cbiAgICAgMVx0cGFja2FnZSBzZXJ2aWNlc1xuICAgICAyXHRcbiAgICAgM1x0aW1wb3J0IChcbiAgICAgNFx0XHRcIkJhY2tlbmQvbW9kZWxzXCJcbiAgICAgNVx0XHRcImZtdFwiXG4gICAgIDZcdFx0XCJsb2dcIlxuICAgICA3XHRcdFwidGltZVwiXG4gICAgIDhcdClcbiAgICAgOVx0XG4gICAgMTBcdC8vIE5vdGlmaWNhdGlvblNlcnZpY2UgaGFuZGxlcyBzZW5kaW5nIG5vdGlmaWNhdGlvbnMgdG8gdXNlcnNcbiAgICAxMVx0dHlwZSBOb3RpZmljYXRpb25TZXJ2aWNlIHN0cnVjdCB7XG4gICAgMTJcdFx0Ly8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGNvbnRhaW46XG4gICAgMTNcdFx0Ly8gLSBQdXNoIG5vdGlmaWNhdGlvbiBzZXJ2aWNlIGNsaWVudHMgKEZDTSwgQVBOUylcbiAgICAxNFx0XHQvLyAtIFNNUyBzZXJ2aWNlIGNsaWVudHNcbiAgICAxNVx0XHQvLyAtIEVtYWlsIHNlcnZpY2UgY2xpZW50c1xuICAgIDE2XHRcdC8vIC0gV2ViU29ja2V0IGNvbm5lY3Rpb25zIGZvciByZWFsLXRpbWUgbm90aWZpY2F0aW9uc1xuICAgIDE3XHR9XG4uLi5cbiAgICAzM1x0XG4gICAgMzRcdC8vIFNlbmRDYWxsTm90aWZpY2F0aW9uIHNlbmRzIGEgbm90aWZpY2F0aW9uIHRvIHRoZSBjdXN0b21lciBhYm91dCBhbiBpbmNvbWluZyBBSSBjYWxsXG4gICAgMzVcdGZ1bmMgKG5zICpOb3RpZmljYXRpb25TZXJ2aWNlKSBTZW5kQ2FsbE5vdGlmaWNhdGlvbihjYWxsICptb2RlbHMuQ2FsbCkgZXJyb3Ige1xuICAgIDM2XHRcdGxvZy5QcmludGYoXCLwn5OxIFtNT0NLXSBTZW5kaW5nIGNhbGwgbm90aWZpY2F0aW9uIHRvIGN1c3RvbWVyOiAlc1wiLCBjYWxsLkN1c3RvbWVySUQpXG4gICAgMzdcdFx0XG4gICAgMzhcdFx0cGF5bG9hZCA6PSBOb3RpZmljYXRpb25QYXlsb2Fke1xuICAgIDM5XHRcdFx0VHlwZTogICAgICAgIFwiaW5jb21pbmdfYWlfY2FsbFwiLFxuICAgIDQwXHRcdFx0VGl0bGU6ICAgICAgIFwiQUkgQ3VzdG9tZXIgU3VwcG9ydCBDYWxsXCIsXG4gICAgNDFcdFx0XHRCb2R5OiAgICAgICAgZm10LlNwcmludGYoXCJZb3UgaGF2ZSBhbiBpbmNvbWluZyBBSSBzdXBwb3J0IGNhbGwgcmVnYXJkaW5nOiAlc1wiLCBjYWxsLk1ldGFkYXRhLlB1cnBvc2UpLFxuICAgIDQyXHRcdFx0Q2FsbElEOiAgICAgIGNhbGwuSUQsXG4gICAgNDNcdFx0XHRDaGFubmVsTmFtZTogY2FsbC5DaGFubmVsTmFtZSxcbiAgICA0NFx0XHRcdERhdGE6IG1hcFtzdHJpbmddaW50ZXJmYWNle317XG4gICAgNDVcdFx0XHRcdFwiY2FsbF9pZFwiOiAgICAgIGNhbGwuSUQsXG4gICAgNDZcdFx0XHRcdFwiY2hhbm5lbF9uYW1lXCI6IGNhbGwuQ2hhbm5lbE5hbWUsXG4gICAgNDdcdFx0XHRcdFwicHVycG9zZVwiOiAgICAgIGNhbGwuTWV0YWRhdGEuUHVycG9zZSxcbiAgICA0OFx0XHRcdFx0XCJjdXN0b21lcl9pZFwiOiAgY2FsbC5DdXN0b21lcklELFxuICAgIDQ5XHRcdFx0XHRcInRpbWVzdGFtcFwiOiAgICB0aW1lLk5vdygpLlVuaXgoKSxcbiAgICA1MFx0XHRcdH0sXG4gICAgNTFcdFx0fVxuICAgIDUyXHRcbiAgICA1M1x0XHQvLyBNb2NrIGRpZmZlcmVudCBub3RpZmljYXRpb24gY2hhbm5lbHNcbiAgICA1NFx0XHRpZiBlcnIgOj0gbnMuc2VuZFB1c2hOb3RpZmljYXRpb24oY2FsbC5DdXN0b21lcklELCBwYXlsb2FkKTsgZXJyICE9IG5pbCB7XG4gICAgNTVcdFx0XHRsb2cuUHJpbnRmKFwiRmFpbGVkIHRvIHNlbmQgcHVzaCBub3RpZmljYXRpb246ICV2XCIsIGVycilcbiAgICA1Nlx0XHR9XG4gICAgNTdcdFxuICAgIDU4XHRcdGlmIGVyciA6PSBucy5zZW5kU01TTm90aWZpY2F0aW9uKGNhbGwuQ3VzdG9tZXJJRCwgcGF5bG9hZCk7IGVyciAhPSBuaWwge1xuICAgIDU5XHRcdFx0bG9nLlByaW50ZihcIkZhaWxlZCB0byBzZW5kIFNNUyBub3RpZmljYXRpb246ICV2XCIsIGVycilcbiAgICA2MFx0XHR9XG4gICAgNjFcdFxuICAgIDYyXHRcdGlmIGVyciA6PSBucy5zZW5kRW1haWxOb3RpZmljYXRpb24oY2FsbC5DdXN0b21lcklELCBwYXlsb2FkKTsgZXJyICE9IG5pbCB7XG4gICAgNjNcdFx0XHRsb2cuUHJpbnRmKFwiRmFpbGVkIHRvIHNlbmQgZW1haWwgbm90aWZpY2F0aW9uOiAldlwiLCBlcnIpXG4gICAgNjRcdFx0fVxuICAgIDY1XHRcbiAgICA2Nlx0XHQvLyBTaW11bGF0ZSByZWFsLXRpbWUgbm90aWZpY2F0aW9uIHZpYSBXZWJTb2NrZXRcbiAgICA2N1x0XHRpZiBlcnIgOj0gbnMuc2VuZFdlYlNvY2tldE5vdGlmaWNhdGlvbihjYWxsLkN1c3RvbWVySUQsIHBheWxvYWQpOyBlcnIgIT0gbmlsIHtcbiAgICA2OFx0XHRcdGxvZy5QcmludGYoXCJGYWlsZWQgdG8gc2VuZCBXZWJTb2NrZXQgbm90aWZpY2F0aW9uOiAldlwiLCBlcnIpXG4gICAgNjlcdFx0fVxuICAgIDcwXHRcbiAgICA3MVx0XHRsb2cuUHJpbnRmKFwi4pyFIFtNT0NLXSBDYWxsIG5vdGlmaWNhdGlvbiBzZW50IHN1Y2Nlc3NmdWxseSB0byBjdXN0b21lcjogJXNcIiwgY2FsbC5DdXN0b21lcklEKVxuICAgIDcyXHRcdHJldHVybiBuaWxcbiAgICA3M1x0fVxuLi4uXG5QYXRoOiBSRUFETUUubWRcbi4uLlxuICAgIDMwXHTilIIg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYIOKUgiAgICDilIIg4pSM4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSQIOKUgiAgICDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJhcbiAgICAzMVx04pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYICAgIOKUgiDilIIgTm90aWZpY2F0aW9u4pSCIOKUgiAgICDilIzilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJBcbiAgICAzMlx0ICAgICAgICAgICAgICAgICAgICAgICDilIIg4pSCIFNlcnZpY2UgICAgIOKUgiDilILil4TilIDilIDilrrilIIgUHVzaC9TTVMvRW1haWwgIOKUglxuICAgIDMzXHQgICAgICAgICAgICAgICAgICAgICAgIOKUgiDilJTilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilIDilJgg4pSCICAgIOKUgiBTZXJ2aWNlcyAgICAgICAg4pSCXG4gICAgMzRcdCAgICAgICAgICAgICAgICAgICAgICAg4pSU4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSA4pSYICAgIOKUlOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUgOKUmFxuICAgIDM1XHRgYGBcbiAgICAzNlx0XG4gICAgMzdcdCMjIPCfk4sgUHJlcmVxdWlzaXRlc1xuICAgIDM4XHRcbiAgICAzOVx0LSBHbyAxLjIyIG9yIGhpZ2hlclxuICAgIDQwXHQtIEFnb3JhLmlvIGFjY291bnQgYW5kIEFwcCBJRFxuICAgIDQxXHQtIE9wZW5BSSBBUEkga2V5IChvcHRpb25hbCwgdXNlcyBtb2NrIGJ5IGRlZmF1bHQpXG4gICAgNDJcdFxuICAgIDQzXHQjIyDwn5ug77iPIFNldHVwXG4gICAgNDRcdFxuICAgIDQ1XHQxLiAqKkNsb25lIGFuZCBzZXR1cCoqOlxuICAgIDQ2XHQgICBgYGBiYXNoXG4gICAgNDdcdCAgIGdpdCBjbG9uZSA8cmVwb3NpdG9yeT5cbiAgICA0OFx0ICAgY2QgQmFja2VuZFxuICAgIDQ5XHQgICBgYGBcbiAgICA1MFx0XG4gICAgNTFcdDIuICoqSW5zdGFsbCBkZXBlbmRlbmNpZXMqKjpcbiAgICA1Mlx0ICAgYGBgYmFzaFxuICAgIDUzXHQgICBnbyBtb2QgdGlkeVxuICAgIDU0XHQgICBgYGBcbiAgICA1NVx0XG4gICAgNTZcdDMuICoqQ29uZmlndXJlIGVudmlyb25tZW50Kio6XG4gICAgNTdcdCAgIGBgYGJhc2hcbiAgICA1OFx0ICAgY3AgLmVudiAuZW52XG4gICAgNTlcdCAgICMgRWRpdCAuZW52IHdpdGggeW91ciBjb25maWd1cmF0aW9uXG4gICAgNjBcdCAgIGBgYFxuICAgIDYxXHRcbiAgICA2Mlx0NC4gKipSZXF1aXJlZCBlbnZpcm9ubWVudCB2YXJpYWJsZXMqKjpcbiAgICA2M1x0ICAgYGBgZW52XG4gICAgNjRcdCAgIEFHT1JBX0FQUF9JRD15b3VyX2Fnb3JhX2FwcF9pZF9oZXJlXG4gICAgNjVcdCAgIEFHT1JBX0FQUF9DRVJUSUZJQ0FURT15b3VyX2Fnb3JhX2FwcF9jZXJ0aWZpY2F0ZV9oZXJlXG4gICAgNjZcdCAgIE9QRU5BSV9BUElfS0VZPXlvdXJfb3BlbmFpX2FwaV9rZXlfaGVyZSAgIyBPcHRpb25hbFxuICAgIDY3XHQgICBgYGBcbiAgICA2OFx0XG4gICAgNjlcdDUuICoqUnVuIHRoZSBzZXJ2ZXIqKjpcbiAgICA3MFx0ICAgYGBgYmFzaFxuICAgIDcxXHQgICBnbyBydW4gbWFpbi5nb1xuICAgIDcyXHQgICBgYGBcbiAgICA3M1x0XG4gICAgNzRcdCMjIPCflIwgQVBJIEVuZHBvaW50c1xuICAgIDc1XHRcbiAgICA3Nlx0IyMjIENvcmUgRW5kcG9pbnRzXG4gICAgNzdcdFxuICAgIDc4XHR8IE1ldGhvZCB8IEVuZHBvaW50IHwgRGVzY3JpcHRpb24gfFxuICAgIDc5XHR8LS0tLS0tLS18LS0tLS0tLS0tLXwtLS0tLS0tLS0tLS0tfFxuICAgIDgwXHR8IGBHRVRgIHwgYC9gIHwgV2VsY29tZSBtZXNzYWdlIGFuZCBBUEkgb3ZlcnZpZXcgfFxuICAgIDgxXHR8IGBHRVRgIHwgYC9oZWFsdGhgIHwgSGVhbHRoIGNoZWNrIGFuZCBzeXN0ZW0gc3RhdHVzIHxcbiAgICA4Mlx0XG4gICAgODNcdCMjIyBDYWxsIE1hbmFnZW1lbnRcbi4uLlxuICAgMjM0XHRcbiAgIDIzNVx0IyMg8J+UpyBDb25maWd1cmF0aW9uXG4gICAyMzZcdFxuICAgMjM3XHQjIyMgU2VydmVyIFNldHRpbmdzXG4gICAyMzhcdGBgYGVudlxuICAgMjM5XHRTRVJWRVJfUE9SVD04MDgwXG4gICAyNDBcdFNFUlZFUl9IT1NUPTAuMC4wLjBcbiAgIDI0MVx0YGBgXG4gICAyNDJcdFxuICAgMjQzXHQjIyMgQWdvcmEgU2V0dGluZ3NcbiAgIDI0NFx0YGBgZW52XG4gICAyNDVcdEFHT1JBX0FQUF9JRD15b3VyX2FwcF9pZFxuICAgMjQ2XHRBR09SQV9BUFBfQ0VSVElGSUNBVEU9eW91cl9jZXJ0aWZpY2F0ZVxuICAgMjQ3XHRBR09SQV9DSEFOTkVMX1BSRUZJWD1haV9zdXBwb3J0X1xuICAgMjQ4XHRgYGBcbiAgIDI0OVx0XG4gICAyNTBcdCMjIyBBSSBTZXR0aW5nc1xuICAgMjUxXHRgYGBlbnZcbiAgIDI1Mlx0T1BFTkFJX0FQSV9LRVk9eW91cl9rZXlcbiAgIDI1M1x0T1BFTkFJX01PREVMPWdwdC00XG4gICAyNTRcdFNUVF9QUk9WSURFUj1vcGVuYWlcbiAgIDI1NVx0VFRTX1BST1ZJREVSPW9wZW5haVxuICAgMjU2XHRUVFNfVk9JQ0VfSUQ9YWxsb3lcbiAgIDI1N1x0RU5BQkxFX1ZBRD10cnVlXG4gICAyNThcdFZBRF9TRU5TSVRJVklUWT0wLjdcbiAgIDI1OVx0YGBgXG4gICAyNjBcdFxuICAgMjYxXHQjIyDwn6eqIERldmVsb3BtZW50XG4gICAyNjJcdFxuICAgMjYzXHQjIyMgTW9jayBTZXJ2aWNlc1xuICAgMjY0XHRcbiAgIDI2NVx0VGhlIGFwcGxpY2F0aW9uIGluY2x1ZGVzIG1vY2sgaW1wbGVtZW50YXRpb25zIGZvcjpcbiAgIDI2Nlx0LSAqKk5vdGlmaWNhdGlvbiBTZXJ2aWNlKio6IFNpbXVsYXRlcyBwdXNoL1NNUy9lbWFpbCBub3RpZmljYXRpb25zXG4gICAyNjdcdC0gKipBSSBTZXJ2aWNlKio6IE1vY2sgU1RULCBMTE0sIGFuZCBUVFMgcmVzcG9uc2VzXG4gICAyNjhcdC0gKipBZ29yYSBTZXJ2aWNlKio6IE1vY2sgdG9rZW4gZ2VuZXJhdGlvbiBhbmQgY2hhbm5lbCBtYW5hZ2VtZW50XG4gICAyNjlcdFxuICAgMjcwXHQjIyMgVGVzdGluZ1xuICAgMjcxXHRcbiAgIDI3Mlx0YGBgYmFzaFxuICAgMjczXHQjIFRlc3QgaGVhbHRoIGVuZHBvaW50XG4gICAyNzRcdGN1cmwgaHR0cDovL2xvY2FsaG9zdDo4MDgwL2hlYWx0aFxuLi4uXG5QYXRoOiBtYWluLmdvXG4uLi5cbiAgICAxN1x0XG4gICAgMThcdGZ1bmMgbWFpbigpIHtcbiAgICAxOVx0XHQvLyBMb2FkIGNvbmZpZ3VyYXRpb25cbiAgICAyMFx0XHRjb25maWcuTG9hZENvbmZpZygpXG4gICAgMjFcdFx0Y2ZnIDo9IGNvbmZpZy5BcHBDb25maWdcbiAgICAyMlx0XG4gICAgMjNcdFx0bG9nLlByaW50ZihcIvCfmoAgU3RhcnRpbmcgQUkgQ3VzdG9tZXIgU3VwcG9ydCBCYWNrZW5kXCIpXG4gICAgMjRcdFx0bG9nLlByaW50ZihcIvCfk4sgU2VydmVyOiAlczolc1wiLCBjZmcuU2VydmVyLkhvc3QsIGNmZy5TZXJ2ZXIuUG9ydClcbiAgICAyNVx0XHRsb2cuUHJpbnRmKFwi8J+OryBBZ29yYSBBcHAgSUQ6ICVzXCIsIGNmZy5BZ29yYS5BcHBJRClcbiAgICAyNlx0XG4gICAgMjdcdFx0Ly8gSW5pdGlhbGl6ZSBzZXJ2aWNlc1xuICAgIDI4XHRcdG5vdGlmaWNhdGlvblNlcnZpY2UgOj0gc2VydmljZXMuTmV3Tm90aWZpY2F0aW9uU2VydmljZSgpXG4gICAgMjlcdFx0YWdvcmFTZXJ2aWNlIDo9IHNlcnZpY2VzLk5ld0Fnb3JhU2VydmljZShjZmcpXG4gICAgMzBcdFx0YWlTZXJ2aWNlIDo9IHNlcnZpY2VzLk5ld0FJU2VydmljZShjZmcpXG4gICAgMzFcdFx0Y2FsbE1hbmFnZXIgOj0gc2VydmljZXMuTmV3Q2FsbE1hbmFnZXIobm90aWZpY2F0aW9uU2VydmljZSwgYWlTZXJ2aWNlLCBhZ29yYVNlcnZpY2UpXG4gICAgMzJcdFxuICAgIDMzXHRcdC8vIEluaXRpYWxpemUgaGFuZGxlcnNcbiAgICAzNFx0XHRjYWxsSGFuZGxlciA6PSBoYW5kbGVycy5OZXdDYWxsSGFuZGxlcihjYWxsTWFuYWdlciwgYWdvcmFTZXJ2aWNlKVxuICAgIDM1XHRcbiAgICAzNlx0XHQvLyBTZXR1cCBHaW4gcm91dGVyXG4gICAgMzdcdFx0cm91dGVyIDo9IHNldHVwUm91dGVyKGNhbGxIYW5kbGVyKVxuICAgIDM4XHRcbiAgICAzOVx0XHQvLyBTZXR1cCBncmFjZWZ1bCBzaHV0ZG93blxuICAgIDQwXHRcdHNldHVwR3JhY2VmdWxTaHV0ZG93bihhZ29yYVNlcnZpY2UpXG4uLi5cblBhdGg6IGNvbmZpZy9jb25maWcuZ29cbiAgICAgMVx0cGFja2FnZSBjb25maWdcbiAgICAgMlx0XG4gICAgIDNcdGltcG9ydCAoXG4gICAgIDRcdFx0XCJsb2dcIlxuICAgICA1XHRcdFwib3NcIlxuICAgICA2XHRcdFwic3RyY29udlwiXG4gICAgIDdcdFxuICAgICA4XHRcdFwiZ2l0aHViLmNvbS9qb2hvL2dvZG90ZW52XCJcbiAgICAgOVx0KVxuICAgIDEwXHRcbiAgICAxMVx0dHlwZSBDb25maWcgc3RydWN0IHtcbiAgICAxMlx0XHRTZXJ2ZXIgU2VydmVyQ29uZmlnXG4gICAgMTNcdFx0QWdvcmEgIEFnb3JhQ29uZmlnXG4gICAgMTRcdFx0QUkgICAgIEFJQ29uZmlnXG4gICAgMTVcdH1cbiAgICAxNlx0XG4gICAgMTdcdHR5cGUgU2VydmVyQ29uZmlnIHN0cnVjdCB7XG4gICAgMThcdFx0UG9ydCBzdHJpbmdcbiAgICAxOVx0XHRIb3N0IHN0cmluZ1xuICAgIDIwXHR9XG4gICAgMjFcdFxuICAgIDIyXHR0eXBlIEFnb3JhQ29uZmlnIHN0cnVjdCB7XG4gICAgMjNcdFx0QXBwSUQgICAgICAgICAgc3RyaW5nXG4gICAgMjRcdFx0QXBwQ2VydGlmaWNhdGUgc3RyaW5nXG4gICAgMjVcdFx0Q2hhbm5lbFByZWZpeCAgc3RyaW5nXG4gICAgMjZcdH1cbiAgICAyN1x0XG4gICAgMjhcdHR5cGUgQUlDb25maWcgc3RydWN0IHtcbiAgICAyOVx0XHRPcGVuQUlBUElLZXkgICAgc3RyaW5nXG4gICAgMzBcdFx0T3BlbkFJTW9kZWwgICAgIHN0cmluZ1xuICAgIDMxXHRcdFNUVFByb3ZpZGVyICAgICBzdHJpbmdcbiAgICAzMlx0XHRUVFNQcm92aWRlciAgICAgc3RyaW5nXG4gICAgMzNcdFx0Vm9pY2VJRCAgICAgICAgIHN0cmluZ1xuICAgIDM0XHRcdEVuYWJsZVZBRCAgICAgICBib29sXG4gICAgMzVcdFx0VkFEU2Vuc2l0aXZpdHkgIGZsb2F0NjRcbiAgICAzNlx0fVxuICAgIDM3XHRcbiAgICAzOFx0dmFyIEFwcENvbmZpZyAqQ29uZmlnXG4gICAgMzlcdFxuICAgIDQwXHRmdW5jIExvYWRDb25maWcoKSB7XG4gICAgNDFcdFx0Ly8gTG9hZCAuZW52IGZpbGUgaWYgaXQgZXhpc3RzXG4gICAgNDJcdFx0aWYgZXJyIDo9IGdvZG90ZW52LkxvYWQoKTsgZXJyICE9IG5pbCB7XG4gICAgNDNcdFx0XHRsb2cuUHJpbnRsbihcIk5vIC5lbnYgZmlsZSBmb3VuZCwgdXNpbmcgZW52aXJvbm1lbnQgdmFyaWFibGVzXCIpXG4gICAgNDRcdFx0fVxuICAgIDQ1XHRcbiAgICA0Nlx0XHRBcHBDb25maWcgPSAmQ29uZmlne1xuICAgIDQ3XHRcdFx0U2VydmVyOiBTZXJ2ZXJDb25maWd7XG4gICAgNDhcdFx0XHRcdFBvcnQ6IGdldEVudihcIlNFUlZFUl9QT1JUXCIsIFwiODA4MFwiKSxcbiAgICA0OVx0XHRcdFx0SG9zdDogZ2V0RW52KFwiU0VSVkVSX0hPU1RcIiwgXCIwLjAuMC4wXCIpLFxuICAgIDUwXHRcdFx0fSxcbiAgICA1MVx0XHRcdEFnb3JhOiBBZ29yYUNvbmZpZ3tcbiAgICA1Mlx0XHRcdFx0QXBwSUQ6ICAgICAgICAgIGdldEVudihcIkFHT1JBX0FQUF9JRFwiLCBcIlwiKSxcbiAgICA1M1x0XHRcdFx0QXBwQ2VydGlmaWNhdGU6IGdldEVudihcIkFHT1JBX0FQUF9DRVJUSUZJQ0FURVwiLCBcIlwiKSxcbiAgICA1NFx0XHRcdFx0Q2hhbm5lbFByZWZpeDogIGdldEVudihcIkFHT1JBX0NIQU5ORUxfUFJFRklYXCIsIFwiYWlfc3VwcG9ydF9cIiksXG4gICAgNTVcdFx0XHR9LFxuICAgIDU2XHRcdFx0QUk6IEFJQ29uZmlne1xuICAgIDU3XHRcdFx0XHRPcGVuQUlBUElLZXk6ICAgIGdldEVudihcIk9QRU5BSV9BUElfS0VZXCIsIFwiXCIpLFxuICAgIDU4XHRcdFx0XHRPcGVuQUlNb2RlbDogICAgIGdldEVudihcIk9QRU5BSV9NT0RFTFwiLCBcImdwdC00XCIpLFxuICAgIDU5XHRcdFx0XHRTVFRQcm92aWRlcjogICAgIGdldEVudihcIlNUVF9QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcbiAgICA2MFx0XHRcdFx0VFRTUHJvdmlkZXI6ICAgICBnZXRFbnYoXCJUVFNfUFJPVklERVJcIiwgXCJvcGVuYWlcIiksXG4gICAgNjFcdFx0XHRcdFZvaWNlSUQ6ICAgICAgICAgZ2V0RW52KFwiVFRTX1ZPSUNFX0lEXCIsIFwiYWxsb3lcIiksXG4gICAgNjJcdFx0XHRcdEVuYWJsZVZBRDogICAgICAgZ2V0RW52Qm9vbChcIkVOQUJMRV9WQURcIiwgdHJ1ZSksXG4gICAgNjNcdFx0XHRcdFZBRFNlbnNpdGl2aXR5OiAgZ2V0RW52RmxvYXQoXCJWQURfU0VOU0lUSVZJVFlcIiwgMC43KSxcbiAgICA2NFx0XHRcdH0sXG4gICAgNjVcdFx0fVxuLi4uXG5QYXRoOiBtb2RlbHMvY2FsbC5nb1xuLi4uXG4gICAgNDJcdFxuICAgIDQzXHQvLyBNZXNzYWdlIHJlcHJlc2VudHMgYSBzaW5nbGUgbWVzc2FnZSBpbiB0aGUgY29udmVyc2F0aW9uXG4gICAgNDRcdHR5cGUgTWVzc2FnZSBzdHJ1Y3Qge1xuICAgIDQ1XHRcdElEICAgICAgICBzdHJpbmcgICAgYGpzb246XCJpZFwiYFxuICAgIDQ2XHRcdFNwZWFrZXIgICBzdHJpbmcgICAgYGpzb246XCJzcGVha2VyXCJgIC8vIFwiYWlcIiBvciBcImN1c3RvbWVyXCJcbiAgICA0N1x0XHRDb250ZW50ICAgc3RyaW5nICAgIGBqc29uOlwiY29udGVudFwiYFxuICAgIDQ4XHRcdFRpbWVzdGFtcCB0aW1lLlRpbWUgYGpzb246XCJ0aW1lc3RhbXBcImBcbiAgICA0OVx0XHRUeXBlICAgICAgc3RyaW5nICAgIGBqc29uOlwidHlwZVwiYCAvLyBcInRleHRcIiwgXCJhdWRpb1wiXG4gICAgNTBcdH1cbiAgICA1MVx0XG4gICAgNTJcdC8vIENhbGxNZXRhZGF0YSBjb250YWlucyBhZGRpdGlvbmFsIGluZm9ybWF0aW9uIGFib3V0IHRoZSBjYWxsXG4gICAgNTNcdHR5cGUgQ2FsbE1ldGFkYXRhIHN0cnVjdCB7XG4gICAgNTRcdFx0UHVycG9zZSAgICAgICBzdHJpbmcgICAgICAgICAgICBganNvbjpcInB1cnBvc2VcImBcbiAgICA1NVx0XHRDdXN0b21lckluZm8gIEN1c3RvbWVySW5mbyAgICAgIGBqc29uOlwiY3VzdG9tZXJfaW5mb1wiYFxuICAgIDU2XHRcdEFJUGVyc29uYWxpdHkgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJhaV9wZXJzb25hbGl0eVwiYFxuICAgIDU3XHRcdExhbmd1YWdlICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgIDU4XHRcdFRhZ3MgICAgICAgICAgW11zdHJpbmcgICAgICAgICAgYGpzb246XCJ0YWdzXCJgXG4gICAgNTlcdFx0Q3VzdG9tRmllbGRzICBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcImN1c3RvbV9maWVsZHNcImBcbiAgICA2MFx0fVxuICAgIDYxXHRcbiAgICA2Mlx0Ly8gQ3VzdG9tZXJJbmZvIGNvbnRhaW5zIGN1c3RvbWVyIGRldGFpbHNcbiAgICA2M1x0dHlwZSBDdXN0b21lckluZm8gc3RydWN0IHtcbiAgICA2NFx0XHROYW1lICAgICAgICBzdHJpbmcgYGpzb246XCJuYW1lXCJgXG4gICAgNjVcdFx0RW1haWwgICAgICAgc3RyaW5nIGBqc29uOlwiZW1haWxcImBcbiAgICA2Nlx0XHRQaG9uZSAgICAgICBzdHJpbmcgYGpzb246XCJwaG9uZVwiYFxuICAgIDY3XHRcdEFjY291bnRJRCAgIHN0cmluZyBganNvbjpcImFjY291bnRfaWRcImBcbiAgICA2OFx0XHRQcmVmZXJlbmNlcyBtYXBbc3RyaW5nXXN0cmluZyBganNvbjpcInByZWZlcmVuY2VzXCJgXG4gICAgNjlcdH1cbiAgICA3MFx0XG4gICAgNzFcdC8vIEluaXRpYXRlQ2FsbFJlcXVlc3QgcmVwcmVzZW50cyB0aGUgcmVxdWVzdCB0byBzdGFydCBhIG5ldyBjYWxsXG4gICAgNzJcdHR5cGUgSW5pdGlhdGVDYWxsUmVxdWVzdCBzdHJ1Y3Qge1xuICAgIDczXHRcdEN1c3RvbWVySUQgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJjdXN0b21lcl9pZFwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgIDc0XHRcdFB1cnBvc2UgICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJwdXJwb3NlXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAgNzVcdFx0Q3VzdG9tZXJJbmZvICBDdXN0b21lckluZm8gICAgICBganNvbjpcImN1c3RvbWVyX2luZm9cImBcbiAgICA3Nlx0XHRBSVBlcnNvbmFsaXR5IHN0cmluZyAgICAgICAgICAgIGBqc29uOlwiYWlfcGVyc29uYWxpdHlcImBcbiAgICA3N1x0XHRMYW5ndWFnZSAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwibGFuZ3VhZ2VcImBcbiAgICA3OFx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgIDc5XHR9XG4uLi5cbiAgIDEwMlx0XG4gICAxMDNcdC8vIEluaXRpYXRlQ3VzdG9tZXJDYWxsUmVxdWVzdCByZXByZXNlbnRzIHRoZSByZXF1ZXN0IHdoZW4gY3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwgdG8gQUlcbiAgIDEwNFx0dHlwZSBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlcXVlc3Qgc3RydWN0IHtcbiAgIDEwNVx0XHRVc2VySUQgICAgICAgIGludCAgICAgICAgICAgICAgIGBqc29uOlwidXNlcl9pZFwiIGJpbmRpbmc6XCJyZXF1aXJlZFwiYFxuICAgMTA2XHRcdFB1cnBvc2UgICAgICAgc3RyaW5nICAgICAgICAgICAgYGpzb246XCJwdXJwb3NlXCIgYmluZGluZzpcInJlcXVpcmVkXCJgXG4gICAxMDdcdFx0Q3VzdG9tZXJJbmZvICBDdXN0b21lckluZm8gICAgICBganNvbjpcImN1c3RvbWVyX2luZm9cImBcbiAgIDEwOFx0XHRBSVBlcnNvbmFsaXR5IHN0cmluZyAgICAgICAgICAgIGBqc29uOlwiYWlfcGVyc29uYWxpdHlcImBcbiAgIDEwOVx0XHRMYW5ndWFnZSAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwibGFuZ3VhZ2VcImBcbiAgIDExMFx0XHRTY2VuYXJpbyAgICAgIHN0cmluZyAgICAgICAgICAgIGBqc29uOlwic2NlbmFyaW9cImAgICAgICAvLyBlLmcuLCBcImJpbGxpbmdcIiwgXCJ0ZWNobmljYWxcIiwgXCJnZW5lcmFsXCJcbiAgIDExMVx0XHRDdXN0b21GaWVsZHMgIG1hcFtzdHJpbmddc3RyaW5nIGBqc29uOlwiY3VzdG9tX2ZpZWxkc1wiYFxuICAgMTEyXHR9XG4gICAxMTNcdFxuICAgMTE0XHQvLyBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNlIHJlcHJlc2VudHMgdGhlIHJlc3BvbnNlIHdoZW4gY3VzdG9tZXIgaW5pdGlhdGVzIGNhbGwgdG8gQUlcbiAgIDExNVx0dHlwZSBJbml0aWF0ZUN1c3RvbWVyQ2FsbFJlc3BvbnNlIHN0cnVjdCB7XG4gICAxMTZcdFx0Q2FsbElEICAgICAgc3RyaW5nIGBqc29uOlwiY2FsbF9pZFwiYFxuICAgMTE3XHRcdENoYW5uZWxOYW1lIHN0cmluZyBganNvbjpcImNoYW5uZWxfbmFtZVwiYFxuICAgMTE4XHRcdFRva2VuICAgICAgIHN0cmluZyBganNvbjpcInRva2VuXCJgXG4gICAxMTlcdFx0VXNlcklEICAgICAgaW50ICAgIGBqc29uOlwidXNlcl9pZFwiYFxuICAgMTIwXHRcdFN0YXR1cyAgICAgIHN0cmluZyBganNvbjpcInN0YXR1c1wiYFxuICAgMTIxXHRcdE1lc3NhZ2UgICAgIHN0cmluZyBganNvbjpcIm1lc3NhZ2VcImBcbiAgIDEyMlx0XHRBSUluZm8gICAgICBBSUluZm8gYGpzb246XCJhaV9pbmZvXCJgXG4gICAxMjNcdH1cbiAgIDEyNFx0XG4gICAxMjVcdC8vIEFJSW5mbyBjb250YWlucyBpbmZvcm1hdGlvbiBhYm91dCB0aGUgQUkgYWdlbnRcbiAgIDEyNlx0dHlwZSBBSUluZm8gc3RydWN0IHtcbiAgIDEyN1x0XHROYW1lICAgICAgICBzdHJpbmcgYGpzb246XCJuYW1lXCJgXG4gICAxMjhcdFx0UGVyc29uYWxpdHkgc3RyaW5nIGBqc29uOlwicGVyc29uYWxpdHlcImBcbiAgIDEyOVx0XHRMYW5ndWFnZSAgICBzdHJpbmcgYGpzb246XCJsYW5ndWFnZVwiYFxuICAgMTMwXHRcdENhcGFiaWxpdGllcyBbXXN0cmluZyBganNvbjpcImNhcGFiaWxpdGllc1wiYFxuICAgMTMxXHR9XG4uLi5cbiAgIDE0MFx0XG4gICAxNDFcdC8vIE5ld0NhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlXG4gICAxNDJcdGZ1bmMgTmV3Q2FsbChjdXN0b21lcklELCBwdXJwb3NlIHN0cmluZywgY3VzdG9tZXJJbmZvIEN1c3RvbWVySW5mbykgKkNhbGwge1xuICAgMTQzXHRcdGNhbGxJRCA6PSB1dWlkLk5ldygpLlN0cmluZygpXG4gICAxNDRcdFx0Y2hhbm5lbE5hbWUgOj0gXCJhaV9zdXBwb3J0X1wiICsgY2FsbElEXG4gICAxNDVcdFxuICAgMTQ2XHRcdHJldHVybiAmQ2FsbHtcbiAgIDE0N1x0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNDhcdFx0XHRDdXN0b21lcklEOiAgY3VzdG9tZXJJRCxcbiAgIDE0OVx0XHRcdENoYW5uZWxOYW1lOiBjaGFubmVsTmFtZSxcbiAgIDE1MFx0XHRcdFN0YXR1czogICAgICBDYWxsU3RhdHVzUGVuZGluZyxcbiAgIDE1MVx0XHRcdFR5cGU6ICAgICAgICBDYWxsVHlwZUFJVG9DdXN0b21lciwgLy8gRGVmYXVsdCB0byBBSSBjYWxsaW5nIGN1c3RvbWVyXG4gICAxNTJcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE1M1x0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNTRcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTU1XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTU2XHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxNTdcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTU4XHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxNTlcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTYwXHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE2MVx0XHRcdH0sXG4gICAxNjJcdFx0fVxuICAgMTYzXHR9XG4gICAxNjRcdFxuICAgMTY1XHQvLyBOZXdDdXN0b21lckNhbGwgY3JlYXRlcyBhIG5ldyBjYWxsIGluc3RhbmNlIHdoZXJlIGN1c3RvbWVyIGNhbGxzIEFJXG4gICAxNjZcdGZ1bmMgTmV3Q3VzdG9tZXJDYWxsKHVzZXJJRCBpbnQsIHB1cnBvc2Ugc3RyaW5nLCBjdXN0b21lckluZm8gQ3VzdG9tZXJJbmZvKSAqQ2FsbCB7XG4gICAxNjdcdFx0Y2FsbElEIDo9IHV1aWQuTmV3KCkuU3RyaW5nKClcbiAgIDE2OFx0XHRjaGFubmVsTmFtZSA6PSBcImN1c3RvbWVyX2FpX1wiICsgY2FsbElEXG4gICAxNjlcdFxuICAgMTcwXHRcdHJldHVybiAmQ2FsbHtcbiAgIDE3MVx0XHRcdElEOiAgICAgICAgICBjYWxsSUQsXG4gICAxNzJcdFx0XHRDdXN0b21lcklEOiAgZm10LlNwcmludGYoXCJ1c2VyXyVkXCIsIHVzZXJJRCksIC8vIENvbnZlcnQgaW50IHVzZXJJRCB0byBzdHJpbmcgZm9yIGludGVybmFsIHVzZVxuICAgMTczXHRcdFx0Q2hhbm5lbE5hbWU6IGNoYW5uZWxOYW1lLFxuICAgMTc0XHRcdFx0U3RhdHVzOiAgICAgIENhbGxTdGF0dXNQZW5kaW5nLFxuICAgMTc1XHRcdFx0VHlwZTogICAgICAgIENhbGxUeXBlQ3VzdG9tZXJUb0FJLCAvLyBDdXN0b21lciBjYWxsaW5nIEFJXG4gICAxNzZcdFx0XHRTdGFydFRpbWU6ICAgdGltZS5Ob3coKSxcbiAgIDE3N1x0XHRcdFRyYW5zY3JpcHQ6ICBtYWtlKFtdTWVzc2FnZSwgMCksXG4gICAxNzhcdFx0XHRNZXRhZGF0YTogQ2FsbE1ldGFkYXRhe1xuICAgMTc5XHRcdFx0XHRQdXJwb3NlOiAgICAgICBwdXJwb3NlLFxuICAgMTgwXHRcdFx0XHRDdXN0b21lckluZm86ICBjdXN0b21lckluZm8sXG4gICAxODFcdFx0XHRcdEFJUGVyc29uYWxpdHk6IFwiaGVscGZ1bFwiLFxuICAgMTgyXHRcdFx0XHRMYW5ndWFnZTogICAgICBcImVuXCIsXG4gICAxODNcdFx0XHRcdFRhZ3M6ICAgICAgICAgIG1ha2UoW11zdHJpbmcsIDApLFxuICAgMTg0XHRcdFx0XHRDdXN0b21GaWVsZHM6ICBtYWtlKG1hcFtzdHJpbmddc3RyaW5nKSxcbiAgIDE4NVx0XHRcdH0sXG4gICAxODZcdFx0fVxuICAgMTg3XHR9XG4uLi5cblBhdGg6IC5lbnZcbiAgICAgMVx0IyBTZXJ2ZXIgQ29uZmlndXJhdGlvblxuICAgICAyXHRTRVJWRVJfUE9SVD04MDgwXG4gICAgIDNcdFNFUlZFUl9IT1NUPTAuMC4wLjBcbiAgICAgNFx0XG4gICAgIDVcdCMgQWdvcmEgQ29uZmlndXJhdGlvblxuICAgICA2XHRBR09SQV9BUFBfSUQ9MDE0NTIzNDgwZDk1NDRiZTgwMGEwMTRmMzViZGNmYWNcbiAgICAgN1x0QUdPUkFfQVBQX0NFUlRJRklDQVRFPWU3ZjM3ZTY3ZDgyYjRhZmY4NDYzMTc3NGMxYjVkZTYxXG4gICAgIDhcdEFHT1JBX0NIQU5ORUxfUFJFRklYPWFpX3N1cHBvcnRfXG4gICAgIDlcdFxuICAgIDEwXHQjIEFJIENvbmZpZ3VyYXRpb25cbiAgICAxMVx0T1BFTkFJX0FQSV9LRVk9ay1wcm9qLXp4M1RXWFItTEtjTVBUbTJMd3EwdzJnb3B1elZPZjR0dWlneFFINWlwaU95UkNLOXpGT09nRU1Cam1xZjBBN1FWZEMxSEc3MWtNVDNCbGJrRkoyTGNnaUJBdXBsNVU5YkI1ckVUeVBNUmxFVl8xR1ota1pzV3MxSmNUcEJZUy1saGthRURETUF2RXFmbnRZUUFVZExLZW5QMzZVQVxuICAgIDEyXHRPUEVOQUlfTU9ERUw9Z3B0LTRcbiAgICAxM1x0U1RUX1BST1ZJREVSPW9wZW5haVxuICAgIDE0XHRUVFNfUFJPVklERVI9b3BlbmFpXG4gICAgMTVcdFRUU19WT0lDRV9JRD1hbGxveVxuICAgIDE2XHRFTkFCTEVfVkFEPXRydWVcbiAgICAxN1x0VkFEX1NFTlNJVElWSVRZPTAuN1xuLi4uXG4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIxMzlmYzNhMS1jNDg0LTQ1YjctYjViNi03MWE0MmU0YzMwMDY7dG9vbHVfdnJ0eF8wMU1KMjQxQ0xRbnl6ZkhIYTllREdBZGUiOnsicmVxdWVzdElkIjoiMTM5ZmMzYTEtYzQ4NC00NWI3LWI1YjYtNzFhNDJlNGMzMDA2IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMU1KMjQxQ0xRbnl6ZkhIYTllREdBZGUiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJIZXJlJ3MgdGhlIGZpbGVzIGFuZCBkaXJlY3RvcmllcyB1cCB0byAyIGxldmVscyBkZWVwIGluIC4sIGV4Y2x1ZGluZyBoaWRkZW4gaXRlbXM6XG4oZW1wdHkgZGlyZWN0b3J5KVxuIiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiYWY2MmIxMzgtODZhZi00NWJiLWIzYmItODYxNDk1Yjc0OTg0O3Rvb2x1X3ZydHhfMDEyTkNuaDNOS2ZYZWRQaFM3M0Z2MVpEIjp7InJlcXVlc3RJZCI6ImFmNjJiMTM4LTg2YWYtNDViYi1iM2JiLTg2MTQ5NWI3NDk4NCIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDEyTkNuaDNOS2ZYZWRQaFM3M0Z2MVpEIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG4gICAgICAgIDxyZXR1cm4tY29kZT5cbiAgICAgICAgMFxuICAgICAgICA8L3JldHVybi1jb2RlPlxuICAgICAgICA8c3Rkb3V0PlxuICAgICAgICAuL0JhY2tlbmQvbWlkZGxld2FyZS9jb3JzLmdvXG4uL0JhY2tlbmQvY29uZmlnL2NvbmZpZy5nb1xuLi9CYWNrZW5kL21vZGVscy9jYWxsLmdvXG4uL0JhY2tlbmQvUkVBRE1FLm1kXG4uL0JhY2tlbmQvLmVudlxuLi9CYWNrZW5kL2hhbmRsZXJzL2NhbGxfaGFuZGxlci5nb1xuLi9CYWNrZW5kL21haW4uZ29cbi4vQmFja2VuZC9zZXJ2aWNlcy9jYWxsX21hbmFnZXIuZ29cbi4vQmFja2VuZC9zZXJ2aWNlcy9haV9zZXJ2aWNlLmdvXG4uL0JhY2tlbmQvc2VydmljZXMvbm90aWZpY2F0aW9uX3NlcnZpY2UuZ29cbi4vQmFja2VuZC9zZXJ2aWNlcy9hZ29yYV9zZXJ2aWNlLmdvXG4uL01vYmlsZSBhcHAvaW9zL1J1bm5lci9Bc3NldHMueGNhc3NldHMvTGF1bmNoSW1hZ2UuaW1hZ2VzZXQvUkVBRE1FLm1kXG4uL01vYmlsZSBhcHAvUkVBRE1FLm1kXG5cbiAgICAgICAgPC9zdGRvdXQ+XG4gICAgICAgIDxzdGRlcnI+XG4gICAgICAgIFxuICAgICAgICA8L3N0ZGVycj4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJlNDk0YTgwMy01YWM0LTRlYzctOTI0ZC0wZmEyZWZjMTM2OGE7dG9vbHVfdnJ0eF8wMUxoU2thalk1cWdLcUFNWFd4QWNDZTIiOnsicmVxdWVzdElkIjoiZTQ5NGE4MDMtNWFjNC00ZWM3LTkyNGQtMGZhMmVmYzEzNjhhIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUxoU2thalk1cWdLcUFNWFd4QWNDZTIiLCJwaGFzZSI6NiwicmVzdWx0Ijp7InRleHQiOiJGaWxlIG5vdCBmb3VuZDogQmFja2VuZC9jb25maWcvY29uZmlnLmdvLiBEaWQgeW91IG1lYW4gb25lIG9mIHRoZXNlPyBCYWNrZW5kL2NvbmZpZy9jb25maWcuZ28iLCJpc0Vycm9yIjp0cnVlLCJyZXF1ZXN0SWQiOiIifX0sIjlhMWRlZTI5LWJmY2MtNGI2ZS05OTBiLThkOWU1MjBjZWM3Yzt0b29sdV92cnR4XzAxVDVaaTdGUmJCY2dCQzVOZkdwdDZrWSI6eyJyZXF1ZXN0SWQiOiI5YTFkZWUyOS1iZmNjLTRiNmUtOTkwYi04ZDllNTIwY2VjN2MiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxVDVaaTdGUmJCY2dCQzVOZkdwdDZrWSIsInBoYXNlIjo2LCJyZXN1bHQiOnsidGV4dCI6IkZpbGUgbm90IGZvdW5kOiBCYWNrZW5kL2NvbmZpZy9jb25maWcuZ28uIERpZCB5b3UgbWVhbiBvbmUgb2YgdGhlc2U/IEJhY2tlbmQvY29uZmlnL2NvbmZpZy5nbyIsImlzRXJyb3IiOnRydWUsInJlcXVlc3RJZCI6IiJ9fSwiMDA5OWNjYWItMDUxNC00YTEzLWI0NTMtMTdjYTkzMDhlNGJiO3Rvb2x1X3ZydHhfMDFITjhtYVE3bmVERDVENzRjQVZjRWc1Ijp7InJlcXVlc3RJZCI6IjAwOTljY2FiLTA1MTQtNGExMy1iNDUzLTE3Y2E5MzA4ZTRiYiIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFITjhtYVE3bmVERDVENzRjQVZjRWc1IiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG4gICAgICAgIDxyZXR1cm4tY29kZT5cbiAgICAgICAgMFxuICAgICAgICA8L3JldHVybi1jb2RlPlxuICAgICAgICA8c3Rkb3V0PlxuICAgICAgICB0b3RhbCA0NzYxNlxuZHJ3eHIteHIteEAgMTYgcXVhbi5uZ28gIHN0YWZmICAgICAgIDUxMiBNYXkgMjYgMTQ6MzYgLlxuZHJ3eHIteHIteEAgIDcgcXVhbi5uZ28gIHN0YWZmICAgICAgIDIyNCBNYXkgMjYgMTQ6MzcgLi5cbi1ydy1yLS1yLS1AICAxIHF1YW4ubmdvICBzdGFmZiAgICAgIDYxNDggTWF5IDI2IDExOjU0IC5EU19TdG9yZVxuLXJ3LXItLXItLUAgIDEgcXVhbi5uZ28gIHN0YWZmICAgICAgIDUzMCBNYXkgMjYgMTE6NTMgLmVudlxuLXJ3LXItLXItLUAgIDEgcXVhbi5uZ28gIHN0YWZmICAgICAgOTUyOCBNYXkgMjYgMTE6NTIgUkVBRE1FLm1kXG4tcnd4ci14ci14QCAgMSBxdWFuLm5nbyAgc3RhZmYgIDEyMDg4NDUwIE1heSAyNiAxMTozOCBhaS1zdXBwb3J0LWJhY2tlbmRcbmRyd3hyLXhyLXhAICAzIHF1YW4ubmdvICBzdGFmZiAgICAgICAgOTYgTWF5IDI2IDExOjIyIGNvbmZpZ1xuLXJ3LXItLXItLUAgIDEgcXVhbi5uZ28gIHN0YWZmICAgICAgMTQ3MCBNYXkgMjYgMTM6MzYgZ28ubW9kXG4tcnctci0tci0tQCAgMSBxdWFuLm5nbyAgc3RhZmYgICAgICA4NDI1IE1heSAyNiAxMzozNiBnby5zdW1cbmRyd3hyLXhyLXhAICAzIHF1YW4ubmdvICBzdGFmZiAgICAgICAgOTYgTWF5IDI2IDExOjI1IGhhbmRsZXJzXG4tcnd4ci14ci14QCAgMSBxdWFuLm5nbyAgc3RhZmYgIDEyMjMxMjk4IE1heSAyNiAxNDozNiBtYWluXG4tcnctci0tci0tQCAgMSBxdWFuLm5nbyAgc3RhZmYgICAgICAzNTA0IE1heSAyNiAxMTozOCBtYWluLmdvXG5kcnd4ci14ci14QCAgMyBxdWFuLm5nbyAgc3RhZmYgICAgICAgIDk2IE1heSAyNiAxMToyNSBtaWRkbGV3YXJlXG5kcnd4ci14ci14QCAgMyBxdWFuLm5nbyAgc3RhZmYgICAgICAgIDk2IE1heSAyNiAxMToyMyBtb2RlbHNcbmRyd3hyLXhyLXhAICA2IHF1YW4ubmdvICBzdGFmZiAgICAgICAxOTIgTWF5IDI2IDE0OjAzIHNlcnZpY2VzXG4tcnd4ci14ci14QCAgMSBxdWFuLm5nbyAgc3RhZmYgICAgICA0NjkzIE1heSAyNiAxMTo0MCB0ZXN0X2FwaS5zaFxuXG4gICAgICAgIDwvc3Rkb3V0PlxuICAgICAgICA8c3RkZXJyPlxuICAgICAgICBcbiAgICAgICAgPC9zdGRlcnI+IiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiNGQ1MGZhMGYtZTRkNS00ODIzLTg1MGYtZWZjMmQ0ZGVlY2Y5O3Rvb2x1X3ZydHhfMDFNbktKbmlnbXlNZ2VpSkxrREFSclZLIjp7InJlcXVlc3RJZCI6IjRkNTBmYTBmLWU0ZDUtNDgyMy04NTBmLWVmYzJkNGRlZWNmOSIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFNbktKbmlnbXlNZ2VpSkxrREFSclZLIiwicGhhc2UiOjYsInJlc3VsdCI6eyJ0ZXh0IjoiRmlsZSBub3QgZm91bmQ6IEJhY2tlbmQvY29uZmlnLiBEaWQgeW91IG1lYW4gb25lIG9mIHRoZXNlPyBCYWNrZW5kL2NvbmZpZyIsImlzRXJyb3IiOnRydWUsInJlcXVlc3RJZCI6IiJ9fSwiMjIzOTI5YWMtMWFlMC00NGEzLTk4NGYtM2ZhOTgyNjQ1ZjA1O3Rvb2x1X3ZydHhfMDFKdWtlelJSWHRNeUxCUVlNQTJCeWk1Ijp7InJlcXVlc3RJZCI6IjIyMzkyOWFjLTFhZTAtNDRhMy05ODRmLTNmYTk4MjY0NWYwNSIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFKdWtlelJSWHRNeUxCUVlNQTJCeWk1IiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG48cmV0dXJuLWNvZGU+XG4wXG48L3JldHVybi1jb2RlPlxuPHN0ZG91dD5cbkJhY2tlbmQvY29uZmlnL2NvbmZpZy5nb1xuXG48L3N0ZG91dD5cbjxzdGRlcnI+XG5cbjwvc3RkZXJyPiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjlkZTczMGI3LTgxYjQtNDNkMS04MTRhLWE4NjQ2ODNhYWFkMjt0b29sdV92cnR4XzAxRWZDaGdnUUtSQW80UEhnYjd0NzJmdSI6eyJyZXF1ZXN0SWQiOiI5ZGU3MzBiNy04MWI0LTQzZDEtODE0YS1hODY0NjgzYWFhZDIiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxRWZDaGdnUUtSQW80UEhnYjd0NzJmdSIsInBoYXNlIjo2LCJyZXN1bHQiOnsidGV4dCI6IkZpbGUgbm90IGZvdW5kOiBCYWNrZW5kL2NvbmZpZy9jb25maWcuZ28uIERpZCB5b3UgbWVhbiBvbmUgb2YgdGhlc2U/IEJhY2tlbmQvY29uZmlnL2NvbmZpZy5nbyIsImlzRXJyb3IiOnRydWUsInJlcXVlc3RJZCI6IiJ9fSwiZjQzYjM0MzktOTJkYS00ZDUzLTg3MjEtZWVmNWQxZjFlNmY4O3Rvb2x1X3ZydHhfMDE3RlR3Umd4VUtxTTNWVGgxWWs5REhwIjp7InJlcXVlc3RJZCI6ImY0M2IzNDM5LTkyZGEtNGQ1My04NzIxLWVlZjVkMWYxZTZmOCIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDE3RlR3Umd4VUtxTTNWVGgxWWs5REhwIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG4gICAgICAgIDxyZXR1cm4tY29kZT5cbiAgICAgICAgMFxuICAgICAgICA8L3JldHVybi1jb2RlPlxuICAgICAgICA8c3Rkb3V0PlxuICAgICAgICBwYWNrYWdlIGNvbmZpZ1xuXG5pbXBvcnQgKFxuXHRcImxvZ1wiXG5cdFwib3NcIlxuXHRcInN0cmNvbnZcIlxuXG5cdFwiZ2l0aHViLmNvbS9qb2hvL2dvZG90ZW52XCJcbilcblxudHlwZSBDb25maWcgc3RydWN0IHtcblx0U2VydmVyIFNlcnZlckNvbmZpZ1xuXHRBZ29yYSAgQWdvcmFDb25maWdcblx0QUkgICAgIEFJQ29uZmlnXG59XG5cbnR5cGUgU2VydmVyQ29uZmlnIHN0cnVjdCB7XG5cdFBvcnQgc3RyaW5nXG5cdEhvc3Qgc3RyaW5nXG59XG5cbnR5cGUgQWdvcmFDb25maWcgc3RydWN0IHtcblx0QXBwSUQgICAgICAgICAgc3RyaW5nXG5cdEFwcENlcnRpZmljYXRlIHN0cmluZ1xuXHRDaGFubmVsUHJlZml4ICBzdHJpbmdcbn1cblxudHlwZSBBSUNvbmZpZyBzdHJ1Y3Qge1xuXHRPcGVuQUlBUElLZXkgICAgc3RyaW5nXG5cdE9wZW5BSU1vZGVsICAgICBzdHJpbmdcblx0U1RUUHJvdmlkZXIgICAgIHN0cmluZ1xuXHRUVFNQcm92aWRlciAgICAgc3RyaW5nXG5cdFZvaWNlSUQgICAgICAgICBzdHJpbmdcblx0RW5hYmxlVkFEICAgICAgIGJvb2xcblx0VkFEU2Vuc2l0aXZpdHkgIGZsb2F0NjRcbn1cblxudmFyIEFwcENvbmZpZyAqQ29uZmlnXG5cbmZ1bmMgTG9hZENvbmZpZygpIHtcblx0Ly8gTG9hZCAuZW52IGZpbGUgaWYgaXQgZXhpc3RzXG5cdGlmIGVyciA6PSBnb2RvdGVudi5Mb2FkKCk7IGVyciAhPSBuaWwge1xuXHRcdGxvZy5QcmludGxuKFwiTm8gLmVudiBmaWxlIGZvdW5kLCB1c2luZyBlbnZpcm9ubWVudCB2YXJpYWJsZXNcIilcblx0fVxuXG5cdEFwcENvbmZpZyA9ICZDb25maWd7XG5cdFx0U2VydmVyOiBTZXJ2ZXJDb25maWd7XG5cdFx0XHRQb3J0OiBnZXRFbnYoXCJTRVJWRVJfUE9SVFwiLCBcIjgwODBcIiksXG5cdFx0XHRIb3N0OiBnZXRFbnYoXCJTRVJWRVJfSE9TVFwiLCBcIjAuMC4wLjBcIiksXG5cdFx0fSxcblx0XHRBZ29yYTogQWdvcmFDb25maWd7XG5cdFx0XHRBcHBJRDogICAgICAgICAgZ2V0RW52KFwiQUdPUkFfQVBQX0lEXCIsIFwiXCIpLFxuXHRcdFx0QXBwQ2VydGlmaWNhdGU6IGdldEVudihcIkFHT1JBX0FQUF9DRVJUSUZJQ0FURVwiLCBcIlwiKSxcblx0XHRcdENoYW5uZWxQcmVmaXg6ICBnZXRFbnYoXCJBR09SQV9DSEFOTkVMX1BSRUZJWFwiLCBcImFpX3N1cHBvcnRfXCIpLFxuXHRcdH0sXG5cdFx0QUk6IEFJQ29uZmlne1xuXHRcdFx0T3BlbkFJQVBJS2V5OiAgICBnZXRFbnYoXCJPUEVOQUlfQVBJX0tFWVwiLCBcIlwiKSxcblx0XHRcdE9wZW5BSU1vZGVsOiAgICAgZ2V0RW52KFwiT1BFTkFJX01PREVMXCIsIFwiZ3B0LTRcIiksXG5cdFx0XHRTVFRQcm92aWRlcjogICAgIGdldEVudihcIlNUVF9QUk9WSURFUlwiLCBcIm9wZW5haVwiKSxcblx0XHRcdFRUU1Byb3ZpZGVyOiAgICAgZ2V0RW52KFwiVFRTX1BST1ZJREVSXCIsIFwib3BlbmFpXCIpLFxuXHRcdFx0Vm9pY2VJRDogICAgICAgICBnZXRFbnYoXCJUVFNfVk9JQ0VfSURcIiwgXCJhbGxveVwiKSxcblx0XHRcdEVuYWJsZVZBRDogICAgICAgZ2V0RW52Qm9vbChcIkVOQUJMRV9WQURcIiwgdHJ1ZSksXG5cdFx0XHRWQURTZW5zaXRpdml0eTogIGdldEVudkZsb2F0KFwiVkFEX1NFTlNJVElWSVRZXCIsIDAuNyksXG5cdFx0fSxcblx0fVxuXG5cdC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuXHRpZiBBcHBDb25maWcuQWdvcmEuQXBwSUQgPT0gXCJcIiB7XG5cdFx0bG9nLkZhdGFsKFwiQUdPUkFfQVBQX0lEIGlzIHJlcXVpcmVkXCIpXG5cdH1cbn1cblxuZnVuYyBnZXRFbnYoa2V5LCBkZWZhdWx0VmFsdWUgc3RyaW5nKSBzdHJpbmcge1xuXHRpZiB2YWx1ZSA6PSBvcy5HZXRlbnYoa2V5KTsgdmFsdWUgIT0gXCJcIiB7XG5cdFx0cmV0dXJuIHZhbHVlXG5cdH1cblx0cmV0dXJuIGRlZmF1bHRWYWx1ZVxufVxuXG5mdW5jIGdldEVudkJvb2woa2V5IHN0cmluZywgZGVmYXVsdFZhbHVlIGJvb2wpIGJvb2wge1xuXHRpZiB2YWx1ZSA6PSBvcy5HZXRlbnYoa2V5KTsgdmFsdWUgIT0gXCJcIiB7XG5cdFx0aWYgcGFyc2VkLCBlcnIgOj0gc3RyY29udi5QYXJzZUJvb2wodmFsdWUpOyBlcnIgPT0gbmlsIHtcblx0XHRcdHJldHVybiBwYXJzZWRcblx0XHR9XG5cdH1cblx0cmV0dXJuIGRlZmF1bHRWYWx1ZVxufVxuXG5mdW5jIGdldEVudkZsb2F0KGtleSBzdHJpbmcsIGRlZmF1bHRWYWx1ZSBmbG9hdDY0KSBmbG9hdDY0IHtcblx0aWYgdmFsdWUgOj0gb3MuR2V0ZW52KGtleSk7IHZhbHVlICE9IFwiXCIge1xuXHRcdGlmIHBhcnNlZCwgZXJyIDo9IHN0cmNvbnYuUGFyc2VGbG9hdCh2YWx1ZSwgNjQpOyBlcnIgPT0gbmlsIHtcblx0XHRcdHJldHVybiBwYXJzZWRcblx0XHR9XG5cdH1cblx0cmV0dXJuIGRlZmF1bHRWYWx1ZVxufVxuXG4gICAgICAgIDwvc3Rkb3V0PlxuICAgICAgICA8c3RkZXJyPlxuICAgICAgICBcbiAgICAgICAgPC9zdGRlcnI+IiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fX0sImRyYWZ0RXhjaGFuZ2UiOnsicmVxdWVzdF9tZXNzYWdlIjoiIiwicmljaF90ZXh0X2pzb25fcmVwciI6eyJ0eXBlIjoiZG9jIiwiY29udGVudCI6W3sidHlwZSI6InBhcmFncmFwaCJ9XX0sInN0YXR1cyI6ImRyYWZ0In0sImRyYWZ0QWN0aXZlQ29udGV4dElkcyI6WyIvVXNlcnMvcXVhbi5uZ28vRGVza3RvcC9pbl9hcHBfY2FsbF9mZWF0dXJlL0JhY2tlbmRmYWxzZWZhbHNlIiwidXNlckd1aWRlbGluZXMiLCJhZ2VudE1lbW9yaWVzIl0sInJlcXVlc3RJZHMiOltdLCJpc1Bpbm5lZCI6ZmFsc2UsImlzU2hhcmVhYmxlIjp0cnVlLCJleHRyYURhdGEiOnsiaXNBZ2VudENvbnZlcnNhdGlvbiI6dHJ1ZSwiaGFzRGlydHlFZGl0cyI6ZmFsc2V9LCJwZXJzb25hVHlwZSI6MCwicm9vdFRhc2tVdWlkIjoiNjYwYTNmMWUtMmM3OC00Yjc4LTliNWQtY2ZkYzgzZWU0OWFiIn0sIjc4MjNiZTJlLTNmMTgtNDkwNy05NzRjLTBmMjI1MDQ2MjEyNCI6eyJpZCI6Ijc4MjNiZTJlLTNmMTgtNDkwNy05NzRjLTBmMjI1MDQ2MjEyNCIsImNyZWF0ZWRBdElzbyI6IjIwMjUtMDUtMjZUMDg6MTM6NDkuMjI2WiIsImxhc3RJbnRlcmFjdGVkQXRJc28iOiIyMDI1LTA1LTI2VDA4OjEzOjQ5LjIyNloiLCJjaGF0SGlzdG9yeSI6W10sImZlZWRiYWNrU3RhdGVzIjp7fSwidG9vbFVzZVN0YXRlcyI6e30sImRyYWZ0RXhjaGFuZ2UiOnsicmVxdWVzdF9tZXNzYWdlIjoiIiwicmljaF90ZXh0X2pzb25fcmVwciI6eyJ0eXBlIjoiZG9jIiwiY29udGVudCI6W3sidHlwZSI6InBhcmFncmFwaCJ9XX0sInN0YXR1cyI6ImRyYWZ0In0sInJlcXVlc3RJZHMiOltdLCJpc1Bpbm5lZCI6ZmFsc2UsImlzU2hhcmVhYmxlIjpmYWxzZSwiZXh0cmFEYXRhIjp7ImlzQWdlbnRDb252ZXJzYXRpb24iOnRydWUsImhhc0RpcnR5RWRpdHMiOmZhbHNlfSwicGVyc29uYVR5cGUiOjAsInJvb3RUYXNrVXVpZCI6IjBkNTE5ODZlLTQ1OWEtNDU4Yi04Nzc3LTRmMTZlZjVjYzk5YSJ9fSwiYWdlbnRFeGVjdXRpb25Nb2RlIjoiYXV0byIsImlzUGFuZWxDb2xsYXBzZWQiOnRydWUsInNvcnRDb252ZXJzYXRpb25zQnkiOiJsYXN0TWVzc2FnZVRpbWVzdGFtcCJ9" />
      </map>
    </option>
  </component>
</project>