# AI Customer Support Backend

A Go backend service that provides AI-powered customer support through voice calls using Agora RTC API. The AI can initiate calls to customers and provide real-time voice assistance.

## 🚀 Features

- **Dual Call Flows**:
  - 🤖➡️👤 **AI-to-Customer**: Server initiates calls to customers via notifications
  - 👤➡️🤖 **Customer-to-AI**: Customers can initiate calls to AI support
- **Real-time Voice Processing**: Speech-to-text, AI response generation, and text-to-speech
- **Agora RTC Integration**: High-quality voice calling infrastructure
- **Voice Activity Detection (VAD)**: Intelligent speech detection
- **Call Management**: Complete call lifecycle management for both flow types
- **Mock Implementations**: Ready for development with mock services

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │   Agora RTC     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ User joins  │ │◄──►│ │ Call Mgmt   │ │◄──►│ │ Voice       │ │
│ │ voice call  │ │    │ │ Service     │ │    │ │ Channels    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    └─────────────────┘
│ ┌─────────────┐ │    │ │ AI Service  │ │    ┌─────────────────┐
│ │ Receives    │ │◄──►│ │ STT→LLM→TTS │ │◄──►│   AI Services   │
│ │ notification│ │    │ └─────────────┘ │    │ (OpenAI, etc.)  │
│ └─────────────┘ │    │ ┌─────────────┐ │    └─────────────────┘
└─────────────────┘    │ │ Notification│ │    ┌─────────────────┐
                       │ │ Service     │ │◄──►│ Push/SMS/Email  │
                       │ └─────────────┘ │    │ Services        │
                       └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

- Go 1.22 or higher
- Agora.io account and App ID
- OpenAI API key (optional, uses mock by default)

## 🛠️ Setup

1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd Backend
   ```

2. **Install dependencies**:
   ```bash
   go mod tidy
   ```

3. **Configure environment**:
   ```bash
   cp .env .env
   # Edit .env with your configuration
   ```

4. **Required environment variables**:
   ```env
   AGORA_APP_ID=your_agora_app_id_here
   AGORA_APP_CERTIFICATE=your_agora_app_certificate_here
   OPENAI_API_KEY=your_openai_api_key_here  # Optional
   ```

5. **Run the server**:
   ```bash
   go run main.go
   ```

## 🔌 API Endpoints

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | Welcome message and API overview |
| `GET` | `/health` | Health check and system status |

### Call Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/calls/initiate` | 🤖➡️👤 Initiate AI call to customer |
| `POST` | `/api/calls/customer-initiate` | 👤➡️🤖 Customer initiates call to AI |
| `POST` | `/api/calls/:call_id/join` | Customer joins an existing call |
| `GET` | `/api/calls/:call_id` | Get call details |
| `POST` | `/api/calls/:call_id/end` | End a call |
| `GET` | `/api/calls/active` | List all active calls |
| `GET` | `/api/calls/:call_id/stats` | Get call statistics |
| `POST` | `/api/calls/:call_id/token` | Generate Agora token |

## 📖 Usage Examples

### 1. 🤖➡️👤 Initiate AI-to-Customer Call

```bash
curl -X POST http://localhost:8080/api/calls/initiate \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "purpose": "billing inquiry",
    "customer_info": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890"
    },
    "ai_personality": "helpful",
    "language": "en"
  }'
```

**Response**:
```json
{
  "call_id": "uuid-here",
  "channel_name": "ai_support_uuid-here",
  "token": "agora_token_here",
  "status": "ringing",
  "message": "Call initiated successfully. Notification sent to customer."
}
```

### 2. 👤➡️🤖 Initiate Customer-to-AI Call

```bash
curl -X POST http://localhost:8080/api/calls/customer-initiate \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_789",
    "purpose": "technical support",
    "customer_info": {
      "name": "Alice Johnson",
      "email": "<EMAIL>",
      "phone": "+1555123456"
    },
    "ai_personality": "technical",
    "language": "en",
    "scenario": "technical"
  }'
```

**Response**:
```json
{
  "call_id": "uuid-here",
  "channel_name": "customer_ai_uuid-here",
  "token": "agora_token_here",
  "status": "connected",
  "message": "Customer call initiated successfully. AI is ready to assist.",
  "ai_info": {
    "name": "AI Support Assistant",
    "personality": "technical",
    "language": "en",
    "capabilities": [
      "billing_support",
      "technical_support",
      "account_management",
      "general_inquiries",
      "password_reset",
      "subscription_management"
    ]
  }
}
```

### 3. Customer Joins Call

```bash
curl -X POST http://localhost:8080/api/calls/{call_id}/join \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123"
  }'
```

### 4. Get Call Status

```bash
curl http://localhost:8080/api/calls/{call_id}
```

## 🎯 Call Flows

### 🤖➡️👤 AI-to-Customer Flow

1. **Server initiates call**:
   - Creates call session
   - Sends notification to customer (push/SMS/email)
   - AI joins Agora channel
   - Sets up audio processing

2. **Customer receives notification**:
   - Opens mobile app
   - App calls `/api/calls/:call_id/join`
   - Gets Agora token and channel info
   - Joins voice channel

3. **AI conversation loop**:
   - Customer speaks → Audio captured
   - VAD detects speech → STT processes
   - LLM generates response → TTS converts
   - AI speaks response → Customer hears

4. **Call ends**:
   - Either party can end call
   - Resources cleaned up
   - Call summary generated

### 👤➡️🤖 Customer-to-AI Flow

1. **Customer initiates call**:
   - App calls `/api/calls/customer-initiate`
   - Creates call session immediately
   - AI joins Agora channel instantly
   - Returns token and channel info

2. **Customer joins channel**:
   - Uses returned token to join Agora channel
   - AI is already waiting and ready
   - No notification or ringing phase

3. **AI conversation loop**:
   - Same as AI-to-Customer flow
   - Customer speaks → VAD → STT → LLM → TTS → AI responds

4. **Call ends**:
   - Either party can end call
   - Resources cleaned up
   - Call summary generated

## 🔧 Configuration

### Server Settings
```env
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
```

### Agora Settings
```env
AGORA_APP_ID=your_app_id
AGORA_APP_CERTIFICATE=your_certificate
AGORA_CHANNEL_PREFIX=ai_support_
```

### AI Settings
```env
OPENAI_API_KEY=your_key
OPENAI_MODEL=gpt-4
STT_PROVIDER=openai
TTS_PROVIDER=openai
TTS_VOICE_ID=alloy
ENABLE_VAD=true
VAD_SENSITIVITY=0.7
```

## 🧪 Development

### Mock Services

The application includes mock implementations for:
- **Notification Service**: Simulates push/SMS/email notifications
- **AI Service**: Mock STT, LLM, and TTS responses
- **Agora Service**: Mock token generation and channel management

### Testing

```bash
# Test health endpoint
curl http://localhost:8080/health

# Test welcome endpoint
curl http://localhost:8080/

# Test call initiation
curl -X POST http://localhost:8080/api/calls/initiate \
  -H "Content-Type: application/json" \
  -d '{"customer_id":"test","purpose":"test call"}'
```

## 🚀 Production Deployment

For production deployment:

1. **Replace mock services** with real implementations
2. **Add Agora Go Server SDK** for actual RTC functionality
3. **Configure real AI services** (OpenAI, etc.)
4. **Set up notification providers** (FCM, Twilio, SendGrid)
5. **Add database** for persistent storage
6. **Implement authentication** and authorization
7. **Add monitoring** and logging

## 📝 Next Steps

To complete the implementation:

1. **Integrate Agora Go Server SDK**
2. **Add real AI service integrations**
3. **Implement database layer**
4. **Add authentication middleware**
5. **Set up real notification services**
6. **Add comprehensive testing**
7. **Implement call recording and analytics**

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details
