#!/bin/bash

# AI Customer Support Backend API Test Script
# This script demonstrates both call flows: AI-to-Customer and Customer-to-AI

BASE_URL="http://localhost:8080"

echo "🤖 AI Customer Support Backend - API Test"
echo "=========================================="
echo "Testing both call flows:"
echo "1. AI-to-Customer (AI initiates call)"
echo "2. Customer-to-AI (Customer initiates call)"
echo ""

# Test 1: Health Check
echo "1. Testing health endpoint..."
curl -s "$BASE_URL/health" | jq '.'

# Test 2: Welcome endpoint
echo ""
echo "2. Testing welcome endpoint..."
curl -s "$BASE_URL/" | jq '.'

# Test 3: Initiate AI-to-Customer Call
echo ""
echo "3. 🤖➡️👤 Initiating AI-to-Customer call..."
AI_CALL_RESPONSE=$(curl -s -X POST "$BASE_URL/api/calls/initiate" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_456",
    "purpose": "password reset assistance",
    "customer_info": {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "+**********"
    },
    "ai_personality": "friendly",
    "language": "en"
  }')

echo "$AI_CALL_RESPONSE" | jq '.'

# Extract call ID for subsequent tests
AI_CALL_ID=$(echo "$AI_CALL_RESPONSE" | jq -r '.call_id')
echo ""
echo "📞 AI-to-Customer Call ID: $AI_CALL_ID"

# Test 4: Initiate Customer-to-AI Call
echo ""
echo "4. 👤➡️🤖 Initiating Customer-to-AI call..."
CUSTOMER_CALL_RESPONSE=$(curl -s -X POST "$BASE_URL/api/calls/customer-initiate" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_789",
    "purpose": "technical support",
    "customer_info": {
      "name": "Alice Johnson",
      "email": "<EMAIL>",
      "phone": "+1555123456"
    },
    "ai_personality": "technical",
    "language": "en",
    "scenario": "technical"
  }')

echo "$CUSTOMER_CALL_RESPONSE" | jq '.'

# Extract call ID for subsequent tests
CUSTOMER_CALL_ID=$(echo "$CUSTOMER_CALL_RESPONSE" | jq -r '.call_id')
echo ""
echo "📞 Customer-to-AI Call ID: $CUSTOMER_CALL_ID"

# Test 5: Get call details for both calls
echo ""
echo "5. Getting AI-to-Customer call details..."
curl -s "$BASE_URL/api/calls/$AI_CALL_ID" | jq '.'

echo ""
echo "6. Getting Customer-to-AI call details..."
curl -s "$BASE_URL/api/calls/$CUSTOMER_CALL_ID" | jq '.'

# Test 7: Customer joins AI-to-Customer call
echo ""
echo "7. Customer joining AI-to-Customer call..."
JOIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/calls/$AI_CALL_ID/join" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_456"
  }')

echo "$JOIN_RESPONSE" | jq '.'

# Test 8: Get active calls (should show both types)
echo ""
echo "8. Getting active calls (both types)..."
curl -s "$BASE_URL/api/calls/active" | jq '.'

# Test 9: Generate additional tokens
echo ""
echo "9. Generating additional token for AI-to-Customer call..."
curl -s -X POST "$BASE_URL/api/calls/$AI_CALL_ID/token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "support_agent",
    "role": 2
  }' | jq '.'

# Test 10: Get call stats for both calls
echo ""
echo "10. Getting AI-to-Customer call statistics..."
curl -s "$BASE_URL/api/calls/$AI_CALL_ID/stats" | jq '.'

echo ""
echo "11. Getting Customer-to-AI call statistics..."
curl -s "$BASE_URL/api/calls/$CUSTOMER_CALL_ID/stats" | jq '.'

# Wait a moment to simulate conversation
echo ""
echo "12. Simulating conversation (waiting 3 seconds)..."
sleep 3

# Test 13: End both calls
echo ""
echo "13. Ending AI-to-Customer call..."
curl -s -X POST "$BASE_URL/api/calls/$AI_CALL_ID/end" | jq '.'

echo ""
echo "14. Ending Customer-to-AI call..."
curl -s -X POST "$BASE_URL/api/calls/$CUSTOMER_CALL_ID/end" | jq '.'

# Test 15: Verify calls ended
echo ""
echo "15. Verifying AI-to-Customer call ended..."
curl -s "$BASE_URL/api/calls/$AI_CALL_ID" | jq '.'

echo ""
echo "16. Verifying Customer-to-AI call ended..."
curl -s "$BASE_URL/api/calls/$CUSTOMER_CALL_ID" | jq '.'

echo ""
echo "✅ API test completed successfully!"
echo ""
echo "📋 Summary:"
echo "🤖➡️👤 AI-to-Customer Flow:"
echo "  - AI initiated call with notifications sent"
echo "  - Customer joined the call"
echo "  - AI provided personalized greeting"
echo "  - Call ended with cleanup"
echo ""
echo "👤➡️🤖 Customer-to-AI Flow:"
echo "  - Customer initiated call to AI"
echo "  - AI immediately available (no ringing)"
echo "  - AI provided welcome greeting"
echo "  - Call ended with cleanup"
echo ""
echo "🔗 Next steps:"
echo "- Integrate real Agora Go Server SDK"
echo "- Add real AI services (OpenAI, etc.)"
echo "- Implement actual notification providers"
echo "- Add database persistence"
echo "- Add call routing and queue management"
