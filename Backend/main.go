package main

import (
	"Backend/config"
	"Backend/handlers"
	"Backend/middleware"
	"Backend/services"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	config.LoadConfig()
	cfg := config.AppConfig

	log.Printf("🚀 Starting AI Customer Support Backend")
	log.Printf("📋 Server: %s:%s", cfg.Server.Host, cfg.Server.Port)
	log.Printf("🎯 Agora App ID: %s", cfg.Agora.AppID)

	// Initialize services
	notificationService := services.NewNotificationService()
	agoraService := services.NewAgoraService(cfg)
	aiService := services.NewAIService(cfg)
	callManager := services.NewCallManager(notificationService, aiService, agoraService)

	// Initialize handlers
	callHandler := handlers.NewCallHandler(callManager, agoraService)

	// Setup Gin router
	router := setupRouter(callHandler)

	// Setup graceful shutdown
	setupGracefulShutdown(agoraService)

	// Start server
	address := fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port)
	log.Printf("✅ Server starting on %s", address)

	if err := router.Run(address); err != nil {
		log.Fatalf("❌ Failed to start server: %v", err)
	}
}

// setupRouter configures the Gin router with all routes and middleware
func setupRouter(callHandler *handlers.CallHandler) *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// Add middleware
	router.Use(middleware.LoggerMiddleware())
	router.Use(middleware.CORSMiddleware())
	router.Use(gin.Recovery())

	// Health check endpoint
	router.GET("/health", callHandler.HealthCheck)

	// API routes
	api := router.Group("/api")
	{
		// Call management routes
		calls := api.Group("/calls")
		{
			calls.POST("/initiate", callHandler.InitiateCall)                    // AI calls customer
			calls.POST("/customer-initiate", callHandler.InitiateCustomerCall)   // Customer calls AI
			calls.POST("/:call_id/join", callHandler.JoinCall)
			calls.GET("/:call_id", callHandler.GetCall)
			calls.POST("/:call_id/end", callHandler.EndCall)
			calls.GET("/active", callHandler.GetActiveCalls)
			calls.GET("/:call_id/stats", callHandler.GetCallStats)
			calls.POST("/:call_id/token", callHandler.GenerateToken)
		}
	}

	// Welcome route
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "🤖 AI Customer Support Backend",
			"version": "1.0.0",
			"status":  "running",
			"endpoints": gin.H{
				"health":                "GET /health",
				"initiate_call":         "POST /api/calls/initiate",
				"customer_initiate":     "POST /api/calls/customer-initiate",
				"join_call":             "POST /api/calls/:call_id/join",
				"get_call":              "GET /api/calls/:call_id",
				"end_call":              "POST /api/calls/:call_id/end",
				"active_calls":          "GET /api/calls/active",
				"call_stats":            "GET /api/calls/:call_id/stats",
				"generate_token":        "POST /api/calls/:call_id/token",
			},
		})
	})

	log.Printf("🛣️ Routes configured successfully")
	return router
}

// setupGracefulShutdown handles graceful shutdown of services
func setupGracefulShutdown(agoraService *services.AgoraService) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Printf("🛑 Shutting down gracefully...")

		// Cleanup Agora service
		if err := agoraService.Cleanup(); err != nil {
			log.Printf("❌ Error during Agora cleanup: %v", err)
		}

		log.Printf("✅ Shutdown complete")
		os.Exit(0)
	}()
}