package models

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// CallStatus represents the current state of a call
type CallStatus string

const (
	CallStatusPending    CallStatus = "pending"
	CallStatusRinging    CallStatus = "ringing"
	CallStatusConnected  CallStatus = "connected"
	CallStatusEnded      CallStatus = "ended"
	CallStatusFailed     CallStatus = "failed"
)

// CallType represents who initiated the call
type CallType string

const (
	CallTypeAIToCustomer   CallType = "ai_to_customer"   // AI calls customer (original flow)
	CallTypeCustomerToAI   CallType = "customer_to_ai"   // Customer calls AI (new flow)
)

// Call represents a customer support call session
type Call struct {
	ID          string       `json:"id"`
	CustomerID  string       `json:"customer_id"`
	ChannelName string       `json:"channel_name"`
	Status      CallStatus   `json:"status"`
	Type        CallType     `json:"type"`
	StartTime   time.Time    `json:"start_time"`
	EndTime     *time.Time   `json:"end_time,omitempty"`
	Duration    int64        `json:"duration"` // in seconds
	Transcript  []Message    `json:"transcript"`
	Metadata    CallMetadata `json:"metadata"`
}

// Message represents a single message in the conversation
type Message struct {
	ID        string    `json:"id"`
	Speaker   string    `json:"speaker"` // "ai" or "customer"
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
	Type      string    `json:"type"` // "text", "audio"
}

// CallMetadata contains additional information about the call
type CallMetadata struct {
	Purpose       string            `json:"purpose"`
	CustomerInfo  CustomerInfo      `json:"customer_info"`
	AIPersonality string            `json:"ai_personality"`
	Language      string            `json:"language"`
	Tags          []string          `json:"tags"`
	CustomFields  map[string]string `json:"custom_fields"`
}

// CustomerInfo contains customer details
type CustomerInfo struct {
	Name        string `json:"name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	AccountID   string `json:"account_id"`
	Preferences map[string]string `json:"preferences"`
}

// InitiateCallRequest represents the request to start a new call
type InitiateCallRequest struct {
	CustomerID    string            `json:"customer_id" binding:"required"`
	Purpose       string            `json:"purpose" binding:"required"`
	CustomerInfo  CustomerInfo      `json:"customer_info"`
	AIPersonality string            `json:"ai_personality"`
	Language      string            `json:"language"`
	CustomFields  map[string]string `json:"custom_fields"`
}

// InitiateCallResponse represents the response when initiating a call
type InitiateCallResponse struct {
	CallID      string `json:"call_id"`
	ChannelName string `json:"channel_name"`
	Token       string `json:"token"`
	Status      string `json:"status"`
	Message     string `json:"message"`
}

// JoinCallRequest represents the request when a user joins a call
type JoinCallRequest struct {
	CallID     string `json:"call_id"`
	CustomerID string `json:"customer_id" binding:"required"`
}

// JoinCallResponse represents the response when joining a call
type JoinCallResponse struct {
	ChannelName string `json:"channel_name"`
	Token       string `json:"token"`
	CallInfo    Call   `json:"call_info"`
}

// InitiateCustomerCallRequest represents the request when customer initiates call to AI
type InitiateCustomerCallRequest struct {
	UserID        int               `json:"user_id" binding:"required"`
	Purpose       string            `json:"purpose" binding:"required"`
	CustomerInfo  CustomerInfo      `json:"customer_info"`
	AIPersonality string            `json:"ai_personality"`
	Language      string            `json:"language"`
	Scenario      string            `json:"scenario"`      // e.g., "billing", "technical", "general"
	CustomFields  map[string]string `json:"custom_fields"`
}

// InitiateCustomerCallResponse represents the response when customer initiates call to AI
type InitiateCustomerCallResponse struct {
	CallID      string `json:"call_id"`
	ChannelName string `json:"channel_name"`
	Token       string `json:"token"`
	UserID      int    `json:"user_id"`
	Status      string `json:"status"`
	Message     string `json:"message"`
	AIInfo      AIInfo `json:"ai_info"`
}

// AIInfo contains information about the AI agent
type AIInfo struct {
	Name        string `json:"name"`
	Personality string `json:"personality"`
	Language    string `json:"language"`
	Capabilities []string `json:"capabilities"`
}

// CallEvent represents events that occur during a call
type CallEvent struct {
	CallID    string                 `json:"call_id"`
	EventType string                 `json:"event_type"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
}

// NewCall creates a new call instance
func NewCall(customerID, purpose string, customerInfo CustomerInfo) *Call {
	callID := uuid.New().String()
	channelName := "ai_support_" + callID

	return &Call{
		ID:          callID,
		CustomerID:  customerID,
		ChannelName: channelName,
		Status:      CallStatusPending,
		Type:        CallTypeAIToCustomer, // Default to AI calling customer
		StartTime:   time.Now(),
		Transcript:  make([]Message, 0),
		Metadata: CallMetadata{
			Purpose:       purpose,
			CustomerInfo:  customerInfo,
			AIPersonality: "helpful",
			Language:      "en",
			Tags:          make([]string, 0),
			CustomFields:  make(map[string]string),
		},
	}
}

// NewCustomerCall creates a new call instance where customer calls AI
func NewCustomerCall(userID int, purpose string, customerInfo CustomerInfo) *Call {
	callID := uuid.New().String()
	channelName := "customer_ai_" + callID

	return &Call{
		ID:          callID,
		CustomerID:  fmt.Sprintf("user_%d", userID), // Convert int userID to string for internal use
		ChannelName: channelName,
		Status:      CallStatusPending,
		Type:        CallTypeCustomerToAI, // Customer calling AI
		StartTime:   time.Now(),
		Transcript:  make([]Message, 0),
		Metadata: CallMetadata{
			Purpose:       purpose,
			CustomerInfo:  customerInfo,
			AIPersonality: "helpful",
			Language:      "en",
			Tags:          make([]string, 0),
			CustomFields:  make(map[string]string),
		},
	}
}

// AddMessage adds a new message to the call transcript
func (c *Call) AddMessage(speaker, content, messageType string) {
	message := Message{
		ID:        uuid.New().String(),
		Speaker:   speaker,
		Content:   content,
		Timestamp: time.Now(),
		Type:      messageType,
	}
	c.Transcript = append(c.Transcript, message)
}

// EndCall marks the call as ended and calculates duration
func (c *Call) EndCall() {
	now := time.Now()
	c.EndTime = &now
	c.Status = CallStatusEnded
	c.Duration = int64(now.Sub(c.StartTime).Seconds())
}

// UpdateStatus updates the call status
func (c *Call) UpdateStatus(status CallStatus) {
	c.Status = status
}
