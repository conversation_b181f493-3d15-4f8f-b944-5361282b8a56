package services

import (
	"Backend/config"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/AgoraIO/Tools/DynamicKey/AgoraDynamicKey/go/src/rtctokenbuilder2"
)

// AgoraService handles Agora RTC operations
type AgoraService struct {
	config *config.Config
	// In real implementation, this would contain:
	// - Agora service instance
	// - Media node factory
	// - Active connections map
	// - Audio/Video observers
}

// TokenRequest represents a request for Agora token
type TokenRequest struct {
	ChannelName string `json:"channel_name"`
	UserID      int    `json:"user_id"`
	Role        int    `json:"role"` // 1 = publisher, 2 = subscriber
}

// TokenResponse represents Agora token response
type TokenResponse struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
}

// NewAgoraService creates a new Agora service instance
func NewAgoraService(cfg *config.Config) *AgoraService {
	service := &AgoraService{
		config: cfg,
	}

	// In real implementation, initialize Agora SDK here:
	// 1. Initialize Agora service with app ID
	// 2. Create media node factory
	// 3. Set up audio/video processing callbacks

	log.Printf("🎯 Agora service initialized with App ID: %s", cfg.Agora.AppID)
	return service
}

// GenerateToken generates an Agora RTC token for channel access (legacy string-based method)
func (as *AgoraService) GenerateToken(channelName, userID string, role int) (*TokenResponse, error) {
	log.Printf("🔑 Generating Agora token for channel: %s, user: %s, role: %d", channelName, userID, role)

	// Token expires in 24 hours
	expirationTime := time.Now().Add(24 * time.Hour).Unix()

	var token string
	var err error

	if as.config.Agora.AppCertificate != "" {
		// Generate real token with official Agora library
		token, err = as.generateRealToken(channelName, userID, role, uint32(expirationTime))
		if err != nil {
			return nil, fmt.Errorf("failed to generate token: %w", err)
		}
		log.Printf("✅ Generated real Agora token for channel: %s, token is: %s", channelName, token)
	} else {
		// Generate mock token for development
		token = as.generateMockToken(channelName, userID, role)
		log.Printf("⚠️ [MOCK] Using mock token (no app certificate provided)")
	}

	response := &TokenResponse{
		Token:     token,
		ExpiresAt: expirationTime,
	}

	log.Printf("✅ Token generated successfully for channel: %s", channelName)
	return response, nil
}

// GenerateTokenWithUID generates an Agora RTC token using integer UID
func (as *AgoraService) GenerateTokenWithUID(channelName string, uid uint32, role int) (*TokenResponse, error) {
	log.Printf("🔑 Generating Agora token for channel: %s, UID: %d, role: %d", channelName, uid, role)

	// Token expires in 24 hours
	expirationTime := time.Now().Add(24 * time.Hour).Unix()

	var token string
	var err error

	if as.config.Agora.AppCertificate != "" {
		// Generate real token with official Agora library using UID directly
		token, err = as.generateRealTokenWithUID(channelName, uid, role, uint32(expirationTime))
		if err != nil {
			return nil, fmt.Errorf("failed to generate token: %w", err)
		}
		log.Printf("✅ Generated real Agora token for channel: %s, UID: %d, token: %s", channelName, uid, token)
	} else {
		// Generate mock token for development
		token = as.generateMockTokenWithUID(channelName, uid, role)
		log.Printf("⚠️ [MOCK] Using mock token (no app certificate provided)")
	}

	response := &TokenResponse{
		Token:     token,
		ExpiresAt: expirationTime,
	}

	log.Printf("✅ Token generated successfully for channel: %s, UID: %d", channelName, uid)
	return response, nil
}

// generateRealToken generates a real Agora token using the official Agora library
func (as *AgoraService) generateRealToken(channelName, userID string, role int, expiration uint32) (string, error) {
	// Convert userID to uint32 (Agora expects numeric UID)
	var uid uint32
	if userID != "" {
		if parsedUID, err := strconv.ParseUint(userID, 10, 32); err == nil {
			uid = uint32(parsedUID)
		} else {
			// If userID is not numeric, generate a hash-based UID
			uid = uint32(len(userID)) // Simple fallback, in production use a proper hash
		}
	} else {
		uid = 0 // Let Agora assign a UID
	}

	// Convert role to Agora role type
	var agoraRole rtctokenbuilder2.Role
	if role == 1 {
		agoraRole = rtctokenbuilder2.RolePublisher
	} else {
		agoraRole = rtctokenbuilder2.RoleSubscriber
	}

	// Generate token using official Agora library
	token, err := rtctokenbuilder2.BuildTokenWithUid(
		as.config.Agora.AppID,
		as.config.Agora.AppCertificate,
		channelName,
		uid,
		agoraRole,
		expiration,
		expiration, // Privilege expired timestamp (same as token expiration)
	)

	if err != nil {
		return "", fmt.Errorf("failed to build Agora token: %w", err)
	}

	return token, nil
}

// generateRealTokenWithUID generates a real Agora token using UID directly
func (as *AgoraService) generateRealTokenWithUID(channelName string, uid uint32, role int, expiration uint32) (string, error) {
	// Convert role to Agora role type
	var agoraRole rtctokenbuilder2.Role
	if role == 1 {
		agoraRole = rtctokenbuilder2.RolePublisher
	} else {
		agoraRole = rtctokenbuilder2.RoleSubscriber
	}

	// Generate token using official Agora library with UID directly
	token, err := rtctokenbuilder2.BuildTokenWithUid(
		as.config.Agora.AppID,
		as.config.Agora.AppCertificate,
		channelName,
		uid,
		agoraRole,
		expiration,
		expiration, // Privilege expired timestamp (same as token expiration)
	)

	if err != nil {
		return "", fmt.Errorf("failed to build Agora token: %w", err)
	}

	return token, nil
}

// generateMockToken generates a mock token for development
func (as *AgoraService) generateMockToken(channelName, userID string, role int) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("mock_token_%s_%s_%d_%d", channelName, userID, role, timestamp)
}

// generateMockTokenWithUID generates a mock token for development using UID
func (as *AgoraService) generateMockTokenWithUID(channelName string, uid uint32, role int) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("mock_token_%s_%d_%d_%d", channelName, uid, role, timestamp)
}

// JoinChannel joins an Agora channel for AI processing
func (as *AgoraService) JoinChannel(channelName, userID string) error {
	log.Printf("🔗 [MOCK] AI joining Agora channel: %s as user: %s", channelName, userID)

	// In real implementation, this would:
	// 1. Create RTC connection
	// 2. Set up local user
	// 3. Create audio track for AI voice
	// 4. Register audio frame observers for processing incoming audio
	// 5. Join the channel
	// 6. Start audio processing pipeline

	// Mock implementation
	time.Sleep(500 * time.Millisecond) // Simulate connection time

	log.Printf("✅ [MOCK] AI successfully joined channel: %s", channelName)
	return nil
}

// LeaveChannel leaves an Agora channel
func (as *AgoraService) LeaveChannel(callID string) error {
	channelName := as.getChannelName(callID)
	log.Printf("👋 [MOCK] AI leaving Agora channel: %s", channelName)

	// In real implementation, this would:
	// 1. Stop audio processing
	// 2. Unpublish audio tracks
	// 3. Disconnect from channel
	// 4. Clean up resources

	// Mock implementation
	time.Sleep(200 * time.Millisecond) // Simulate disconnection time

	log.Printf("✅ [MOCK] AI successfully left channel: %s", channelName)
	return nil
}

// SendAudioToChannel sends AI-generated audio to the channel
func (as *AgoraService) SendAudioToChannel(callID string, audioData []byte) error {
	channelName := as.getChannelName(callID)
	log.Printf("🔊 [MOCK] Sending %d bytes of audio to channel: %s", len(audioData), channelName)

	// In real implementation, this would:
	// 1. Convert audio data to required format (PCM)
	// 2. Push audio frames to Agora audio track
	// 3. Handle audio encoding and transmission

	// Mock implementation - simulate audio transmission time
	transmissionTime := time.Duration(len(audioData)/1000) * time.Millisecond
	if transmissionTime > 100*time.Millisecond {
		transmissionTime = 100 * time.Millisecond
	}
	time.Sleep(transmissionTime)

	log.Printf("✅ [MOCK] Audio sent successfully to channel: %s", channelName)
	return nil
}

// SetupAudioProcessing sets up audio processing callbacks
func (as *AgoraService) SetupAudioProcessing(callID string, audioCallback func([]byte) error) error {
	channelName := as.getChannelName(callID)
	log.Printf("🎙️ [MOCK] Setting up audio processing for channel: %s", channelName)

	// In real implementation, this would:
	// 1. Register audio frame observer
	// 2. Set up VAD (Voice Activity Detection)
	// 3. Configure audio format (sample rate, channels, etc.)
	// 4. Start processing incoming audio frames

	// Mock implementation - simulate receiving audio periodically
	go as.simulateIncomingAudio(callID, audioCallback)

	log.Printf("✅ [MOCK] Audio processing setup complete for channel: %s", channelName)
	return nil
}

// simulateIncomingAudio simulates receiving audio from the customer
func (as *AgoraService) simulateIncomingAudio(callID string, audioCallback func([]byte) error) {
	log.Printf("🎭 [MOCK] Starting audio simulation for call: %s", callID)

	// Simulate customer speaking every 5-10 seconds
	for i := 0; i < 5; i++ { // Simulate 5 customer messages
		time.Sleep(time.Duration(5+i*2) * time.Second)

		// Generate mock audio data
		mockAudioData := make([]byte, 1600) // Simulate 100ms of 16kHz mono audio
		for j := range mockAudioData {
			mockAudioData[j] = byte((i*100 + j) % 256)
		}

		log.Printf("🎤 [MOCK] Simulating incoming audio for call %s (message %d)", callID, i+1)

		if err := audioCallback(mockAudioData); err != nil {
			log.Printf("Error processing simulated audio: %v", err)
			break
		}
	}

	log.Printf("🎭 [MOCK] Audio simulation completed for call: %s", callID)
}

// GetChannelStats returns channel statistics
func (as *AgoraService) GetChannelStats(callID string) (map[string]interface{}, error) {
	channelName := as.getChannelName(callID)
	log.Printf("📊 [MOCK] Getting channel stats for: %s", channelName)

	// Mock statistics
	stats := map[string]interface{}{
		"channel_name":     channelName,
		"connected_users":  2, // AI + Customer
		"audio_bitrate":    64000,
		"packet_loss_rate": 0.01,
		"rtt":              45, // Round trip time in ms
		"audio_quality":    "good",
		"connection_state": "connected",
	}

	return stats, nil
}

// getChannelName generates channel name from call ID
func (as *AgoraService) getChannelName(callID string) string {
	return as.config.Agora.ChannelPrefix + callID
}

// ValidateChannel checks if a channel exists and is accessible
func (as *AgoraService) ValidateChannel(channelName string) error {
	log.Printf("✅ [MOCK] Channel validation passed for: %s", channelName)
	return nil
}

// GetActiveChannels returns list of active channels
func (as *AgoraService) GetActiveChannels() ([]string, error) {
	// Mock active channels
	channels := []string{
		as.config.Agora.ChannelPrefix + "call_1",
		as.config.Agora.ChannelPrefix + "call_2",
	}

	log.Printf("📋 [MOCK] Active channels: %v", channels)
	return channels, nil
}

// Cleanup performs cleanup when shutting down
func (as *AgoraService) Cleanup() error {
	log.Printf("🧹 [MOCK] Cleaning up Agora service")

	// In real implementation, this would:
	// 1. Leave all active channels
	// 2. Release media node factory
	// 3. Release Agora service
	// 4. Clean up all resources

	log.Printf("✅ [MOCK] Agora service cleanup completed")
	return nil
}
