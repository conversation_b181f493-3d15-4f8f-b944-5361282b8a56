package services

import (
	"Backend/models"
	"fmt"
	"log"
	"sync"
	"time"
)

// CallManager manages active calls and their lifecycle
type CallManager struct {
	calls               map[string]*models.Call
	mutex               sync.RWMutex
	notificationService *NotificationService
	aiService          *AIService
	agoraService       *AgoraService
}

// NewCallManager creates a new call manager instance
func NewCallManager(notificationService *NotificationService, aiService *AIService, agoraService *AgoraService) *CallManager {
	return &CallManager{
		calls:               make(map[string]*models.Call),
		notificationService: notificationService,
		aiService:          aiService,
		agoraService:       agoraService,
	}
}

// InitiateCall starts a new AI customer support call (AI calls customer)
func (cm *CallManager) InitiateCall(request models.InitiateCallRequest) (*models.Call, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	log.Printf("🚀 Initiating new AI-to-customer call for customer: %s, purpose: %s", request.CustomerID, request.Purpose)

	// Create new call
	call := models.NewCall(request.CustomerID, request.Purpose, request.CustomerInfo)

	// Set additional metadata
	if request.AIPersonality != "" {
		call.Metadata.AIPersonality = request.AIPersonality
	}
	if request.Language != "" {
		call.Metadata.Language = request.Language
	}
	if request.CustomFields != nil {
		call.Metadata.CustomFields = request.CustomFields
	}

	// Store the call
	cm.calls[call.ID] = call

	// Send notification to customer
	if err := cm.notificationService.SendCallNotification(call); err != nil {
		log.Printf("Failed to send notification for call %s: %v", call.ID, err)
		// Don't fail the call creation if notification fails
	}

	// Update call status to ringing
	call.UpdateStatus(models.CallStatusRinging)

	// Set up call timeout (auto-end if not answered within 2 minutes)
	go cm.setupCallTimeout(call.ID, 2*time.Minute)

	log.Printf("✅ AI-to-customer call initiated successfully: %s", call.ID)
	return call, nil
}

// InitiateCustomerCall starts a new customer-to-AI call (customer calls AI)
func (cm *CallManager) InitiateCustomerCall(request models.InitiateCustomerCallRequest) (*models.Call, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	log.Printf("📞 Initiating new customer-to-AI call for user ID: %d, purpose: %s", request.UserID, request.Purpose)

	// Create new customer call
	call := models.NewCustomerCall(request.UserID, request.Purpose, request.CustomerInfo)

	// Set additional metadata
	if request.AIPersonality != "" {
		call.Metadata.AIPersonality = request.AIPersonality
	}
	if request.Language != "" {
		call.Metadata.Language = request.Language
	}
	if request.CustomFields != nil {
		call.Metadata.CustomFields = request.CustomFields
	}
	if request.Scenario != "" {
		call.Metadata.Tags = append(call.Metadata.Tags, "scenario:"+request.Scenario)
	}

	// Store the call
	cm.calls[call.ID] = call

	// For customer-to-AI calls, the AI is immediately available
	// Update call status to connected (no ringing phase)
	call.UpdateStatus(models.CallStatusConnected)

	log.Printf("✅ Customer-to-AI call initiated successfully: %s", call.ID)
	return call, nil
}

// JoinCall allows a customer to join an existing call
func (cm *CallManager) JoinCall(callID, customerID string) (*models.Call, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	call, exists := cm.calls[callID]
	if !exists {
		return nil, fmt.Errorf("call not found: %s", callID)
	}

	if call.CustomerID != customerID {
		return nil, fmt.Errorf("unauthorized: customer %s cannot join call %s", customerID, callID)
	}

	if call.Status != models.CallStatusRinging && call.Status != models.CallStatusPending {
		return nil, fmt.Errorf("call %s is not available for joining (status: %s)", callID, call.Status)
	}

	log.Printf("👤 Customer %s joining call %s", customerID, callID)

	// Update call status to connected
	call.UpdateStatus(models.CallStatusConnected)

	// Send status update notification
	if err := cm.notificationService.SendCallStatusUpdate(call, models.CallStatusConnected); err != nil {
		log.Printf("Failed to send status update for call %s: %v", callID, err)
	}

	// Add initial AI greeting
	greeting := cm.generateInitialGreeting(call)
	call.AddMessage("ai", greeting, "text")

	log.Printf("🎉 Customer successfully joined call %s", callID)
	return call, nil
}

// ProcessAudioMessage processes incoming audio from the customer
func (cm *CallManager) ProcessAudioMessage(callID string, audioData []byte) error {
	cm.mutex.RLock()
	call, exists := cm.calls[callID]
	cm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("call not found: %s", callID)
	}

	if call.Status != models.CallStatusConnected {
		return fmt.Errorf("call %s is not active (status: %s)", callID, call.Status)
	}

	log.Printf("🎤 Processing audio message for call %s (%d bytes)", callID, len(audioData))

	// Process with VAD first
	isSpeech, err := cm.aiService.ProcessVAD(audioData)
	if err != nil {
		log.Printf("VAD processing failed for call %s: %v", callID, err)
		return err
	}

	if !isSpeech {
		log.Printf("🔇 No speech detected in audio for call %s", callID)
		return nil // Not an error, just no speech
	}

	// Convert speech to text
	sttResponse, err := cm.aiService.ProcessSpeechToText(audioData)
	if err != nil {
		log.Printf("STT processing failed for call %s: %v", callID, err)
		return err
	}

	if sttResponse.Text == "" {
		log.Printf("📝 Empty transcription for call %s", callID)
		return nil
	}

	log.Printf("📝 Transcribed: '%s' (confidence: %.2f)", sttResponse.Text, sttResponse.Confidence)

	// Add customer message to transcript
	call.AddMessage("customer", sttResponse.Text, "text")

	// Generate AI response
	aiResponse, err := cm.aiService.GenerateAIResponse(call, sttResponse.Text)
	if err != nil {
		log.Printf("AI response generation failed for call %s: %v", callID, err)
		return err
	}

	// Add AI response to transcript
	call.AddMessage("ai", aiResponse, "text")

	// Convert AI response to speech and send back
	go cm.sendAIAudioResponse(callID, aiResponse)

	return nil
}

// sendAIAudioResponse converts AI text response to audio and sends it back
func (cm *CallManager) sendAIAudioResponse(callID, text string) {
	log.Printf("🔊 Generating audio response for call %s", callID)

	// Convert text to speech
	audioData, err := cm.aiService.ConvertTextToSpeech(text)
	if err != nil {
		log.Printf("TTS processing failed for call %s: %v", callID, err)
		return
	}

	// Send audio through Agora (mock implementation)
	if err := cm.agoraService.SendAudioToChannel(callID, audioData); err != nil {
		log.Printf("Failed to send audio to channel for call %s: %v", callID, err)
		return
	}

	log.Printf("✅ Audio response sent for call %s", callID)
}

// EndCall terminates a call
func (cm *CallManager) EndCall(callID string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	call, exists := cm.calls[callID]
	if !exists {
		return fmt.Errorf("call not found: %s", callID)
	}

	log.Printf("🏁 Ending call %s", callID)

	// Update call status and calculate duration
	call.EndCall()

	// Send call ended notification
	if err := cm.notificationService.SendCallEndedNotification(call); err != nil {
		log.Printf("Failed to send call ended notification for call %s: %v", callID, err)
	}

	// Clean up Agora resources
	if err := cm.agoraService.LeaveChannel(callID); err != nil {
		log.Printf("Failed to leave Agora channel for call %s: %v", callID, err)
	}

	log.Printf("✅ Call %s ended successfully (duration: %d seconds)", callID, call.Duration)
	return nil
}

// GetCall retrieves a call by ID
func (cm *CallManager) GetCall(callID string) (*models.Call, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	call, exists := cm.calls[callID]
	if !exists {
		return nil, fmt.Errorf("call not found: %s", callID)
	}

	return call, nil
}

// GetActiveCalls returns all active calls
func (cm *CallManager) GetActiveCalls() []*models.Call {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	var activeCalls []*models.Call
	for _, call := range cm.calls {
		if call.Status == models.CallStatusConnected || call.Status == models.CallStatusRinging {
			activeCalls = append(activeCalls, call)
		}
	}

	return activeCalls
}

// setupCallTimeout sets up automatic call termination if not answered
func (cm *CallManager) setupCallTimeout(callID string, timeout time.Duration) {
	time.Sleep(timeout)

	cm.mutex.RLock()
	call, exists := cm.calls[callID]
	cm.mutex.RUnlock()

	if !exists {
		return // Call was already removed
	}

	if call.Status == models.CallStatusRinging || call.Status == models.CallStatusPending {
		log.Printf("⏰ Call %s timed out, marking as failed", callID)

		cm.mutex.Lock()
		call.UpdateStatus(models.CallStatusFailed)
		cm.mutex.Unlock()

		// Send timeout notification
		cm.notificationService.SendCallStatusUpdate(call, models.CallStatusFailed)
	}
}

// generateInitialGreeting creates a personalized greeting for the customer
func (cm *CallManager) generateInitialGreeting(call *models.Call) string {
	customerName := call.Metadata.CustomerInfo.Name
	if customerName == "" {
		customerName = "there"
	}

	purpose := call.Metadata.Purpose
	if purpose == "" {
		purpose = "your inquiry"
	}

	// Different greetings based on call type
	switch call.Type {
	case models.CallTypeAIToCustomer:
		// AI initiated the call to customer
		return fmt.Sprintf("Hello %s! Thank you for connecting. I'm your AI customer support assistant, and I'm here to help you with %s. How can I assist you today?", customerName, purpose)
	case models.CallTypeCustomerToAI:
		// Customer initiated the call to AI
		return fmt.Sprintf("Hello %s! Welcome to AI customer support. I understand you're calling about %s. I'm here to help you resolve this quickly. What specific issue can I assist you with?", customerName, purpose)
	default:
		return fmt.Sprintf("Hello %s! I'm your AI assistant. How can I help you today?", customerName)
	}
}
