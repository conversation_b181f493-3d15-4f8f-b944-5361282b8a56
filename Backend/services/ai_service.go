package services

import (
	"Backend/config"
	"Backend/models"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

// AIService handles all AI-related operations
type AIService struct {
	config *config.Config
	client *http.Client
}

// STTResponse represents speech-to-text response
type STTResponse struct {
	Text       string  `json:"text"`
	Confidence float64 `json:"confidence"`
	Language   string  `json:"language"`
}

// LLMRequest represents a request to the language model
type LLMRequest struct {
	Model    string    `json:"model"`
	Messages []LLMMessage `json:"messages"`
	Temperature float64 `json:"temperature"`
	MaxTokens   int     `json:"max_tokens"`
}

// LLMMessage represents a message in the conversation
type LLMMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// LLMResponse represents the language model response
type LLMResponse struct {
	Choices []struct {
		Message LLMMessage `json:"message"`
	} `json:"choices"`
}

// TTSRequest represents text-to-speech request
type TTSRequest struct {
	Model string `json:"model"`
	Input string `json:"input"`
	Voice string `json:"voice"`
}

// NewAIService creates a new AI service instance
func NewAIService(cfg *config.Config) *AIService {
	return &AIService{
		config: cfg,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ProcessSpeechToText converts audio to text using STT
func (ai *AIService) ProcessSpeechToText(audioData []byte) (*STTResponse, error) {
	log.Printf("🎤 [MOCK] Processing speech-to-text for %d bytes of audio", len(audioData))
	
	// Mock STT processing - in real implementation, this would:
	// 1. Send audio to OpenAI Whisper API or other STT service
	// 2. Handle different audio formats
	// 3. Return transcribed text with confidence scores
	
	// Simulate processing time
	time.Sleep(500 * time.Millisecond)
	
	// Mock responses based on audio length to simulate realistic behavior
	mockResponses := []string{
		"Hello, I need help with my account",
		"Can you help me reset my password?",
		"I'm having trouble with my billing",
		"What are your business hours?",
		"I want to cancel my subscription",
		"How do I update my payment method?",
		"Yes, that sounds good",
		"No, that's not what I meant",
		"Can you repeat that please?",
		"Thank you for your help",
	}
	
	// Simple mock selection based on audio data length
	responseIndex := len(audioData) % len(mockResponses)
	
	response := &STTResponse{
		Text:       mockResponses[responseIndex],
		Confidence: 0.85 + float64(len(audioData)%15)/100, // Mock confidence between 0.85-1.0
		Language:   "en",
	}
	
	log.Printf("📝 [MOCK] STT Result: '%s' (confidence: %.2f)", response.Text, response.Confidence)
	return response, nil
}

// GenerateAIResponse generates AI response using LLM
func (ai *AIService) GenerateAIResponse(call *models.Call, userMessage string) (string, error) {
	log.Printf("🤖 Processing AI response for call %s", call.ID)
	
	// Build conversation context
	messages := ai.buildConversationContext(call, userMessage)
	
	// In a real implementation, this would call OpenAI API
	if ai.config.AI.OpenAIAPIKey != "" {
		return ai.callOpenAIAPI(messages)
	}
	
	// Mock AI response
	return ai.generateMockAIResponse(userMessage, call.Metadata.Purpose)
}

// buildConversationContext creates the conversation context for the LLM
func (ai *AIService) buildConversationContext(call *models.Call, userMessage string) []LLMMessage {
	messages := []LLMMessage{
		{
			Role: "system",
			Content: fmt.Sprintf(`You are a helpful AI customer support agent. 
Customer Info: %s
Call Purpose: %s
Instructions: Be helpful, professional, and concise. Address the customer's needs efficiently.`,
				call.Metadata.CustomerInfo.Name,
				call.Metadata.Purpose),
		},
	}
	
	// Add conversation history
	for _, msg := range call.Transcript {
		role := "user"
		if msg.Speaker == "ai" {
			role = "assistant"
		}
		messages = append(messages, LLMMessage{
			Role:    role,
			Content: msg.Content,
		})
	}
	
	// Add current user message
	messages = append(messages, LLMMessage{
		Role:    "user",
		Content: userMessage,
	})
	
	return messages
}

// callOpenAIAPI makes actual API call to OpenAI
func (ai *AIService) callOpenAIAPI(messages []LLMMessage) (string, error) {
	request := LLMRequest{
		Model:       ai.config.AI.OpenAIModel,
		Messages:    messages,
		Temperature: 0.7,
		MaxTokens:   150,
	}
	
	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}
	
	req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+ai.config.AI.OpenAIAPIKey)
	
	resp, err := ai.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}
	
	var llmResponse LLMResponse
	if err := json.Unmarshal(body, &llmResponse); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}
	
	if len(llmResponse.Choices) == 0 {
		return "", fmt.Errorf("no response from LLM")
	}
	
	return llmResponse.Choices[0].Message.Content, nil
}

// generateMockAIResponse generates mock AI responses
func (ai *AIService) generateMockAIResponse(userMessage, purpose string) (string, error) {
	log.Printf("🎭 [MOCK] Generating AI response for: '%s'", userMessage)
	
	// Simulate processing time
	time.Sleep(800 * time.Millisecond)
	
	userLower := strings.ToLower(userMessage)
	
	// Context-aware responses based on user input
	switch {
	case strings.Contains(userLower, "hello") || strings.Contains(userLower, "hi"):
		return fmt.Sprintf("Hello! I'm here to help you with %s. How can I assist you today?", purpose), nil
	case strings.Contains(userLower, "password") || strings.Contains(userLower, "reset"):
		return "I can help you reset your password. I'll send a reset link to your registered email address. Please check your inbox and follow the instructions.", nil
	case strings.Contains(userLower, "billing") || strings.Contains(userLower, "payment"):
		return "I understand you have a billing question. Let me look up your account details. Can you please confirm your account email address?", nil
	case strings.Contains(userLower, "cancel") || strings.Contains(userLower, "subscription"):
		return "I'm sorry to hear you want to cancel. Before we proceed, may I ask what's prompting this decision? Perhaps I can help resolve any issues you're experiencing.", nil
	case strings.Contains(userLower, "hours") || strings.Contains(userLower, "time"):
		return "Our customer support is available 24/7 through this AI system. For human agents, we're available Monday through Friday, 9 AM to 6 PM EST.", nil
	case strings.Contains(userLower, "thank"):
		return "You're very welcome! Is there anything else I can help you with today?", nil
	case strings.Contains(userLower, "yes"):
		return "Great! Let me proceed with that for you. I'll take care of it right away.", nil
	case strings.Contains(userLower, "no"):
		return "I understand. Let me know if you'd like to explore other options or if there's anything else I can help you with.", nil
	default:
		return "I understand your concern. Let me help you with that. Could you provide a bit more detail so I can give you the most accurate assistance?", nil
	}
}

// ConvertTextToSpeech converts text to speech audio
func (ai *AIService) ConvertTextToSpeech(text string) ([]byte, error) {
	log.Printf("🔊 [MOCK] Converting text to speech: '%s'", text)
	
	// In a real implementation, this would:
	// 1. Call OpenAI TTS API or other TTS service
	// 2. Return audio bytes in the required format
	// 3. Handle different voices and languages
	
	// Simulate processing time based on text length
	processingTime := time.Duration(len(text)*10) * time.Millisecond
	if processingTime > 2*time.Second {
		processingTime = 2 * time.Second
	}
	time.Sleep(processingTime)
	
	// Mock audio data - in real implementation, this would be actual audio bytes
	mockAudioData := make([]byte, len(text)*100) // Simulate audio data size
	for i := range mockAudioData {
		mockAudioData[i] = byte(i % 256)
	}
	
	log.Printf("🎵 [MOCK] Generated %d bytes of audio data", len(mockAudioData))
	return mockAudioData, nil
}

// ProcessVAD performs Voice Activity Detection
func (ai *AIService) ProcessVAD(audioData []byte) (bool, error) {
	if !ai.config.AI.EnableVAD {
		return true, nil // Always consider as speech if VAD is disabled
	}
	
	// Mock VAD processing
	// In real implementation, this would use Agora's VAD or other VAD algorithms
	
	// Simple mock: consider it speech if audio data has sufficient "energy"
	if len(audioData) < 100 {
		return false, nil // Too short to be speech
	}
	
	// Mock energy calculation
	var energy int64
	for _, sample := range audioData {
		energy += int64(sample * sample)
	}
	
	avgEnergy := float64(energy) / float64(len(audioData))
	threshold := 1000.0 * ai.config.AI.VADSensitivity
	
	isSpeech := avgEnergy > threshold
	log.Printf("🎙️ [MOCK] VAD: Energy=%.2f, Threshold=%.2f, IsSpeech=%v", avgEnergy, threshold, isSpeech)
	
	return isSpeech, nil
}
