package handlers

import (
	"Backend/models"
	"Backend/services"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// CallHandler handles HTTP requests related to calls
type CallHandler struct {
	callManager  *services.CallManager
	agoraService *services.AgoraService
}

// NewCallHandler creates a new call handler instance
func NewCallHandler(callManager *services.CallManager, agoraService *services.AgoraService) *CallHandler {
	return &CallHandler{
		callManager:  callManager,
		agoraService: agoraService,
	}
}

// InitiateCall handles POST /api/calls/initiate
func (ch *CallHandler) InitiateCall(c *gin.Context) {
	var request models.InitiateCallRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if request.CustomerID == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"error": "customer_id is required",
		})
		return
	}

	if request.Purpose == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "purpose is required",
		})
		return
	}

	// Initiate the call
	call, err := ch.callManager.InitiateCall(request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to initiate call",
			"details": err.Error(),
		})
		return
	}

	// Generate Agora token for the channel
	token, err := ch.agoraService.GenerateToken(call.ChannelName, "ai_agent", 1) // Role 1 = publisher
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate Agora token",
			"details": err.Error(),
		})
		return
	}

	// Join the channel as AI agent
	if err := ch.agoraService.JoinChannel(call.ChannelName, "ai_agent"); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to join Agora channel",
			"details": err.Error(),
		})
		return
	}

	// Set up audio processing for this call
	audioCallback := func(audioData []byte) error {
		return ch.callManager.ProcessAudioMessage(call.ID, audioData)
	}

	if err := ch.agoraService.SetupAudioProcessing(call.ID, audioCallback); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to setup audio processing",
			"details": err.Error(),
		})
		return
	}

	response := models.InitiateCallResponse{
		CallID:      call.ID,
		ChannelName: call.ChannelName,
		Token:       token.Token,
		Status:      string(call.Status),
		Message:     "Call initiated successfully. Notification sent to customer.",
	}

	c.JSON(http.StatusCreated, response)
}

// InitiateCustomerCall handles POST /api/calls/customer-initiate
func (ch *CallHandler) InitiateCustomerCall(c *gin.Context) {
	var request models.InitiateCustomerCallRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if request.UserID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "user_id is required",
		})
		return
	}

	if request.Purpose == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "purpose is required",
		})
		return
	}

	// Initiate the customer-to-AI call
	call, err := ch.callManager.InitiateCustomerCall(request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to initiate customer call",
			"details": err.Error(),
		})
		return
	}

	// Generate Agora token for the customer using integer user ID
	customerToken, err := ch.agoraService.GenerateTokenWithUID(call.ChannelName, uint32(request.UserID), 1) // Role 1 = publisher
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate customer Agora token",
			"details": err.Error(),
		})
		return
	}

	// AI joins the channel immediately for customer-to-AI calls
	if err := ch.agoraService.JoinChannel(call.ChannelName, "ai_agent"); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to join Agora channel",
			"details": err.Error(),
		})
		return
	}

	// Set up audio processing for this call
	audioCallback := func(audioData []byte) error {
		return ch.callManager.ProcessAudioMessage(call.ID, audioData)
	}

	if err := ch.agoraService.SetupAudioProcessing(call.ID, audioCallback); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to setup audio processing",
			"details": err.Error(),
		})
		return
	}

	// Add initial AI greeting
	greeting := ch.generateCustomerCallGreeting(call)
	call.AddMessage("ai", greeting, "text")

	// Prepare AI info
	aiInfo := models.AIInfo{
		Name:        "AI Support Assistant",
		Personality: call.Metadata.AIPersonality,
		Language:    call.Metadata.Language,
		Capabilities: []string{
			"billing_support",
			"technical_support",
			"account_management",
			"general_inquiries",
			"password_reset",
			"subscription_management",
		},
	}

	response := models.InitiateCustomerCallResponse{
		CallID:      call.ID,
		ChannelName: call.ChannelName,
		Token:       customerToken.Token,
		UserID:      request.UserID,
		Status:      string(call.Status),
		Message:     "Customer call initiated successfully. AI is ready to assist.",
		AIInfo:      aiInfo,
	}

	c.JSON(http.StatusCreated, response)
}

// generateCustomerCallGreeting creates a greeting for customer-initiated calls
func (ch *CallHandler) generateCustomerCallGreeting(call *models.Call) string {
	customerName := call.Metadata.CustomerInfo.Name
	if customerName == "" {
		customerName = "there"
	}

	purpose := call.Metadata.Purpose
	if purpose == "" {
		purpose = "your inquiry"
	}

	return fmt.Sprintf("Hello %s! Welcome to AI customer support. I understand you're calling about %s. I'm here to help you resolve this quickly. What specific issue can I assist you with?", customerName, purpose)
}

// JoinCall handles POST /api/calls/:call_id/join
func (ch *CallHandler) JoinCall(c *gin.Context) {
	callID := c.Param("call_id")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "call_id is required",
		})
		return
	}

	var request models.JoinCallRequest
	request.CallID = callID // Set call ID from URL parameter first

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate customer ID
	if request.CustomerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "customer_id is required",
		})
		return
	}

	// Join the call
	call, err := ch.callManager.JoinCall(request.CallID, request.CustomerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to join call",
			"details": err.Error(),
		})
		return
	}

	// Generate Agora token for the customer
	token, err := ch.agoraService.GenerateToken(call.ChannelName, request.CustomerID, 1) // Role 1 = publisher
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate Agora token",
			"details": err.Error(),
		})
		return
	}

	response := models.JoinCallResponse{
		ChannelName: call.ChannelName,
		Token:       token.Token,
		CallInfo:    *call,
	}

	c.JSON(http.StatusOK, response)
}

// GetCall handles GET /api/calls/:call_id
func (ch *CallHandler) GetCall(c *gin.Context) {
	callID := c.Param("call_id")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "call_id is required",
		})
		return
	}

	call, err := ch.callManager.GetCall(callID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Call not found",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, call)
}

// EndCall handles POST /api/calls/:call_id/end
func (ch *CallHandler) EndCall(c *gin.Context) {
	callID := c.Param("call_id")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "call_id is required",
		})
		return
	}

	if err := ch.callManager.EndCall(callID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Failed to end call",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Call ended successfully",
		"call_id": callID,
	})
}

// GetActiveCalls handles GET /api/calls/active
func (ch *CallHandler) GetActiveCalls(c *gin.Context) {
	calls := ch.callManager.GetActiveCalls()

	c.JSON(http.StatusOK, gin.H{
		"active_calls": calls,
		"count":        len(calls),
	})
}

// GetCallStats handles GET /api/calls/:call_id/stats
func (ch *CallHandler) GetCallStats(c *gin.Context) {
	callID := c.Param("call_id")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "call_id is required",
		})
		return
	}

	// Get call info
	call, err := ch.callManager.GetCall(callID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Call not found",
			"details": err.Error(),
		})
		return
	}

	// Get Agora channel stats
	agoraStats, err := ch.agoraService.GetChannelStats(callID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get channel stats",
			"details": err.Error(),
		})
		return
	}

	stats := gin.H{
		"call_info":     call,
		"agora_stats":   agoraStats,
		"message_count": len(call.Transcript),
	}

	c.JSON(http.StatusOK, stats)
}

// GenerateToken handles POST /api/calls/:call_id/token
func (ch *CallHandler) GenerateToken(c *gin.Context) {
	callID := c.Param("call_id")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "call_id is required",
		})
		return
	}

	var request struct {
		UserID string `json:"user_id" binding:"required"`
		Role   int    `json:"role"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Default role to publisher if not specified
	if request.Role == 0 {
		request.Role = 1
	}

	// Get call to validate it exists
	call, err := ch.callManager.GetCall(callID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Call not found",
			"details": err.Error(),
		})
		return
	}

	// Generate token
	token, err := ch.agoraService.GenerateToken(call.ChannelName, request.UserID, request.Role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate token",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":        token.Token,
		"expires_at":   token.ExpiresAt,
		"channel_name": call.ChannelName,
		"user_id":      request.UserID,
		"role":         request.Role,
	})
}

// HealthCheck handles GET /api/health
func (ch *CallHandler) HealthCheck(c *gin.Context) {
	// Get active channels from Agora
	activeChannels, err := ch.agoraService.GetActiveChannels()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": "unhealthy",
			"error":  err.Error(),
		})
		return
	}

	activeCalls := ch.callManager.GetActiveCalls()

	c.JSON(http.StatusOK, gin.H{
		"status":          "healthy",
		"active_calls":    len(activeCalls),
		"active_channels": len(activeChannels),
		"timestamp":       time.Now().Unix(),
	})
}
