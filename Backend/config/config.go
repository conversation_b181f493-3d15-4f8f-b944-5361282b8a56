package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	Server ServerConfig
	Agora  AgoraConfig
	AI     AIConfig
}

type ServerConfig struct {
	Port string
	Host string
}

type AgoraConfig struct {
	AppID          string
	AppCertificate string
	ChannelPrefix  string
}

type AIConfig struct {
	OpenAIAPIKey    string
	OpenAIModel     string
	STTProvider     string
	TTSProvider     string
	VoiceID         string
	EnableVAD       bool
	VADSensitivity  float64
}

var AppConfig *Config

func LoadConfig() {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	AppConfig = &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
		},
		Agora: AgoraConfig{
			AppID:          getEnv("AGORA_APP_ID", ""),
			AppCertificate: getEnv("AGORA_APP_CERTIFICATE", ""),
			ChannelPrefix:  getEnv("AGORA_CHANNEL_PREFIX", "ai_support_"),
		},
		AI: AIConfig{
			OpenAIAPIKey:    getEnv("OPENAI_API_KEY", ""),
			OpenAIModel:     getEnv("OPENAI_MODEL", "gpt-4"),
			STTProvider:     getEnv("STT_PROVIDER", "openai"),
			TTSProvider:     getEnv("TTS_PROVIDER", "openai"),
			VoiceID:         getEnv("TTS_VOICE_ID", "alloy"),
			EnableVAD:       getEnvBool("ENABLE_VAD", true),
			VADSensitivity:  getEnvFloat("VAD_SENSITIVITY", 0.7),
		},
	}

	// Validate required fields
	if AppConfig.Agora.AppID == "" {
		log.Fatal("AGORA_APP_ID is required")
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseFloat(value, 64); err == nil {
			return parsed
		}
	}
	return defaultValue
}
