<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>