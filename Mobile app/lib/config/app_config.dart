class AppConfig {
  // TODO: Replace with your actual Agora App ID from the backend configuration
  // You can find this in Backend/config/.env file as AGORA_APP_ID
  static const String agoraAppId = '014523480d9544be800a014f35bdcfac';
  
  // Backend server configuration
  // For development, update this to match your backend server
  static const String backendBaseUrl = 'http://************:8080';
  
  // Alternative URLs for different environments:
  // For Android emulator: 'http://********:8080'
  // For iOS simulator: 'http://localhost:8080'
  // For physical devices: 'http://YOUR_COMPUTER_IP:8080'
  
  // Validate configuration
  static bool get isConfigured {
    return agoraAppId != 'your_agora_app_id_here' && agoraAppId.isNotEmpty;
  }
  
  static String get configurationInstructions {
    return '''
To configure the app:

1. Open Backend/config/.env and copy the AGORA_APP_ID value
2. Replace 'your_agora_app_id_here' in lib/config/app_config.dart with your actual Agora App ID
3. Update backendBaseUrl if your backend is running on a different host/port
4. Make sure your backend server is running on $backendBaseUrl

For physical devices, replace 'localhost' with your computer's IP address.
''';
  }
}
