import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/call_models.dart';
import '../config/app_config.dart';

class ApiService {
  // Backend URL from configuration
  static String get baseUrl => AppConfig.backendBaseUrl;

  static const Duration timeoutDuration = Duration(seconds: 30);

  /// Initiates a customer-to-AI call
  Future<InitiateCustomerCallResponse> initiateCustomerCall(
    InitiateCustomerCallRequest request,
  ) async {
    try {
      final url = Uri.parse('$baseUrl/api/calls/customer-initiate');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(request.toJson()),
      ).timeout(timeoutDuration);

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        return InitiateCustomerCallResponse.fromJson(responseData);
      } else {
        final errorData = jsonDecode(response.body);
        final apiError = ApiError.fromJson(errorData);
        throw ApiException(apiError.toString(), response.statusCode);
      }
    } on SocketException {
      throw ApiException('No internet connection. Please check your network.', 0);
    } on HttpException {
      throw ApiException('Failed to connect to server.', 0);
    } on FormatException {
      throw ApiException('Invalid response format from server.', 0);
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Unexpected error: ${e.toString()}', 0);
    }
  }

  /// Ends a call
  Future<void> endCall(String callId) async {
    try {
      final url = Uri.parse('$baseUrl/api/calls/$callId/end');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(timeoutDuration);

      if (response.statusCode != 200) {
        final errorData = jsonDecode(response.body);
        final apiError = ApiError.fromJson(errorData);
        throw ApiException(apiError.toString(), response.statusCode);
      }
    } on SocketException {
      throw ApiException('No internet connection. Please check your network.', 0);
    } on HttpException {
      throw ApiException('Failed to connect to server.', 0);
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Failed to end call: ${e.toString()}', 0);
    }
  }

  /// Checks server health
  Future<bool> checkServerHealth() async {
    try {
      final url = Uri.parse('$baseUrl/health');

      final response = await http.get(url).timeout(
        const Duration(seconds: 5),
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Gets the appropriate base URL for the current platform
  static String getBaseUrl() {
    // This is a simple implementation. In a real app, you might want to
    // make this configurable or detect the platform automatically
    return baseUrl;
  }
}

class ApiException implements Exception {
  final String message;
  final int statusCode;

  ApiException(this.message, this.statusCode);

  @override
  String toString() => message;
}
