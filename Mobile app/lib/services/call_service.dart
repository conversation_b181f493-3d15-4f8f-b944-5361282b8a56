import 'dart:async';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/call_models.dart';

class CallService {
  static CallService? _instance;
  static CallService get instance => _instance ??= CallService._();

  CallService._();

  RtcEngine? _engine;
  StreamController<CallStatus>? _callStatusController;
  StreamController<String>? _errorController;

  String? _currentCallId;
  String? _currentChannelName;
  bool _isInCall = false;

  Stream<CallStatus> get callStatusStream =>
      _callStatusController?.stream ?? const Stream.empty();

  Stream<String> get errorStream =>
      _errorController?.stream ?? const Stream.empty();

  bool get isInCall => _isInCall;
  String? get currentCallId => _currentCallId;

  /// Initialize the Agora RTC Engine
  Future<void> initialize({required String appId}) async {
    if (_engine != null) return;

    _callStatusController = StreamController<CallStatus>.broadcast();
    _errorController = StreamController<String>.broadcast();

    try {
      _engine = createAgoraRtcEngine();
      await _engine!.initialize(RtcEngineContext(
        appId: appId,
        channelProfile: ChannelProfileType.channelProfileCommunication,
      ));

      await _engine!.enableAudio();
      await _engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);

      // Register event handlers
      _engine!.registerEventHandler(
        RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
            print('Successfully joined channel: ${connection.channelId}');
            _callStatusController?.add(CallStatus.connected);
            _isInCall = true;
          },
          onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
            print('Remote user joined: $remoteUid');
          },
          onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
            print('Remote user left: $remoteUid, reason: $reason');
          },
          onLeaveChannel: (RtcConnection connection, RtcStats stats) {
            print('Left channel: ${connection.channelId}');
            _callStatusController?.add(CallStatus.ended);
            _isInCall = false;
            _currentCallId = null;
            _currentChannelName = null;
          },
          onError: (ErrorCodeType err, String msg) {
            print('Agora error: $err, message: $msg');
            _errorController?.add('Call error: $msg');
            _callStatusController?.add(CallStatus.failed);
          },
          onConnectionStateChanged: (RtcConnection connection,
              ConnectionStateType state, ConnectionChangedReasonType reason) {
            print('Connection state changed: $state, reason: $reason');

            switch (state) {
              case ConnectionStateType.connectionStateConnecting:
                _callStatusController?.add(CallStatus.connecting);
                break;
              case ConnectionStateType.connectionStateConnected:
                _callStatusController?.add(CallStatus.connected);
                break;
              case ConnectionStateType.connectionStateFailed:
                _callStatusController?.add(CallStatus.failed);
                _errorController?.add('Failed to connect to call');
                break;
              case ConnectionStateType.connectionStateDisconnected:
                _callStatusController?.add(CallStatus.ended);
                break;
              default:
                break;
            }
          },
        ),
      );

      print('Agora RTC Engine initialized successfully');
    } catch (e) {
      print('Failed to initialize Agora RTC Engine: $e');
      _errorController?.add('Failed to initialize call engine: $e');
      throw Exception('Failed to initialize call engine');
    }
  }

  /// Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }

  /// Join a voice call channel
  Future<void> joinCall({
    required String callId,
    required String channelName,
    required String token,
    required int userId,
  }) async {
    if (_engine == null) {
      throw Exception('Call engine not initialized');
    }

    if (_isInCall) {
      throw Exception('Already in a call');
    }

    // Request microphone permission
    final hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      _errorController?.add('Microphone permission denied');
      throw Exception('Microphone permission required for calls');
    }

    try {
      _currentCallId = callId;
      _currentChannelName = channelName;
      _callStatusController?.add(CallStatus.connecting);

      final options = ChannelMediaOptions(
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
        channelProfile: ChannelProfileType.channelProfileCommunication,
      );

      await _engine!.joinChannel(
        token: token,
        channelId: channelName,
        uid: userId, // Use the specific user ID that matches the token
        options: options,
      );

      print('Joining call: $callId, channel: $channelName, userId: $userId');
    } catch (e) {
      print('Failed to join call: $e');
      _errorController?.add('Failed to join call: $e');
      _callStatusController?.add(CallStatus.failed);
      _currentCallId = null;
      _currentChannelName = null;
      rethrow;
    }
  }

  /// Leave the current call
  Future<void> leaveCall() async {
    if (_engine == null || !_isInCall) return;

    try {
      await _engine!.leaveChannel();
      print('Left call: $_currentCallId');
    } catch (e) {
      print('Error leaving call: $e');
      _errorController?.add('Error ending call: $e');
    }
  }

  /// Mute/unmute microphone
  Future<void> toggleMute() async {
    if (_engine == null) return;

    try {
      // Get current mute state and toggle it
      await _engine!.muteLocalAudioStream(false); // This is a simplified implementation
      print('Toggled microphone mute');
    } catch (e) {
      print('Error toggling mute: $e');
      _errorController?.add('Error toggling mute: $e');
    }
  }

  /// Dispose of resources
  Future<void> dispose() async {
    await leaveCall();
    await _engine?.release();
    _engine = null;

    await _callStatusController?.close();
    await _errorController?.close();
    _callStatusController = null;
    _errorController = null;

    _instance = null;
  }
}
