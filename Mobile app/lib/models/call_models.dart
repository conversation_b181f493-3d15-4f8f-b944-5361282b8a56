// Models for API communication with the backend

class CustomerInfo {
  final String name;
  final String email;
  final String phone;
  final String? accountId;
  final Map<String, String>? preferences;

  CustomerInfo({
    required this.name,
    required this.email,
    required this.phone,
    this.accountId,
    this.preferences,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      if (accountId != null) 'account_id': accountId,
      if (preferences != null) 'preferences': preferences,
    };
  }

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      accountId: json['account_id'],
      preferences: json['preferences'] != null
          ? Map<String, String>.from(json['preferences'])
          : null,
    );
  }
}

class InitiateCustomerCallRequest {
  final int userId;
  final String purpose;
  final CustomerInfo customerInfo;
  final String? aiPersonality;
  final String? language;
  final String? scenario;
  final Map<String, String>? customFields;

  InitiateCustomerCallRequest({
    required this.userId,
    required this.purpose,
    required this.customerInfo,
    this.aiPersonality,
    this.language,
    this.scenario,
    this.customFields,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'purpose': purpose,
      'customer_info': customerInfo.toJson(),
      if (aiPersonality != null) 'ai_personality': aiPersonality,
      if (language != null) 'language': language,
      if (scenario != null) 'scenario': scenario,
      if (customFields != null) 'custom_fields': customFields,
    };
  }
}

class AIInfo {
  final String name;
  final String personality;
  final String language;
  final List<String> capabilities;

  AIInfo({
    required this.name,
    required this.personality,
    required this.language,
    required this.capabilities,
  });

  factory AIInfo.fromJson(Map<String, dynamic> json) {
    return AIInfo(
      name: json['name'] ?? '',
      personality: json['personality'] ?? '',
      language: json['language'] ?? '',
      capabilities: List<String>.from(json['capabilities'] ?? []),
    );
  }
}

class InitiateCustomerCallResponse {
  final String callId;
  final String channelName;
  final String token;
  final int userId;
  final String status;
  final String message;
  final AIInfo aiInfo;

  InitiateCustomerCallResponse({
    required this.callId,
    required this.channelName,
    required this.token,
    required this.userId,
    required this.status,
    required this.message,
    required this.aiInfo,
  });

  factory InitiateCustomerCallResponse.fromJson(Map<String, dynamic> json) {
    return InitiateCustomerCallResponse(
      callId: json['call_id'] ?? '',
      channelName: json['channel_name'] ?? '',
      token: json['token'] ?? '',
      userId: json['user_id'] ?? 0,
      status: json['status'] ?? '',
      message: json['message'] ?? '',
      aiInfo: AIInfo.fromJson(json['ai_info'] ?? {}),
    );
  }
}

class ApiError {
  final String error;
  final String? details;

  ApiError({
    required this.error,
    this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      error: json['error'] ?? 'Unknown error',
      details: json['details'],
    );
  }

  @override
  String toString() {
    return details != null ? '$error: $details' : error;
  }
}

enum CallStatus {
  idle,
  connecting,
  connected,
  ended,
  failed,
}
