import 'package:flutter/material.dart';
import 'dart:async';
import '../models/call_models.dart';
import '../services/api_service.dart';
import '../services/call_service.dart';

class CallScreen extends StatefulWidget {
  const CallScreen({super.key});

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> {
  final ApiService _apiService = ApiService();
  final CallService _callService = CallService.instance;

  // Form controllers
  final _nameController = TextEditingController(text: '<PERSON>');
  final _emailController = TextEditingController(text: '<EMAIL>');
  final _phoneController = TextEditingController(text: '+1234567890');
  final _purposeController = TextEditingController(text: 'General support');

  // State variables
  CallStatus _callStatus = CallStatus.idle;
  String? _errorMessage;
  String? _currentCallId;
  bool _isLoading = false;
  StreamSubscription<CallStatus>? _callStatusSubscription;
  StreamSubscription<String>? _errorSubscription;

  @override
  void initState() {
    super.initState();
    _setupCallListeners();
    _checkServerConnection();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _purposeController.dispose();
    _callStatusSubscription?.cancel();
    _errorSubscription?.cancel();
    super.dispose();
  }

  void _setupCallListeners() {
    _callStatusSubscription = _callService.callStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _callStatus = status;
          if (status == CallStatus.ended || status == CallStatus.failed) {
            _currentCallId = null;
            _isLoading = false;
          }
        });
      }
    });

    _errorSubscription = _callService.errorStream.listen((error) {
      if (mounted) {
        setState(() {
          _errorMessage = error;
          _isLoading = false;
        });
        _showErrorSnackBar(error);
      }
    });
  }

  Future<void> _checkServerConnection() async {
    final isHealthy = await _apiService.checkServerHealth();
    if (!isHealthy && mounted) {
      setState(() {
        _errorMessage = 'Cannot connect to server. Please check if the backend is running.';
      });
    }
  }

  Future<void> _startCall() async {
    if (_isLoading || _callStatus == CallStatus.connecting || _callStatus == CallStatus.connected) {
      return;
    }

    // Validate form
    if (_nameController.text.trim().isEmpty ||
        _emailController.text.trim().isEmpty ||
        _phoneController.text.trim().isEmpty ||
        _purposeController.text.trim().isEmpty) {
      _showErrorSnackBar('Please fill in all fields');
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Create customer info
      final customerInfo = CustomerInfo(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
      );

      // Generate a unique integer user ID (using timestamp)
      final userId = DateTime.now().millisecondsSinceEpoch % 2147483647; // Keep within int32 range

      // Create call request
      final request = InitiateCustomerCallRequest(
        userId: userId,
        purpose: _purposeController.text.trim(),
        customerInfo: customerInfo,
        aiPersonality: 'helpful',
        language: 'en',
        scenario: 'general',
      );

      // Initiate call with backend
      final response = await _apiService.initiateCustomerCall(request);

      setState(() {
        _currentCallId = response.callId;
      });

      // Join the Agora voice channel using the same user ID
      await _callService.joinCall(
        callId: response.callId,
        channelName: response.channelName,
        token: response.token,
        userId: response.userId,
      );

      _showSuccessSnackBar('Connected to AI Support: ${response.aiInfo.name}');

    } catch (e) {
      print("Error starting call: ${e}");
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      _showErrorSnackBar(e.toString());
    }
  }

  Future<void> _endCall() async {
    if (_currentCallId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Leave the Agora channel
      await _callService.leaveCall();

      // End call on backend
      await _apiService.endCall(_currentCallId!);

      setState(() {
        _currentCallId = null;
        _callStatus = CallStatus.ended;
        _isLoading = false;
      });

      _showSuccessSnackBar('Call ended successfully');

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Error ending call: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _getCallStatusText() {
    switch (_callStatus) {
      case CallStatus.idle:
        return 'Ready to call';
      case CallStatus.connecting:
        return 'Connecting...';
      case CallStatus.connected:
        return 'Connected to AI Support';
      case CallStatus.ended:
        return 'Call ended';
      case CallStatus.failed:
        return 'Call failed';
    }
  }

  Color _getCallStatusColor() {
    switch (_callStatus) {
      case CallStatus.idle:
        return Colors.grey;
      case CallStatus.connecting:
        return Colors.orange;
      case CallStatus.connected:
        return Colors.green;
      case CallStatus.ended:
        return Colors.grey;
      case CallStatus.failed:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Call Support'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status indicator
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getCallStatusColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _getCallStatusColor()),
              ),
              child: Row(
                children: [
                  Icon(
                    _callStatus == CallStatus.connected ? Icons.phone : Icons.phone_disabled,
                    color: _getCallStatusColor(),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getCallStatusText(),
                    style: TextStyle(
                      color: _getCallStatusColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Form fields
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    TextField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Your Name',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                      enabled: _callStatus == CallStatus.idle,
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      enabled: _callStatus == CallStatus.idle,
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'Phone Number',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.phone),
                      ),
                      keyboardType: TextInputType.phone,
                      enabled: _callStatus == CallStatus.idle,
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _purposeController,
                      decoration: const InputDecoration(
                        labelText: 'What can we help you with?',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.help_outline),
                      ),
                      maxLines: 2,
                      enabled: _callStatus == CallStatus.idle,
                    ),

                    if (_errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Call button
            SizedBox(
              height: 56,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : (_callStatus == CallStatus.connected ? _endCall : _startCall),
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Icon(_callStatus == CallStatus.connected ? Icons.call_end : Icons.call),
                label: Text(
                  _isLoading
                      ? 'Please wait...'
                      : (_callStatus == CallStatus.connected ? 'End Call' : 'Call AI Support'),
                  style: const TextStyle(fontSize: 18),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _callStatus == CallStatus.connected ? Colors.red : Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
