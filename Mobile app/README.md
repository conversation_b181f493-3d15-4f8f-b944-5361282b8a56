# AI Call Support Mobile App

A Flutter mobile application that allows customers to initiate voice calls with an AI support assistant. The app integrates with the backend AI call system using Agora RTC for real-time voice communication.

## Features

- 🎤 **Voice Calling**: Real-time voice calls with AI support using Agora RTC
- 📱 **Cross-Platform**: Works on both iOS and Android
- 🤖 **AI Integration**: Connects to the backend AI customer support system
- 🔒 **Permission Handling**: Automatic microphone permission requests
- 📋 **Customer Info**: Collects customer details for personalized support
- 🔄 **Real-time Status**: Live call status updates and error handling

## Prerequisites

1. **Flutter SDK**: Install Flutter (3.5.4 or later)
2. **Backend Server**: The AI call backend must be running
3. **Agora Account**: You need an Agora App ID (provided by the backend configuration)

## Setup Instructions

### 1. Install Dependencies

```bash
cd "Mobile app"
flutter pub get
```

### 2. Configure the App

1. **Get Agora App ID**:
   - Open `Backend/config/.env` file
   - Copy the `AGORA_APP_ID` value

2. **Update Configuration**:
   - Open `lib/config/app_config.dart`
   - Replace `'your_agora_app_id_here'` with your actual Agora App ID
   - Update `backendBaseUrl` if your backend runs on a different host/port

```dart
class AppConfig {
  static const String agoraAppId = 'your_actual_agora_app_id';
  static const String backendBaseUrl = 'http://localhost:8080';
}
```

### 3. Platform-Specific Setup

#### For Android Emulator:
- Update `backendBaseUrl` to `'http://********:8080'`

#### For Physical Devices:
- Replace `localhost` with your computer's IP address
- Example: `'http://*************:8080'`

### 4. Run the App

```bash
# For Android
flutter run

# For iOS
flutter run -d ios

# For specific device
flutter devices
flutter run -d <device_id>
```

## Usage

1. **Start the Backend**: Make sure the AI call backend is running on the configured URL
2. **Fill Customer Info**: Enter your name, email, phone, and support request
3. **Call AI Support**: Tap the "Call AI Support" button to start a voice call
4. **Talk to AI**: Speak naturally with the AI assistant
5. **End Call**: Tap "End Call" when finished

## App Structure

```
lib/
├── config/
│   └── app_config.dart          # App configuration
├── models/
│   └── call_models.dart         # Data models for API communication
├── screens/
│   └── call_screen.dart         # Main call interface
├── services/
│   ├── api_service.dart         # Backend API communication
│   └── call_service.dart        # Agora RTC integration
└── main.dart                    # App entry point
```

## API Integration

The app communicates with the backend using these endpoints:

- `POST /api/calls/customer-initiate` - Start a new customer-to-AI call
- `POST /api/calls/:call_id/end` - End an active call
- `GET /health` - Check backend server health

## Permissions

The app requires the following permissions:

### Android (`android/app/src/main/AndroidManifest.xml`):
- `INTERNET` - Network communication
- `RECORD_AUDIO` - Microphone access for voice calls
- `MODIFY_AUDIO_SETTINGS` - Audio configuration
- `ACCESS_NETWORK_STATE` - Network status

### iOS (`ios/Runner/Info.plist`):
- `NSMicrophoneUsageDescription` - Microphone access explanation

## Troubleshooting

### Common Issues:

1. **"Cannot connect to server"**:
   - Ensure the backend is running
   - Check the `backendBaseUrl` configuration
   - For physical devices, use your computer's IP address

2. **"Call engine not initialized"**:
   - Verify the Agora App ID is correctly configured
   - Check that the App ID matches the backend configuration

3. **"Microphone permission denied"**:
   - Grant microphone permission in device settings
   - Restart the app after granting permission

4. **"Failed to join call"**:
   - Check internet connection
   - Verify the backend is generating valid Agora tokens
   - Ensure Agora App ID and certificate match

### Debug Mode:

Enable debug logging by checking the console output for detailed error messages and call status updates.

## Development

### Adding New Features:

1. **New API Endpoints**: Add methods to `ApiService`
2. **UI Changes**: Modify `CallScreen` widget
3. **Call Features**: Extend `CallService` for additional Agora features
4. **Data Models**: Update `call_models.dart` for new API structures

### Testing:

```bash
# Run tests
flutter test

# Run on specific platform
flutter test --platform android
flutter test --platform ios
```

## Dependencies

- `flutter`: Flutter SDK
- `http`: HTTP client for API calls
- `agora_rtc_engine`: Agora RTC SDK for voice calling
- `permission_handler`: Runtime permission management

## Support

For issues related to:
- **Backend Integration**: Check the Backend README
- **Agora Configuration**: Verify App ID and certificate
- **Flutter Development**: See [Flutter documentation](https://docs.flutter.dev/)

## License

This project is part of the AI Customer Support system.
